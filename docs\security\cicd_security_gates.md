# 🛡️ CI/CD Security Gates Design

> **DevSecOps Security Integration for ImpactCV Development Pipeline**  
> **Standards:** NIST SSDF | OWASP DevSecOps | CIS Controls

---

## 📋 **EXECUTIVE SUMMARY**

### **Security Gates Overview**
ImpactCV implements comprehensive security gates throughout the CI/CD pipeline to ensure that security vulnerabilities are detected and remediated before code reaches production. The security-first approach integrates SAST, DAST, dependency scanning, and compliance validation at every stage.

### **Security Gate Principles**
1. **Shift Left Security** - Integrate security early in development lifecycle
2. **Automated Security Testing** - Continuous security validation without manual intervention
3. **Fail Fast** - Block deployments when critical security issues are detected
4. **Comprehensive Coverage** - Multiple security testing tools for complete coverage

---

## 🔄 **CI/CD PIPELINE WITH SECURITY GATES**

### **Pipeline Overview**

```mermaid
graph TD
    A[Developer Commit] --> B[Pre-commit Hooks]
    B --> C[Code Quality Gate]
    C --> D[SAST Security Gate]
    D --> E[Dependency Security Gate]
    E --> F[Build & Test Gate]
    F --> G[Container Security Gate]
    G --> H[DAST Security Gate]
    H --> I[Compliance Gate]
    I --> J[Deployment Gate]
    J --> K[Runtime Security Gate]
    
    B -.-> FAIL1[❌ Block Commit]
    C -.-> FAIL2[❌ Block PR]
    D -.-> FAIL3[❌ Block Build]
    E -.-> FAIL4[❌ Block Build]
    F -.-> FAIL5[❌ Block Deploy]
    G -.-> FAIL6[❌ Block Deploy]
    H -.-> FAIL7[❌ Block Deploy]
    I -.-> FAIL8[❌ Block Deploy]
    J -.-> FAIL9[❌ Block Release]
    K -.-> FAIL10[❌ Alert & Monitor]
```

### **Security Gate Definitions**

| Gate | Stage | Tools | Blocking Criteria | SLA |
|------|-------|-------|-------------------|-----|
| **Pre-commit** | Local Dev | pre-commit, secrets scanner | Secrets detected | <30s |
| **Code Quality** | PR Creation | SonarQube, CodeQL | Critical issues | <5min |
| **SAST** | Build | Bandit, Semgrep | High/Critical vulnerabilities | <10min |
| **Dependency** | Build | Safety, Snyk | Known CVEs | <5min |
| **Container** | Package | Trivy, Clair | Critical container vulns | <5min |
| **DAST** | Staging | OWASP ZAP | High/Critical web vulns | <30min |
| **Compliance** | Pre-deploy | Custom scripts | GDPR/OWASP violations | <10min |
| **Runtime** | Production | Falco, SIEM | Real-time threats | <1min |

---

## 🔧 **GITHUB ACTIONS IMPLEMENTATION**

### **Main Security Pipeline**

```yaml
# .github/workflows/security-pipeline.yml
name: Security Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  # ============================================================================
  # SECURITY GATE 1: CODE QUALITY & SECRETS SCANNING
  # ============================================================================
  code-quality:
    name: Code Quality Gate
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for SonarQube
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
      
      - name: Secrets Scanning
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified
      
      - name: Code Quality Analysis
        uses: sonarqube-quality-gate-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: target/sonar/report-task.txt
      
      - name: Quality Gate Check
        run: |
          if [ "${{ steps.sonarqube.outputs.quality-gate-status }}" != "PASSED" ]; then
            echo "❌ Quality gate failed"
            exit 1
          fi

  # ============================================================================
  # SECURITY GATE 2: STATIC APPLICATION SECURITY TESTING (SAST)
  # ============================================================================
  sast-security:
    name: SAST Security Gate
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install security tools
        run: |
          pip install bandit[toml] safety semgrep
      
      - name: Run Bandit SAST
        run: |
          bandit -r . -f json -o bandit-report.json
          bandit -r . -f txt
        continue-on-error: true
      
      - name: Run Semgrep SAST
        run: |
          semgrep --config=auto --json --output=semgrep-report.json .
          semgrep --config=auto .
        continue-on-error: true
      
      - name: CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          languages: python
          queries: security-and-quality
      
      - name: Security Gate Evaluation
        run: |
          python scripts/security_gate_evaluator.py \
            --bandit-report bandit-report.json \
            --semgrep-report semgrep-report.json \
            --max-high 0 \
            --max-medium 5
      
      - name: Upload SAST Results
        uses: actions/upload-artifact@v3
        with:
          name: sast-reports
          path: |
            bandit-report.json
            semgrep-report.json

  # ============================================================================
  # SECURITY GATE 3: DEPENDENCY VULNERABILITY SCANNING
  # ============================================================================
  dependency-security:
    name: Dependency Security Gate
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install dependencies
        run: |
          pip install safety pip-audit
      
      - name: Safety Check
        run: |
          safety check --json --output safety-report.json
          safety check
        continue-on-error: true
      
      - name: Pip Audit
        run: |
          pip-audit --format=json --output=pip-audit-report.json
          pip-audit
        continue-on-error: true
      
      - name: Snyk Security Scan
        uses: snyk/actions/python@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --json-file-output=snyk-report.json
      
      - name: Dependency Gate Evaluation
        run: |
          python scripts/dependency_gate_evaluator.py \
            --safety-report safety-report.json \
            --pip-audit-report pip-audit-report.json \
            --snyk-report snyk-report.json \
            --max-critical 0 \
            --max-high 0

  # ============================================================================
  # SECURITY GATE 4: CONTAINER SECURITY SCANNING
  # ============================================================================
  container-security:
    name: Container Security Gate
    runs-on: ubuntu-latest
    needs: [sast-security, dependency-security]
    steps:
      - uses: actions/checkout@v4
      
      - name: Build Docker Images
        run: |
          docker-compose build
      
      - name: Trivy Container Scan
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'impactcv/api-gateway:latest'
          format: 'json'
          output: 'trivy-report.json'
          severity: 'CRITICAL,HIGH'
          exit-code: '1'
      
      - name: Clair Container Scan
        run: |
          docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
            -v $(pwd):/tmp/clair \
            quay.io/coreos/clair-local-scan:latest \
            --imageName=impactcv/api-gateway:latest \
            --reportPath=/tmp/clair/clair-report.json
      
      - name: Container Security Gate
        run: |
          python scripts/container_gate_evaluator.py \
            --trivy-report trivy-report.json \
            --clair-report clair-report.json \
            --max-critical 0 \
            --max-high 2

  # ============================================================================
  # SECURITY GATE 5: DYNAMIC APPLICATION SECURITY TESTING (DAST)
  # ============================================================================
  dast-security:
    name: DAST Security Gate
    runs-on: ubuntu-latest
    needs: container-security
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Start Application
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30  # Wait for services to start
      
      - name: OWASP ZAP Baseline Scan
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:8000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a -j -m 10 -T 60'
      
      - name: OWASP ZAP Full Scan
        uses: zaproxy/action-full-scan@v0.4.0
        with:
          target: 'http://localhost:8000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a -j -m 10 -T 60'
      
      - name: Nuclei Security Scan
        run: |
          docker run --rm -v $(pwd):/tmp/nuclei \
            projectdiscovery/nuclei:latest \
            -target http://localhost:8000 \
            -json-export /tmp/nuclei/nuclei-report.json
      
      - name: DAST Gate Evaluation
        run: |
          python scripts/dast_gate_evaluator.py \
            --zap-report zap-report.json \
            --nuclei-report nuclei-report.json \
            --max-high 0 \
            --max-medium 3

  # ============================================================================
  # SECURITY GATE 6: COMPLIANCE VALIDATION
  # ============================================================================
  compliance-validation:
    name: Compliance Gate
    runs-on: ubuntu-latest
    needs: dast-security
    steps:
      - uses: actions/checkout@v4
      
      - name: GDPR Compliance Check
        run: |
          python scripts/gdpr_compliance_validator.py \
            --source-code . \
            --config compliance/gdpr-config.yml \
            --output gdpr-compliance-report.json
      
      - name: OWASP Top 10 Validation
        run: |
          python scripts/owasp_top10_validator.py \
            --sast-reports sast-reports/ \
            --dast-reports dast-reports/ \
            --output owasp-compliance-report.json
      
      - name: DAMA-DMBOK Validation
        run: |
          python scripts/dama_dmbok_validator.py \
            --data-flows docs/architecture/ \
            --policies compliance/data-governance.yml \
            --output dama-compliance-report.json
      
      - name: Compliance Gate Check
        run: |
          python scripts/compliance_gate_evaluator.py \
            --gdpr-report gdpr-compliance-report.json \
            --owasp-report owasp-compliance-report.json \
            --dama-report dama-compliance-report.json \
            --min-compliance-score 95

  # ============================================================================
  # SECURITY GATE 7: DEPLOYMENT AUTHORIZATION
  # ============================================================================
  deployment-gate:
    name: Deployment Authorization Gate
    runs-on: ubuntu-latest
    needs: compliance-validation
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Security Approval Check
        run: |
          python scripts/deployment_gate_validator.py \
            --all-gates-passed true \
            --security-approval-required true \
            --approver-list "security-team,lead-developer"
      
      - name: Generate Security Summary
        run: |
          python scripts/security_summary_generator.py \
            --pipeline-run ${{ github.run_id }} \
            --output security-summary.md
      
      - name: Notify Security Team
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: "🔒 Security gates passed for deployment to production"
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

---

## 🛠️ **SECURITY GATE EVALUATORS**

### **SAST Gate Evaluator**

```python
# scripts/security_gate_evaluator.py
import json
import sys
import argparse
from typing import Dict, List

class SecurityGateEvaluator:
    def __init__(self, max_critical: int = 0, max_high: int = 0, max_medium: int = 5):
        self.max_critical = max_critical
        self.max_high = max_high
        self.max_medium = max_medium
        self.violations = []
    
    def evaluate_bandit_report(self, report_path: str) -> Dict:
        """Evaluate Bandit SAST results"""
        with open(report_path, 'r') as f:
            report = json.load(f)
        
        severity_counts = {
            'CRITICAL': 0,
            'HIGH': 0,
            'MEDIUM': 0,
            'LOW': 0
        }
        
        for result in report.get('results', []):
            severity = result.get('issue_severity', 'LOW').upper()
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        # Check against thresholds
        violations = []
        if severity_counts['CRITICAL'] > self.max_critical:
            violations.append(f"Critical vulnerabilities: {severity_counts['CRITICAL']} (max: {self.max_critical})")
        
        if severity_counts['HIGH'] > self.max_high:
            violations.append(f"High vulnerabilities: {severity_counts['HIGH']} (max: {self.max_high})")
        
        if severity_counts['MEDIUM'] > self.max_medium:
            violations.append(f"Medium vulnerabilities: {severity_counts['MEDIUM']} (max: {self.max_medium})")
        
        return {
            'tool': 'bandit',
            'severity_counts': severity_counts,
            'violations': violations,
            'passed': len(violations) == 0
        }
    
    def evaluate_semgrep_report(self, report_path: str) -> Dict:
        """Evaluate Semgrep SAST results"""
        with open(report_path, 'r') as f:
            report = json.load(f)
        
        severity_counts = {'CRITICAL': 0, 'HIGH': 0, 'MEDIUM': 0, 'LOW': 0}
        
        for result in report.get('results', []):
            severity = result.get('extra', {}).get('severity', 'LOW').upper()
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        violations = []
        if severity_counts['CRITICAL'] > self.max_critical:
            violations.append(f"Critical vulnerabilities: {severity_counts['CRITICAL']} (max: {self.max_critical})")
        
        if severity_counts['HIGH'] > self.max_high:
            violations.append(f"High vulnerabilities: {severity_counts['HIGH']} (max: {self.max_high})")
        
        return {
            'tool': 'semgrep',
            'severity_counts': severity_counts,
            'violations': violations,
            'passed': len(violations) == 0
        }
    
    def generate_gate_report(self, evaluations: List[Dict]) -> Dict:
        """Generate final security gate report"""
        all_passed = all(eval_result['passed'] for eval_result in evaluations)
        total_violations = sum(len(eval_result['violations']) for eval_result in evaluations)
        
        report = {
            'gate_status': 'PASSED' if all_passed else 'FAILED',
            'total_violations': total_violations,
            'evaluations': evaluations,
            'summary': {
                'critical_issues': sum(eval_result['severity_counts'].get('CRITICAL', 0) for eval_result in evaluations),
                'high_issues': sum(eval_result['severity_counts'].get('HIGH', 0) for eval_result in evaluations),
                'medium_issues': sum(eval_result['severity_counts'].get('MEDIUM', 0) for eval_result in evaluations)
            }
        }
        
        return report

def main():
    parser = argparse.ArgumentParser(description='Evaluate SAST security gate')
    parser.add_argument('--bandit-report', required=True, help='Path to Bandit JSON report')
    parser.add_argument('--semgrep-report', required=True, help='Path to Semgrep JSON report')
    parser.add_argument('--max-critical', type=int, default=0, help='Maximum critical vulnerabilities allowed')
    parser.add_argument('--max-high', type=int, default=0, help='Maximum high vulnerabilities allowed')
    parser.add_argument('--max-medium', type=int, default=5, help='Maximum medium vulnerabilities allowed')
    
    args = parser.parse_args()
    
    evaluator = SecurityGateEvaluator(args.max_critical, args.max_high, args.max_medium)
    
    evaluations = []
    evaluations.append(evaluator.evaluate_bandit_report(args.bandit_report))
    evaluations.append(evaluator.evaluate_semgrep_report(args.semgrep_report))
    
    report = evaluator.generate_gate_report(evaluations)
    
    # Print report
    print(json.dumps(report, indent=2))
    
    # Exit with appropriate code
    if report['gate_status'] == 'FAILED':
        print(f"\n❌ Security gate FAILED with {report['total_violations']} violations")
        sys.exit(1)
    else:
        print(f"\n✅ Security gate PASSED")
        sys.exit(0)

if __name__ == '__main__':
    main()
```

### **Pre-commit Security Hooks**

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
  
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: package.lock.json
  
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ['-c', 'pyproject.toml']
  
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.11
  
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings]
  
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

---

## 📊 **SECURITY METRICS & REPORTING**

### **Security Dashboard Metrics**

```python
# Security metrics collection
class SecurityMetrics:
    def __init__(self):
        self.security_gate_duration = Histogram("security_gate_duration_seconds", ["gate_name"])
        self.security_violations = Counter("security_violations_total", ["tool", "severity"])
        self.security_gate_status = Gauge("security_gate_status", ["gate_name"])
        
    def record_gate_duration(self, gate_name: str, duration: float):
        self.security_gate_duration.labels(gate_name=gate_name).observe(duration)
    
    def record_violation(self, tool: str, severity: str):
        self.security_violations.labels(tool=tool, severity=severity).inc()
    
    def set_gate_status(self, gate_name: str, status: int):  # 1 = passed, 0 = failed
        self.security_gate_status.labels(gate_name=gate_name).set(status)
```

### **Security Report Generation**

```python
# scripts/security_summary_generator.py
def generate_security_summary(pipeline_run_id: str) -> str:
    """Generate comprehensive security summary for deployment"""
    
    summary = f"""
# 🔒 Security Summary - Pipeline Run {pipeline_run_id}

## Security Gates Status
✅ **All security gates PASSED**

### Gate Results:
- ✅ **Code Quality Gate**: No critical issues detected
- ✅ **SAST Gate**: 0 critical, 0 high vulnerabilities
- ✅ **Dependency Gate**: No known CVEs in dependencies
- ✅ **Container Gate**: Base images secure, no critical vulnerabilities
- ✅ **DAST Gate**: No high-risk web vulnerabilities
- ✅ **Compliance Gate**: 100% GDPR, OWASP, DAMA-DMBOK compliance

### Security Tools Used:
- **SAST**: Bandit, Semgrep, CodeQL
- **Dependency**: Safety, Snyk, pip-audit
- **Container**: Trivy, Clair
- **DAST**: OWASP ZAP, Nuclei
- **Secrets**: TruffleHog, detect-secrets

### Compliance Status:
- 🔒 **OWASP Top 10**: 100% compliant
- 🛡️ **GDPR**: Privacy by design implemented
- 📊 **DAMA-DMBOK**: Data governance standards met

**Deployment Authorization**: ✅ APPROVED for production deployment
"""
    
    return summary
```

---

## ✅ **IMPLEMENTATION CHECKLIST**

### **Security Gate Implementation**
- [ ] Pre-commit hooks configured
- [ ] SAST tools integrated (Bandit, Semgrep, CodeQL)
- [ ] Dependency scanning (Safety, Snyk, pip-audit)
- [ ] Container security scanning (Trivy, Clair)
- [ ] DAST tools configured (OWASP ZAP, Nuclei)
- [ ] Compliance validation scripts
- [ ] Security gate evaluators implemented

### **CI/CD Integration**
- [ ] GitHub Actions workflows configured
- [ ] Security gate failure handling
- [ ] Artifact collection and reporting
- [ ] Notification systems (Slack, email)
- [ ] Security metrics collection
- [ ] Dashboard integration

### **Monitoring & Alerting**
- [ ] Security metrics dashboard
- [ ] Real-time security alerts
- [ ] Security gate performance monitoring
- [ ] Compliance reporting automation
- [ ] Security team notifications

---

*This comprehensive security gate system ensures that every code change is thoroughly validated for security vulnerabilities before reaching production, maintaining the highest security standards throughout the development lifecycle.*
