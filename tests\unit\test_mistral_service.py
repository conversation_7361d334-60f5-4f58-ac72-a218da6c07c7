"""
Unit tests for Mistral Service
Tests the core Mistral 7B integration functionality
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock
import aiohttp
from aiohttp import ClientSession, ClientResponse

from app.services.mistral_service import MistralService, mistral_service, MistralResponse


class TestMistralService:
    """Test suite for MistralService"""
    
    @pytest.fixture
    def mistral_service_instance(self):
        """Create MistralService instance for testing"""
        return MistralService()
    
    @pytest.fixture
    def mock_mistral_response(self):
        """Mock Mistral API response"""
        return {
            "model": "mistral:7b",
            "created_at": "2024-12-04T22:00:00Z",
            "response": "This is a test response from Mistral",
            "done": True,
            "context": [1, 2, 3],
            "total_duration": **********,
            "load_duration": 500000000,
            "prompt_eval_count": 10,
            "prompt_eval_duration": 200000000,
            "eval_count": 20,
            "eval_duration": 300000000
        }
    
    @pytest.fixture
    def mock_json_response(self):
        """Mock JSON response from Mistral"""
        return {
            "tarea": "Optimizar sistema de base de datos",
            "cuantificacion": "Reducir tiempo de consulta en 40%",
            "resultado": "Mejorar rendimiento general del sistema"
        }

    @pytest.mark.asyncio
    async def test_mistral_service_initialization(self, mistral_service_instance):
        """Test MistralService initialization"""
        assert mistral_service_instance.base_url is not None
        assert mistral_service_instance.model_name is not None
        assert mistral_service_instance.timeout is not None
        assert mistral_service_instance.max_retries == 3
        assert mistral_service_instance.settings is not None

    @pytest.mark.asyncio
    async def test_make_request_success(self, mistral_service_instance, mock_mistral_response):
        """Test successful API request to Mistral"""
        with patch('aiohttp.ClientSession.post') as mock_post:
            # Setup mock response
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_mistral_response
            mock_post.return_value.__aenter__.return_value = mock_response
            
            # Test request
            result = await mistral_service_instance._make_request(
                "api/generate",
                {"model": "mistral:7b", "prompt": "Test prompt"}
            )
            
            assert result == mock_mistral_response
            mock_post.assert_called_once()

    @pytest.mark.asyncio
    async def test_make_request_http_error(self, mistral_service_instance):
        """Test HTTP error handling in API request"""
        with patch('aiohttp.ClientSession.post') as mock_post:
            # Setup mock error response
            mock_response = AsyncMock()
            mock_response.status = 500
            mock_response.text.return_value = "Internal Server Error"
            mock_post.return_value.__aenter__.return_value = mock_response
            
            # Test request should raise exception
            with pytest.raises(Exception) as exc_info:
                await mistral_service_instance._make_request(
                    "api/generate",
                    {"model": "mistral:7b", "prompt": "Test prompt"}
                )
            
            assert "Mistral API error 500" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_completion_success(self, mistral_service_instance, mock_mistral_response):
        """Test successful text completion generation"""
        with patch.object(mistral_service_instance, '_make_request', return_value=mock_mistral_response):
            result = await mistral_service_instance.generate_completion(
                prompt="Test prompt",
                system_prompt="You are a helpful assistant",
                temperature=0.7,
                max_tokens=100
            )
            
            assert result == "This is a test response from Mistral"

    @pytest.mark.asyncio
    async def test_generate_completion_with_parameters(self, mistral_service_instance, mock_mistral_response):
        """Test completion generation with various parameters"""
        with patch.object(mistral_service_instance, '_make_request', return_value=mock_mistral_response) as mock_request:
            await mistral_service_instance.generate_completion(
                prompt="Test prompt",
                temperature=0.5,
                max_tokens=200,
                top_p=0.9,
                top_k=40
            )
            
            # Verify request was called with correct parameters
            call_args = mock_request.call_args
            request_data = call_args[0][1]
            
            assert request_data["options"]["temperature"] == 0.5
            assert request_data["options"]["num_predict"] == 200
            assert request_data["options"]["top_p"] == 0.9
            assert request_data["options"]["top_k"] == 40

    @pytest.mark.asyncio
    async def test_generate_json_completion_success(self, mistral_service_instance, mock_json_response):
        """Test successful JSON completion generation"""
        # Mock the response to return valid JSON
        json_response_text = json.dumps(mock_json_response)
        mock_mistral_response = {
            "model": "mistral:7b",
            "created_at": "2024-12-04T22:00:00Z",
            "response": json_response_text,
            "done": True
        }
        
        with patch.object(mistral_service_instance, '_make_request', return_value=mock_mistral_response):
            result = await mistral_service_instance.generate_json_completion(
                prompt="Generate TQR for achievement",
                system_prompt="You are an expert CV writer",
                schema_example={"tarea": "task", "cuantificacion": "quantification", "resultado": "result"}
            )
            
            assert result == mock_json_response
            assert "tarea" in result
            assert "cuantificacion" in result
            assert "resultado" in result

    @pytest.mark.asyncio
    async def test_generate_json_completion_invalid_json(self, mistral_service_instance):
        """Test JSON completion with invalid JSON response"""
        # Mock response with invalid JSON
        mock_mistral_response = {
            "model": "mistral:7b",
            "created_at": "2024-12-04T22:00:00Z",
            "response": "This is not valid JSON",
            "done": True
        }
        
        with patch.object(mistral_service_instance, '_make_request', return_value=mock_mistral_response):
            with pytest.raises(Exception) as exc_info:
                await mistral_service_instance.generate_json_completion(
                    prompt="Generate JSON",
                    system_prompt="Return JSON only"
                )
            
            assert "JSON generation failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_build_prompt_with_system(self, mistral_service_instance):
        """Test prompt building with system prompt"""
        result = mistral_service_instance._build_prompt(
            "User prompt",
            "System prompt"
        )
        
        expected = "<s>[INST] System prompt\n\nUser prompt [/INST]"
        assert result == expected

    @pytest.mark.asyncio
    async def test_build_prompt_without_system(self, mistral_service_instance):
        """Test prompt building without system prompt"""
        result = mistral_service_instance._build_prompt("User prompt")
        
        expected = "<s>[INST] User prompt [/INST]"
        assert result == expected

    @pytest.mark.asyncio
    async def test_build_json_prompt(self, mistral_service_instance):
        """Test JSON prompt building"""
        schema_example = {"field1": "value1", "field2": "value2"}
        
        result = mistral_service_instance._build_json_prompt(
            "Generate data",
            "You are helpful",
            schema_example
        )
        
        assert "JSON válido únicamente" in result
        assert "Generate data" in result
        assert json.dumps(schema_example, indent=2, ensure_ascii=False) in result

    @pytest.mark.asyncio
    async def test_extract_json_simple(self, mistral_service_instance):
        """Test JSON extraction from simple response"""
        json_data = {"test": "value", "number": 42}
        response = json.dumps(json_data)
        
        result = mistral_service_instance._extract_json(response)
        assert result == json_data

    @pytest.mark.asyncio
    async def test_extract_json_with_markdown(self, mistral_service_instance):
        """Test JSON extraction from markdown-formatted response"""
        json_data = {"test": "value", "number": 42}
        response = f"```json\n{json.dumps(json_data)}\n```"
        
        result = mistral_service_instance._extract_json(response)
        assert result == json_data

    @pytest.mark.asyncio
    async def test_extract_json_with_text(self, mistral_service_instance):
        """Test JSON extraction from response with surrounding text"""
        json_data = {"test": "value", "number": 42}
        response = f"Here is the JSON:\n{json.dumps(json_data)}\nThat's it!"
        
        result = mistral_service_instance._extract_json(response)
        assert result == json_data

    @pytest.mark.asyncio
    async def test_health_check_success(self, mistral_service_instance):
        """Test successful health check"""
        mock_response = {
            "model": "mistral:7b",
            "created_at": "2024-12-04T22:00:00Z",
            "response": "OK",
            "done": True
        }
        
        with patch.object(mistral_service_instance, '_make_request', return_value=mock_response):
            result = await mistral_service_instance.health_check()
            
            assert result["status"] == "healthy"
            assert result["model"] == "mistral:7b"
            assert "test_response" in result

    @pytest.mark.asyncio
    async def test_health_check_failure(self, mistral_service_instance):
        """Test health check failure"""
        with patch.object(mistral_service_instance, '_make_request', side_effect=Exception("Connection failed")):
            result = await mistral_service_instance.health_check()
            
            assert result["status"] == "unhealthy"
            assert "error" in result
            assert "Connection failed" in result["error"]

    def test_mistral_response_model(self):
        """Test MistralResponse Pydantic model"""
        response_data = {
            "model": "mistral:7b",
            "created_at": "2024-12-04T22:00:00Z",
            "response": "Test response",
            "done": True,
            "total_duration": **********,
            "load_duration": 500000000,
            "prompt_eval_count": 10,
            "eval_count": 20
        }
        
        response = MistralResponse(**response_data)
        
        assert response.model == "mistral:7b"
        assert response.response == "Test response"
        assert response.done is True
        assert response.total_duration == **********

    @pytest.mark.asyncio
    async def test_global_mistral_service_instance(self):
        """Test global mistral_service instance"""
        assert mistral_service is not None
        assert isinstance(mistral_service, MistralService)
        assert mistral_service.model_name == "mistral:7b"

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, mistral_service_instance, mock_mistral_response):
        """Test handling of concurrent requests"""
        with patch.object(mistral_service_instance, '_make_request', return_value=mock_mistral_response):
            # Create multiple concurrent requests
            tasks = [
                mistral_service_instance.generate_completion(f"Prompt {i}")
                for i in range(3)
            ]
            
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 3
            for result in results:
                assert result == "This is a test response from Mistral"

    @pytest.mark.asyncio
    async def test_timeout_handling(self, mistral_service_instance):
        """Test timeout handling"""
        with patch('aiohttp.ClientSession.post') as mock_post:
            # Simulate timeout
            mock_post.side_effect = asyncio.TimeoutError("Request timed out")
            
            with pytest.raises(Exception) as exc_info:
                await mistral_service_instance._make_request(
                    "api/generate",
                    {"model": "mistral:7b", "prompt": "Test prompt"}
                )
            
            assert "timed out" in str(exc_info.value).lower() or "timeout" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_service_attributes(self, mistral_service_instance):
        """Test service attributes and configuration"""
        # Verify service has required attributes
        assert hasattr(mistral_service_instance, 'settings')
        assert hasattr(mistral_service_instance, 'base_url')
        assert hasattr(mistral_service_instance, 'model_name')
        assert hasattr(mistral_service_instance, 'timeout')
        assert hasattr(mistral_service_instance, 'max_retries')

        # Verify configuration values
        assert mistral_service_instance.base_url is not None
        assert mistral_service_instance.model_name is not None
        assert mistral_service_instance.timeout > 0
        assert mistral_service_instance.max_retries > 0

        # Verify service can be used for requests
        assert callable(mistral_service_instance._make_request)
        assert callable(mistral_service_instance.generate_completion)
        assert callable(mistral_service_instance.generate_json_completion)
        assert callable(mistral_service_instance.health_check)
