"""
Unit tests for AI Service
Tests the unified AI service that orchestrates different providers
"""

import pytest
import asyncio
import time
import aiohttp
from unittest.mock import Mock, patch, AsyncMock
import os
import logging

from app.services.ai_service import AIService, ai_service, MistralProvider, OpenAIProvider
from app.core.config import get_settings


class TestAIService:
    """Test suite for AIService"""

    @pytest.fixture(autouse=True)
    def reset_provider(self, ai_service_instance):
        """Reset provider before and after each test"""
        ai_service_instance._provider = None
        yield
        ai_service_instance._provider = None

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for all tests"""
        with patch('app.core.config.get_settings') as mock:
            mock.return_value.AI_PROVIDER = "mistral"
            mock.return_value.AI_TIMEOUT = 30
            mock.return_value.AI_MAX_RETRIES = 3
            yield mock

    @pytest.fixture
    def ai_service_instance(self):
        """Create AIService instance for testing"""
        return AIService()

    @pytest.fixture
    def mock_mistral_provider(self):
        """Mock MistralProvider"""
        mock = AsyncMock()
        mock.generate_completion.return_value = "Mock completion response"
        mock.generate_json_completion.return_value = {
            "tarea": "Mock task",
            "cuantificacion": "Mock quantification",
            "resultado": "Mock result"
        }
        mock.health_check.return_value = {"status": "healthy", "model": "mistral:7b"}
        return mock

    @pytest.fixture
    def mock_openai_provider(self):
        """Mock OpenAI provider"""
        mock = AsyncMock()
        mock.generate_completion.return_value = "Mock OpenAI response"
        mock.generate_json_completion.return_value = {
            "tarea": "OpenAI task",
            "cuantificacion": "OpenAI quantification",
            "resultado": "OpenAI result"
        }
        mock.health_check.return_value = {"status": "healthy", "model": "gpt-4"}
        return mock

    @pytest.fixture
    def real_world_tqr_data(self):
        """Real-world TQR request data"""
        return {
            "name": "Ana García",
            "position": "Senior Software Engineer",
            "experience_years": 7,
            "achievement_description": "Lideré la migración de una aplicación monolítica a microservicios, reduciendo el tiempo de deployment de 2 horas a 15 minutos y mejorando la disponibilidad del sistema al 99.9%"
        }

    @pytest.mark.asyncio
    async def test_ai_service_initialization(self, ai_service_instance):
        """Test AIService initialization"""
        assert ai_service_instance is not None
        assert hasattr(ai_service_instance, 'settings')
        assert hasattr(ai_service_instance, 'provider')
        # Provider is initialized automatically, not lazy loaded
        assert ai_service_instance._provider is not None

    @pytest.mark.asyncio
    async def test_provider_property_mistral(self, ai_service_instance, mock_settings):
        """Test provider property returns Mistral when configured"""
        mock_settings.return_value.AI_PROVIDER = "mistral"
        
        # Reset provider to test lazy loading
        ai_service_instance._provider = None
        
        provider = await ai_service_instance.provider
        
        # Should return mistral provider
        assert provider is not None
        assert ai_service_instance._provider is not None

    @pytest.mark.asyncio
    async def test_provider_property_openai(self, ai_service_instance, mock_settings):
        """Test provider property returns OpenAI when configured"""
        mock_settings.return_value.AI_PROVIDER = "openai"
        
        # Reset provider to test lazy loading
        ai_service_instance._provider = None
        
        provider = await ai_service_instance.provider
        
        # Should return openai provider
        assert provider is not None
        assert ai_service_instance._provider is not None

    @pytest.mark.asyncio
    async def test_generate_completion_success(self, ai_service_instance, mock_mistral_provider):
        """Test successful completion generation"""
        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        result = await ai_service_instance.generate_completion(
            prompt="Test prompt",
            system_prompt="You are helpful",
            temperature=0.7
        )

        assert result == "Mock completion response"
        mock_mistral_provider.generate_completion.assert_called_once_with(
            prompt="Test prompt",
            system_prompt="You are helpful",
            temperature=0.7,
            max_tokens=500
        )

    @pytest.mark.asyncio
    async def test_generate_json_completion_success(self, ai_service_instance, mock_mistral_provider):
        """Test successful JSON completion generation"""
        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        result = await ai_service_instance.generate_json_completion(
            prompt="Generate JSON",
            system_prompt="Return JSON only",
            schema_example={"field": "value"},
            temperature=0.3
        )

        expected = {
            "tarea": "Mock task",
            "cuantificacion": "Mock quantification",
            "resultado": "Mock result"
        }
        assert result == expected
        mock_mistral_provider.generate_json_completion.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_tqr_achievement_success(self, ai_service_instance, mock_mistral_provider, real_world_tqr_data):
        """Test successful TQR achievement generation with real-world data"""
        expected_tqr_response = {
            "tarea": "Lideré la migración completa de arquitectura monolítica a microservicios",
            "cuantificacion": "Reduciendo el tiempo de deployment de 2 horas a 15 minutos y mejorando la disponibilidad al 99.9%",
            "resultado": "Incrementando la eficiencia operativa del equipo y la confiabilidad del sistema para 10,000+ usuarios"
        }

        mock_mistral_provider.generate_json_completion.return_value = expected_tqr_response

        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        result = await ai_service_instance.generate_tqr_achievement(
            profile_data={
                "name": real_world_tqr_data["name"],
                "position": real_world_tqr_data["position"],
                "experience_years": real_world_tqr_data["experience_years"]
            },
            achievement_description=real_world_tqr_data["achievement_description"]
        )

        assert "tarea" in result
        assert "cuantificacion" in result
        assert "resultado" in result

        # Verify the content is meaningful
        assert len(result["tarea"]) > 20
        assert len(result["cuantificacion"]) > 20
        assert len(result["resultado"]) > 20

        # Verify the service was called with correct parameters
        mock_mistral_provider.generate_json_completion.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_tqr_achievement_with_parameters(self, ai_service_instance, mock_mistral_provider, real_world_tqr_data):
        """Test TQR generation with custom parameters"""
        mock_mistral_provider.generate_json_completion.return_value = {
            "tarea": "Implementé sistema de monitoreo avanzado",
            "cuantificacion": "Reduciendo incidentes en 60% y tiempo de resolución en 45%",
            "resultado": "Mejorando la estabilidad del sistema y satisfacción del cliente"
        }

        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        result = await ai_service_instance.generate_tqr_achievement(
            profile_data={
                "name": real_world_tqr_data["name"],
                "position": real_world_tqr_data["position"],
                "experience_years": real_world_tqr_data["experience_years"]
            },
            achievement_description=real_world_tqr_data["achievement_description"],
            temperature=0.5,
            max_tokens=300
        )

        assert result is not None
        assert "tarea" in result
        assert "cuantificacion" in result
        assert "resultado" in result

        # Verify parameters were passed correctly
        call_args = mock_mistral_provider.generate_json_completion.call_args
        assert call_args is not None

    @pytest.mark.asyncio
    async def test_generate_tqr_achievement_missing_fields(self, ai_service_instance, mock_mistral_provider):
        """Test TQR generation with missing required fields"""
        incomplete_profile = {
            "name": "Test User",
            # Missing position, experience_years
        }

        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        # Should handle missing fields gracefully
        try:
            result = await ai_service_instance.generate_tqr_achievement(
                profile_data=incomplete_profile,
                achievement_description="Test achievement"
            )
            # If it succeeds, should still return valid structure
            assert "tarea" in result
            assert "cuantificacion" in result
            assert "resultado" in result
        except (KeyError, ValueError, TypeError):
            # This is also acceptable behavior
            pass

    @pytest.mark.asyncio
    async def test_health_check_success(self, ai_service_instance, mock_mistral_provider, mock_settings):
        """Test successful health check"""
        mock_mistral_provider.health_check.return_value = {
            "status": "healthy",
            "provider": "mistral",
            "model": "mistral:7b"
        }

        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider
        mock_settings.return_value.AI_PROVIDER = "mistral"

        result = await ai_service_instance.health_check()

        assert result["status"] == "healthy"
        assert result["provider"] == "mistral"
        assert result["configured_provider"] == "mistral"
        mock_mistral_provider.health_check.assert_called_once()

    @pytest.mark.asyncio
    async def test_health_check_failure(self, ai_service_instance, mock_settings):
        """Test health check when provider fails"""
        mock_failing_provider = AsyncMock()
        mock_failing_provider.health_check.side_effect = Exception("Service unavailable")

        # Mock the _provider directly
        ai_service_instance._provider = mock_failing_provider
        mock_settings.return_value.AI_PROVIDER = "mistral"

        result = await ai_service_instance.health_check()

        assert result["status"] == "unhealthy"
        assert result["configured_provider"] == "mistral"
        assert "error" in result

    @pytest.mark.asyncio
    async def test_provider_default_fallback(self, ai_service_instance, mock_settings):
        """Test default fallback to Mistral when unknown provider configured"""
        mock_settings.return_value.AI_PROVIDER = "unknown_provider"
        
        # Reset provider to test lazy loading
        ai_service_instance._provider = None
        
        provider = await ai_service_instance.provider
        
        # Should fallback to Mistral provider
        assert provider is not None
        assert ai_service_instance._provider is not None
        assert isinstance(provider, MistralProvider)

    @pytest.mark.asyncio
    async def test_concurrent_tqr_generation(self, ai_service_instance, mock_mistral_provider, real_world_tqr_data):
        """Test concurrent TQR generation requests with real-world scenarios"""
        mock_mistral_provider.generate_json_completion.return_value = {
            "tarea": "Desarrollé sistema de automatización de procesos",
            "cuantificacion": "Reduciendo tiempo de procesamiento en 50% para 200+ transacciones diarias",
            "resultado": "Mejorando eficiencia operativa y reduciendo errores manuales en 80%"
        }

        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        # Create multiple concurrent requests with different scenarios
        scenarios = [
            {
                "profile_data": {"name": "Dev 1", "position": "Backend Developer", "experience_years": 3},
                "achievement_description": "Optimicé consultas de base de datos"
            },
            {
                "profile_data": {"name": "Dev 2", "position": "Frontend Developer", "experience_years": 4},
                "achievement_description": "Implementé interfaz de usuario responsive"
            },
            {
                "profile_data": {"name": "Dev 3", "position": "DevOps Engineer", "experience_years": 5},
                "achievement_description": "Automaticé pipeline de deployment"
            }
        ]

        tasks = [
            ai_service_instance.generate_tqr_achievement(**scenario)
            for scenario in scenarios
        ]

        results = await asyncio.gather(*tasks)

        assert len(results) == 3
        for result in results:
            assert "tarea" in result
            assert "cuantificacion" in result
            assert "resultado" in result
            assert len(result["tarea"]) > 10
            assert len(result["cuantificacion"]) > 10
            assert len(result["resultado"]) > 10

    @pytest.mark.asyncio
    async def test_prompt_construction(self, ai_service_instance, mock_mistral_provider, real_world_tqr_data):
        """Test that prompts are constructed correctly for TQR generation"""
        mock_mistral_provider.generate_json_completion.return_value = {
            "tarea": "Test task",
            "cuantificacion": "Test quantification",
            "resultado": "Test result"
        }

        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        await ai_service_instance.generate_tqr_achievement(
            profile_data={
                "name": real_world_tqr_data["name"],
                "position": real_world_tqr_data["position"],
                "experience_years": real_world_tqr_data["experience_years"]
            },
            achievement_description=real_world_tqr_data["achievement_description"]
        )

        # Verify the service was called
        call_args = mock_mistral_provider.generate_json_completion.call_args
        assert call_args is not None

        # Verify prompt structure (check kwargs)
        kwargs = call_args[1] if len(call_args) > 1 else {}
        assert 'prompt' in kwargs or len(call_args[0]) > 0

    @pytest.mark.asyncio
    async def test_response_validation(self, ai_service_instance, mock_mistral_provider, real_world_tqr_data):
        """Test response validation for TQR generation"""
        # Test with complete valid response
        mock_mistral_provider.generate_json_completion.return_value = {
            "tarea": "Implementé sistema de monitoreo en tiempo real",
            "cuantificacion": "Reduciendo tiempo de detección de incidentes de 30 minutos a 2 minutos",
            "resultado": "Mejorando la disponibilidad del servicio al 99.95% y satisfacción del cliente"
        }

        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        result = await ai_service_instance.generate_tqr_achievement(
            profile_data={
                "name": real_world_tqr_data["name"],
                "position": real_world_tqr_data["position"],
                "experience_years": real_world_tqr_data["experience_years"]
            },
            achievement_description=real_world_tqr_data["achievement_description"]
        )

        # Should have all required fields
        assert "tarea" in result
        assert "cuantificacion" in result
        assert "resultado" in result

        # Should have meaningful content
        assert len(result["tarea"]) > 20
        assert len(result["cuantificacion"]) > 20
        assert len(result["resultado"]) > 20

    @pytest.mark.asyncio
    async def test_global_ai_service_instance(self):
        """Test global ai_service instance"""
        assert ai_service is not None
        assert isinstance(ai_service, AIService)

    @pytest.mark.asyncio
    async def test_error_handling_and_logging(self, ai_service_instance, mock_mistral_provider, real_world_tqr_data):
        """Test error handling and logging"""
        mock_mistral_provider.generate_json_completion.side_effect = Exception("Provider connection failed")

        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        with pytest.raises(Exception) as exc_info:
            await ai_service_instance.generate_tqr_achievement(
                profile_data={
                    "name": real_world_tqr_data["name"],
                    "position": real_world_tqr_data["position"],
                    "experience_years": real_world_tqr_data["experience_years"]
                },
                achievement_description=real_world_tqr_data["achievement_description"]
            )

        # Should propagate the error appropriately
        assert "Provider connection failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_parameter_validation(self, ai_service_instance, mock_mistral_provider):
        """Test parameter validation for TQR generation"""
        mock_mistral_provider.generate_json_completion.return_value = {
            "tarea": "Valid task",
            "cuantificacion": "Valid quantification",
            "resultado": "Valid result"
        }

        # Test with various edge cases
        edge_cases = [
            {
                "profile_data": {"name": "", "position": "Dev", "experience_years": 5},
                "achievement_description": "Test achievement"
            },
            {
                "profile_data": {"name": "Test", "position": "", "experience_years": 5},
                "achievement_description": "Test achievement"
            },
            {
                "profile_data": {"name": "Test", "position": "Dev", "experience_years": 0},
                "achievement_description": "Test achievement"
            },
            {
                "profile_data": {"name": "Test", "position": "Dev", "experience_years": 5},
                "achievement_description": ""
            }
        ]

        # Mock the _provider directly
        ai_service_instance._provider = mock_mistral_provider

        for edge_case in edge_cases:
            # Should handle edge cases gracefully
            try:
                result = await ai_service_instance.generate_tqr_achievement(**edge_case)
                # If it succeeds, should return valid structure
                assert "tarea" in result
                assert "cuantificacion" in result
                assert "resultado" in result
            except (ValueError, TypeError, KeyError):
                # This is also acceptable behavior for invalid inputs
                pass

    @pytest.mark.asyncio
    async def test_provider_initialization(self, ai_service_instance, mock_settings):
        """Test provider initialization with different configurations"""
        # Test with Mistral provider
        mock_settings.return_value.AI_PROVIDER = "mistral"
        provider = await ai_service_instance.provider
        assert isinstance(provider, MistralProvider)

        # Reset provider
        ai_service_instance._provider = None

        # Test with OpenAI provider
        mock_settings.return_value.AI_PROVIDER = "openai"
        provider = await ai_service_instance.provider
        assert isinstance(provider, OpenAIProvider)

    @pytest.mark.asyncio
    async def test_error_handling(self, ai_service_instance, mock_mistral_provider):
        """Test comprehensive error handling"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test network error
        mock_mistral_provider.generate_completion.side_effect = aiohttp.ClientError()
        with pytest.raises(Exception) as exc_info:
            await ai_service_instance.generate_completion("Test prompt")
        assert "Network error" in str(exc_info.value)
        
        # Reset side effect
        mock_mistral_provider.generate_completion.side_effect = None
        
        # Test timeout error
        mock_mistral_provider.generate_completion.side_effect = asyncio.TimeoutError()
        with pytest.raises(Exception) as exc_info:
            await ai_service_instance.generate_completion("Test prompt")
        assert "Timeout" in str(exc_info.value)
        
        # Reset side effect
        mock_mistral_provider.generate_completion.side_effect = None
        
        # Test invalid response
        mock_mistral_provider.generate_completion.return_value = None
        with pytest.raises(Exception) as exc_info:
            await ai_service_instance.generate_completion("Test prompt")
        assert "Invalid response" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_input_validation(self, ai_service_instance, mock_mistral_provider):
        """Test input validation"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test empty prompt
        with pytest.raises(ValueError) as exc_info:
            await ai_service_instance.generate_completion("")
        assert "Empty prompt" in str(exc_info.value)
        
        # Test invalid temperature
        with pytest.raises(ValueError) as exc_info:
            await ai_service_instance.generate_completion(
                "Test prompt",
                temperature=2.0  # Invalid temperature
            )
        assert "Invalid temperature" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_performance_metrics(self, ai_service_instance, mock_mistral_provider):
        """Test performance metrics collection"""
        ai_service_instance._provider = mock_mistral_provider
        
        start_time = time.time()
        result = await ai_service_instance.generate_completion("Test prompt")
        end_time = time.time()
        
        # Verify response time is within acceptable range
        assert end_time - start_time < 5.0  # 5 seconds max
        
        # Verify response quality
        assert isinstance(result, str)
        assert len(result) > 0

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, ai_service_instance, mock_mistral_provider):
        """Test handling of concurrent requests"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Create multiple concurrent requests
        tasks = [
            ai_service_instance.generate_completion("Test prompt 1"),
            ai_service_instance.generate_completion("Test prompt 2"),
            ai_service_instance.generate_completion("Test prompt 3")
        ]
        
        results = await asyncio.gather(*tasks)
        assert len(results) == 3
        assert all(isinstance(r, str) for r in results)
        assert all(len(r) > 0 for r in results)

    @pytest.mark.integration
    async def test_end_to_end_flow(self, ai_service_instance):
        """Test complete flow from request to response"""
        # Test with real provider (if available)
        if os.getenv("TEST_WITH_REAL_PROVIDER"):
            result = await ai_service_instance.generate_tqr_achievement(
                profile_data={
                    "name": "Test User",
                    "position": "Software Engineer",
                    "experience_years": 5
                },
                achievement_description="Implemented CI/CD pipeline"
            )
            
            assert "tarea" in result
            assert "cuantificacion" in result
            assert "resultado" in result
            assert all(len(v) > 10 for v in result.values())
            
            # Verify response structure
            assert isinstance(result["tarea"], str)
            assert isinstance(result["cuantificacion"], str)
            assert isinstance(result["resultado"], str)
            
            # Verify response quality
            assert len(result["tarea"]) > 20
            assert len(result["cuantificacion"]) > 20
            assert len(result["resultado"]) > 20
            
            # Verify response content
            assert "CI/CD" in result["tarea"] or "pipeline" in result["tarea"]
            assert any(str(i) in result["cuantificacion"] for i in range(10))  # Should contain numbers
            assert "mejora" in result["resultado"].lower() or "impacto" in result["resultado"].lower()

    @pytest.mark.asyncio
    async def test_rate_limiting_and_retry(self, ai_service_instance, mock_mistral_provider):
        """Test rate limiting and retry logic"""
        # Configure mock to fail twice then succeed
        mock_mistral_provider.generate_completion.side_effect = [
            aiohttp.ClientError("Rate limit exceeded"),
            aiohttp.ClientError("Rate limit exceeded"),
            "Success response"
        ]
        
        ai_service_instance._provider = mock_mistral_provider
        
        result = await ai_service_instance.generate_completion("Test prompt")
        
        assert result == "Success response"
        assert mock_mistral_provider.generate_completion.call_count == 3

    @pytest.mark.asyncio
    async def test_response_format_validation(self, ai_service_instance, mock_mistral_provider):
        """Test response format validation for different scenarios"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test with malformed JSON response
        mock_mistral_provider.generate_json_completion.return_value = {
            "tarea": "Test task",
            # Missing cuantificacion
            "resultado": "Test result"
        }
        
        with pytest.raises(ValueError) as exc_info:
            await ai_service_instance.generate_tqr_achievement(
                profile_data={"name": "Test", "position": "Dev", "experience_years": 5},
                achievement_description="Test achievement"
            )
        assert "Missing required field" in str(exc_info.value)
        
        # Test with invalid field types
        mock_mistral_provider.generate_json_completion.return_value = {
            "tarea": 123,  # Should be string
            "cuantificacion": "Test quantification",
            "resultado": "Test result"
        }
        
        with pytest.raises(TypeError) as exc_info:
            await ai_service_instance.generate_tqr_achievement(
                profile_data={"name": "Test", "position": "Dev", "experience_years": 5},
                achievement_description="Test achievement"
            )
        assert "Invalid field type" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_tqr_edge_cases(self, ai_service_instance, mock_mistral_provider):
        """Test TQR generation with edge cases"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test with very long achievement description
        long_description = "Test achievement " * 100  # Very long description
        mock_mistral_provider.generate_json_completion.return_value = {
            "tarea": "Test task",
            "cuantificacion": "Test quantification",
            "resultado": "Test result"
        }
        
        result = await ai_service_instance.generate_tqr_achievement(
            profile_data={"name": "Test", "position": "Dev", "experience_years": 5},
            achievement_description=long_description
        )
        assert result is not None
        
        # Test with special characters
        special_chars = "Test achievement with special chars: !@#$%^&*()_+"
        result = await ai_service_instance.generate_tqr_achievement(
            profile_data={"name": "Test", "position": "Dev", "experience_years": 5},
            achievement_description=special_chars
        )
        assert result is not None
        
        # Test with non-ASCII characters
        non_ascii = "Test achievement with ñ and áéíóú"
        result = await ai_service_instance.generate_tqr_achievement(
            profile_data={"name": "Test", "position": "Dev", "experience_years": 5},
            achievement_description=non_ascii
        )
        assert result is not None

    @pytest.mark.asyncio
    async def test_provider_switching(self, ai_service_instance, mock_mistral_provider, mock_openai_provider, mock_settings):
        """Test switching between providers"""
        # Start with Mistral
        mock_settings.return_value.AI_PROVIDER = "mistral"
        ai_service_instance._provider = None
        provider = await ai_service_instance.provider
        assert isinstance(provider, MistralProvider)
        
        # Switch to OpenAI
        mock_settings.return_value.AI_PROVIDER = "openai"
        ai_service_instance._provider = None
        provider = await ai_service_instance.provider
        assert isinstance(provider, OpenAIProvider)
        
        # Switch back to Mistral
        mock_settings.return_value.AI_PROVIDER = "mistral"
        ai_service_instance._provider = None
        provider = await ai_service_instance.provider
        assert isinstance(provider, MistralProvider)
        
        # Test with invalid provider (should fallback to Mistral)
        mock_settings.return_value.AI_PROVIDER = "invalid_provider"
        ai_service_instance._provider = None
        provider = await ai_service_instance.provider
        assert isinstance(provider, MistralProvider)

    @pytest.mark.asyncio
    async def test_concurrent_provider_switching(self, ai_service_instance, mock_mistral_provider, mock_openai_provider, mock_settings):
        """Test concurrent requests during provider switching"""
        # Configure mocks
        mock_mistral_provider.generate_completion.return_value = "Mistral response"
        mock_openai_provider.generate_completion.return_value = "OpenAI response"
        
        # Start with Mistral
        mock_settings.return_value.AI_PROVIDER = "mistral"
        ai_service_instance._provider = mock_mistral_provider
        
        # Create concurrent requests
        tasks = [
            ai_service_instance.generate_completion("Test prompt 1"),
            ai_service_instance.generate_completion("Test prompt 2")
        ]
        
        # Switch provider during requests
        mock_settings.return_value.AI_PROVIDER = "openai"
        ai_service_instance._provider = mock_openai_provider
        
        # Complete requests
        results = await asyncio.gather(*tasks)
        
        # Verify all requests completed successfully
        assert len(results) == 2
        assert all(isinstance(r, str) for r in results)
        assert all(len(r) > 0 for r in results)

    @pytest.mark.asyncio
    async def test_memory_management(self, ai_service_instance, mock_mistral_provider):
        """Test memory management and resource cleanup"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test with large response
        large_response = "x" * (1024 * 1024)  # 1MB response
        mock_mistral_provider.generate_completion.return_value = large_response
        
        result = await ai_service_instance.generate_completion("Test prompt")
        assert len(result) == len(large_response)
        
        # Test multiple large responses
        for _ in range(10):
            result = await ai_service_instance.generate_completion("Test prompt")
            assert len(result) == len(large_response)
        
        # Verify no memory leaks (provider should handle cleanup)
        assert not hasattr(mock_mistral_provider, '_cached_responses')

    @pytest.mark.asyncio
    async def test_configuration_validation(self, ai_service_instance, mock_settings):
        """Test configuration validation"""
        # Test invalid timeout
        mock_settings.return_value.AI_TIMEOUT = -1
        with pytest.raises(ValueError) as exc_info:
            await ai_service_instance.provider
        assert "Invalid timeout" in str(exc_info.value)
        
        # Test invalid max retries
        mock_settings.return_value.AI_TIMEOUT = 30
        mock_settings.return_value.AI_MAX_RETRIES = 0
        with pytest.raises(ValueError) as exc_info:
            await ai_service_instance.provider
        assert "Invalid max retries" in str(exc_info.value)
        
        # Test missing required configuration
        mock_settings.return_value.AI_MAX_RETRIES = 3
        delattr(mock_settings.return_value, 'AI_PROVIDER')
        with pytest.raises(ValueError) as exc_info:
            await ai_service_instance.provider
        assert "Missing required configuration" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_logging_and_monitoring(self, ai_service_instance, mock_mistral_provider, caplog):
        """Test logging and monitoring"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test successful request logging
        with caplog.at_level(logging.INFO):
            await ai_service_instance.generate_completion("Test prompt")
            assert "Generating completion" in caplog.text
            assert "Completion generated successfully" in caplog.text
        
        # Test error logging
        mock_mistral_provider.generate_completion.side_effect = Exception("Test error")
        with caplog.at_level(logging.ERROR):
            with pytest.raises(Exception):
                await ai_service_instance.generate_completion("Test prompt")
            assert "Error generating completion" in caplog.text
            assert "Test error" in caplog.text
        
        # Test performance logging
        with caplog.at_level(logging.INFO):
            await ai_service_instance.generate_completion("Test prompt")
            assert "Request completed in" in caplog.text

    @pytest.mark.asyncio
    async def test_cache_behavior(self, ai_service_instance, mock_mistral_provider):
        """Test caching behavior if implemented"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test cache hit
        prompt = "Test prompt"
        mock_mistral_provider.generate_completion.return_value = "Cached response"
        
        # First call should hit the provider
        result1 = await ai_service_instance.generate_completion(prompt)
        assert result1 == "Cached response"
        assert mock_mistral_provider.generate_completion.call_count == 1
        
        # Second call with same prompt should use cache
        result2 = await ai_service_instance.generate_completion(prompt)
        assert result2 == "Cached response"
        assert mock_mistral_provider.generate_completion.call_count == 1
        
        # Different prompt should not use cache
        result3 = await ai_service_instance.generate_completion("Different prompt")
        assert result3 == "Cached response"
        assert mock_mistral_provider.generate_completion.call_count == 2
        
        # Test cache invalidation
        ai_service_instance._clear_cache()
        result4 = await ai_service_instance.generate_completion(prompt)
        assert result4 == "Cached response"
        assert mock_mistral_provider.generate_completion.call_count == 3

    @pytest.mark.asyncio
    async def test_security_measures(self, ai_service_instance, mock_mistral_provider):
        """Test security measures and input sanitization"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test SQL injection attempt
        sql_injection = "'; DROP TABLE users; --"
        result = await ai_service_instance.generate_completion(sql_injection)
        assert result is not None
        assert "DROP TABLE" not in result
        
        # Test XSS attempt
        xss_attempt = "<script>alert('xss')</script>"
        result = await ai_service_instance.generate_completion(xss_attempt)
        assert result is not None
        assert "<script>" not in result
        
        # Test command injection attempt
        cmd_injection = "test; rm -rf /"
        result = await ai_service_instance.generate_completion(cmd_injection)
        assert result is not None
        assert "rm -rf" not in result
        
        # Test large input (potential DoS)
        large_input = "x" * (1024 * 1024)  # 1MB input
        with pytest.raises(ValueError) as exc_info:
            await ai_service_instance.generate_completion(large_input)
        assert "Input too large" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_load_handling(self, ai_service_instance, mock_mistral_provider):
        """Test system behavior under load"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test with many concurrent requests
        num_requests = 100
        tasks = [
            ai_service_instance.generate_completion(f"Test prompt {i}")
            for i in range(num_requests)
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Verify all requests completed
        assert len(results) == num_requests
        assert all(not isinstance(r, Exception) for r in results)
        
        # Verify performance under load
        total_time = end_time - start_time
        assert total_time < num_requests * 0.1  # Each request should take < 100ms
        
        # Verify memory usage
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        assert memory_info.rss < 1024 * 1024 * 1024  # Less than 1GB memory usage

    @pytest.mark.asyncio
    async def test_recovery_scenarios(self, ai_service_instance, mock_mistral_provider):
        """Test system recovery from various failure scenarios"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test recovery from temporary network failure
        mock_mistral_provider.generate_completion.side_effect = [
            aiohttp.ClientError("Network error"),
            "Recovery response"
        ]
        result = await ai_service_instance.generate_completion("Test prompt")
        assert result == "Recovery response"
        
        # Test recovery from provider timeout
        mock_mistral_provider.generate_completion.side_effect = [
            asyncio.TimeoutError(),
            "Timeout recovery response"
        ]
        result = await ai_service_instance.generate_completion("Test prompt")
        assert result == "Timeout recovery response"
        
        # Test recovery from malformed response
        mock_mistral_provider.generate_completion.side_effect = [
            ValueError("Malformed response"),
            "Malformed recovery response"
        ]
        result = await ai_service_instance.generate_completion("Test prompt")
        assert result == "Malformed recovery response"
        
        # Test recovery from provider unavailability
        mock_mistral_provider.generate_completion.side_effect = [
            Exception("Provider unavailable"),
            "Provider recovery response"
        ]
        result = await ai_service_instance.generate_completion("Test prompt")
        assert result == "Provider recovery response"

    @pytest.mark.asyncio
    async def test_state_management(self, ai_service_instance, mock_mistral_provider):
        """Test state management and consistency"""
        ai_service_instance._provider = mock_mistral_provider
        
        # Test state consistency during concurrent operations
        async def concurrent_operation():
            # Simulate state changes
            await ai_service_instance.generate_completion("State test 1")
            await ai_service_instance.generate_completion("State test 2")
            return ai_service_instance._get_current_state()
        
        # Run multiple concurrent operations
        tasks = [concurrent_operation() for _ in range(5)]
        states = await asyncio.gather(*tasks)
        
        # Verify state consistency
        assert all(s == states[0] for s in states)
        
        # Test state recovery after failure
        mock_mistral_provider.generate_completion.side_effect = Exception("State error")
        try:
            await ai_service_instance.generate_completion("Test prompt")
        except Exception:
            pass
        
        # Verify state is still valid after error
        assert ai_service_instance._is_state_valid()
        
        # Test state cleanup
        await ai_service_instance._cleanup_state()
        assert not ai_service_instance._has_pending_operations()

    @pytest.mark.asyncio
    async def test_integration_with_other_services(self, ai_service_instance, mock_mistral_provider):
        """Test integration with other services"""
        # Mock other services
        mock_logging_service = AsyncMock()
        mock_metrics_service = AsyncMock()
        mock_cache_service = AsyncMock()
        
        # Inject mock services
        ai_service_instance._logging_service = mock_logging_service
        ai_service_instance._metrics_service = mock_metrics_service
        ai_service_instance._cache_service = mock_cache_service
        
        # Test logging service integration
        await ai_service_instance.generate_completion("Test prompt")
        mock_logging_service.log_request.assert_called_once()
        
        # Test metrics service integration
        mock_metrics_service.record_latency.assert_called_once()
        mock_metrics_service.record_success.assert_called_once()
        
        # Test cache service integration
        mock_cache_service.get.assert_called_once()
        mock_cache_service.set.assert_called_once()
        
        # Test error handling with dependent services
        mock_logging_service.log_request.side_effect = Exception("Logging service error")
        with pytest.raises(Exception) as exc_info:
            await ai_service_instance.generate_completion("Test prompt")
        assert "Logging service error" in str(exc_info.value)
        
        # Verify graceful degradation
        mock_metrics_service.record_latency.side_effect = Exception("Metrics service error")
        result = await ai_service_instance.generate_completion("Test prompt")
        assert result is not None  # Should still work despite metrics failure
