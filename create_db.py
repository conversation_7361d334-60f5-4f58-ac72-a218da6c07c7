#!/usr/bin/env python3
"""
Database initialization script for ImpactCV
Creates all tables and initial data
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import db_manager
from app.models.base import Base
from app.models.user import User
from app.models.document import Document, DocumentChunk, DocumentMetadata
from app.core.config import settings
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_tables():
    """Create all database tables."""
    try:
        logger.info("🔄 Creating database tables...")
        
        # Import all models to ensure they're registered
        from app.models import user, document
        
        # Initialize database manager
        db_manager.initialize()

        # Create all tables (ignore index conflicts)
        async with db_manager.async_engine.begin() as conn:
            try:
                await conn.run_sync(Base.metadata.create_all)
            except Exception as e:
                if "already exists" in str(e):
                    logger.warning(f"Some indexes already exist, continuing: {e}")
                else:
                    raise
        
        logger.info("✅ Database tables created successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating database tables: {e}")
        return False

async def create_sample_data():
    """Create sample data for testing."""
    try:
        logger.info("🔄 Creating sample data...")
        
        from app.models.user import User
        import bcrypt

        async with db_manager.get_async_session() as session:
            # Check if sample user already exists
            from sqlalchemy import select
            result = await session.execute(
                select(User).where(User.email == "<EMAIL>")
            )
            existing_user = result.scalar_one_or_none()
            
            if not existing_user:
                # Create sample admin user
                hashed_password = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt())
                sample_user = User(
                    email="<EMAIL>",
                    hashed_password=hashed_password.decode('utf-8'),
                    full_name="Admin User",
                    is_active=True,
                    is_superuser=True,
                    company="ImpactCV"
                )
                
                session.add(sample_user)
                await session.commit()
                logger.info("✅ Sample admin user created: <EMAIL> / admin123")
            else:
                logger.info("ℹ️ Sample admin user already exists")
        
        logger.info("✅ Sample data created successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating sample data: {e}")
        return False

async def main():
    """Main function to initialize the database."""
    logger.info("🚀 Starting database initialization...")
    logger.info(f"📊 Database URL: {settings.get_database_url()}")
    
    # Create tables
    if not await create_tables():
        sys.exit(1)
    
    # Create sample data
    if not await create_sample_data():
        sys.exit(1)
    
    logger.info("🎉 Database initialization completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())
