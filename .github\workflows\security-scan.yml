name: 🔒 Security Scanning

on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM UTC
  workflow_dispatch:
  push:
    branches: [ main, develop ]
    paths:
      - 'app/**'
      - 'requirements*.txt'
      - 'Dockerfile'

env:
  PYTHON_VERSION: '3.11'

jobs:
  # ============================================================================
  # COMPREHENSIVE SECURITY SCANNING
  # ============================================================================
  security-scan:
    name: 🛡️ Comprehensive Security Scan
    runs-on: ubuntu-latest
    
    permissions:
      security-events: write
      contents: read
      actions: read
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
      
      - name: 📦 Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
      
      # ========================================================================
      # SAST (Static Application Security Testing)
      # ========================================================================
      - name: 🔍 CodeQL Analysis
        uses: github/codeql-action/init@v2
        with:
          languages: python
          queries: security-extended,security-and-quality
      
      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:python"
      
      - name: 🔒 Bandit Security Scan
        run: |
          bandit -r app/ -f sarif -o bandit-results.sarif
          bandit -r app/ -ll
      
      - name: 📤 Upload Bandit Results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: bandit-results.sarif
          category: bandit
      
      - name: 🔍 Semgrep Security Scan
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten
            p/python
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
      
      # ========================================================================
      # DEPENDENCY SCANNING
      # ========================================================================
      - name: 🔍 Safety Check
        run: |
          safety check --json --output safety-results.json
          safety check --short-report
        continue-on-error: true
      
      - name: 🔍 Pip Audit
        run: |
          pip-audit --format=sarif --output=pip-audit-results.sarif
          pip-audit --desc
        continue-on-error: true
      
      - name: 📤 Upload Pip Audit Results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: pip-audit-results.sarif
          category: pip-audit
      
      # ========================================================================
      # SECRETS SCANNING
      # ========================================================================
      - name: 🔐 TruffleHog Secrets Scan
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: ${{ github.event.repository.default_branch }}
          head: HEAD
          extra_args: --debug --only-verified --json --output trufflehog-results.json
      
      - name: 🔐 GitLeaks Secrets Scan
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}
      
      # ========================================================================
      # INFRASTRUCTURE SCANNING
      # ========================================================================
      - name: 🏗️ Checkov IaC Scan
        uses: bridgecrewio/checkov-action@master
        with:
          directory: .
          framework: dockerfile,github_actions
          output_format: sarif
          output_file_path: checkov-results.sarif
      
      - name: 📤 Upload Checkov Results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: checkov-results.sarif
          category: checkov
      
      # ========================================================================
      # CONTAINER SCANNING
      # ========================================================================
      - name: 🐳 Build Docker Image for Scanning
        run: |
          docker build -t impactcv:security-scan .
      
      - name: 🔍 Trivy Container Scan
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'impactcv:security-scan'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: 📤 Upload Trivy Results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
          category: trivy
      
      - name: 🔍 Grype Container Scan
        uses: anchore/scan-action@v3
        with:
          image: 'impactcv:security-scan'
          fail-build: false
          severity-cutoff: high
      
      # ========================================================================
      # COMPLIANCE VALIDATION
      # ========================================================================
      - name: 📋 GDPR Compliance Check
        run: |
          python scripts/compliance/gdpr_check.py --scan-results .
      
      - name: 📋 OWASP Top 10 Validation
        run: |
          python scripts/compliance/owasp_validation.py --scan-results .
      
      # ========================================================================
      # REPORTING
      # ========================================================================
      - name: 📊 Generate Security Report
        run: |
          python scripts/security/generate_security_report.py \
            --bandit bandit-results.sarif \
            --safety safety-results.json \
            --trivy trivy-results.sarif \
            --checkov checkov-results.sarif \
            --output security-report.json
      
      - name: 📤 Upload Security Artifacts
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-scan-results
          path: |
            *-results.sarif
            *-results.json
            security-report.json
      
      - name: 📊 Update Security Dashboard
        run: |
          echo "Updating security dashboard with latest scan results..."
          # Add dashboard update logic here
      
      - name: 🚨 Security Alert Notification
        if: failure()
        run: |
          echo "Security scan failed - sending alerts..."
          # Add notification logic here

  # ============================================================================
  # PENETRATION TESTING
  # ============================================================================
  penetration-test:
    name: 🎯 Penetration Testing
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
      
      - name: 🐳 Start Application Stack
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30  # Wait for services to start
      
      - name: 🎯 OWASP ZAP Baseline Scan
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:8000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
      
      - name: 🎯 OWASP ZAP Full Scan
        uses: zaproxy/action-full-scan@v0.4.0
        with:
          target: 'http://localhost:8000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
      
      - name: 🎯 Nuclei Vulnerability Scan
        uses: projectdiscovery/nuclei-action@main
        with:
          target: 'http://localhost:8000'
          templates: 'cves,vulnerabilities,exposures'
      
      - name: 📤 Upload Penetration Test Results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: penetration-test-results
          path: |
            report_html.html
            report_md.md
            nuclei-results.json
      
      - name: 🛑 Stop Application Stack
        if: always()
        run: |
          docker-compose -f docker-compose.test.yml down

  # ============================================================================
  # SECURITY METRICS
  # ============================================================================
  security-metrics:
    name: 📊 Security Metrics
    runs-on: ubuntu-latest
    needs: [security-scan]
    if: always()
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
      
      - name: 📥 Download Security Results
        uses: actions/download-artifact@v3
        with:
          name: security-scan-results
      
      - name: 📊 Calculate Security Metrics
        run: |
          python scripts/security/calculate_metrics.py \
            --scan-results . \
            --output security-metrics.json
      
      - name: 📊 Update Security Scorecard
        run: |
          python scripts/security/update_scorecard.py \
            --metrics security-metrics.json \
            --dashboard-url ${{ secrets.SECURITY_DASHBOARD_URL }}
      
      - name: 📤 Upload Security Metrics
        uses: actions/upload-artifact@v3
        with:
          name: security-metrics
          path: security-metrics.json
