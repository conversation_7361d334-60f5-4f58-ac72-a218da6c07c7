// Animation service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class AnimationService {
    constructor() {
        this.animations = new Map();
        this.subscribers = new Set();
        this.animationFrame = null;
        this.isAnimating = false;
    }

    /**
     * Subscribe to animation events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of animation events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in animation subscriber:', error);
            }
        });
    }

    /**
     * Register an animation
     * @param {string} id - The animation ID
     * @param {Object} options - The animation options
     */
    register(id, options = {}) {
        this.animations.set(id, {
            ...options,
            startTime: null,
            currentTime: null,
            progress: 0,
            isPlaying: false,
        });
    }

    /**
     * Start an animation
     * @param {string} id - The animation ID
     * @param {Object} [options] - The animation options
     */
    start(id, options = {}) {
        return measurePerformance('animation_start', () => {
            const animation = this.animations.get(id);
            if (!animation) {
                console.error(`Animation ${id} not found`);
                return;
            }

            if (animation.isPlaying) {
                return;
            }

            animation.startTime = performance.now();
            animation.currentTime = animation.startTime;
            animation.progress = 0;
            animation.isPlaying = true;

            if (!this.isAnimating) {
                this.isAnimating = true;
                this.animate();
            }

            if (animation.onStart) {
                animation.onStart(options);
            }

            this.notifySubscribers('start', { id, options });
        });
    }

    /**
     * Stop an animation
     * @param {string} id - The animation ID
     */
    stop(id) {
        return measurePerformance('animation_stop', () => {
            const animation = this.animations.get(id);
            if (!animation) {
                console.error(`Animation ${id} not found`);
                return;
            }

            if (!animation.isPlaying) {
                return;
            }

            animation.isPlaying = false;
            animation.progress = 1;

            if (animation.onStop) {
                animation.onStop();
            }

            this.notifySubscribers('stop', { id });

            // Check if any animations are still playing
            this.isAnimating = Array.from(this.animations.values()).some(a => a.isPlaying);
            if (!this.isAnimating) {
                cancelAnimationFrame(this.animationFrame);
            }
        });
    }

    /**
     * Pause an animation
     * @param {string} id - The animation ID
     */
    pause(id) {
        return measurePerformance('animation_pause', () => {
            const animation = this.animations.get(id);
            if (!animation) {
                console.error(`Animation ${id} not found`);
                return;
            }

            if (!animation.isPlaying) {
                return;
            }

            animation.isPlaying = false;

            if (animation.onPause) {
                animation.onPause();
            }

            this.notifySubscribers('pause', { id });
        });
    }

    /**
     * Resume an animation
     * @param {string} id - The animation ID
     */
    resume(id) {
        return measurePerformance('animation_resume', () => {
            const animation = this.animations.get(id);
            if (!animation) {
                console.error(`Animation ${id} not found`);
                return;
            }

            if (animation.isPlaying) {
                return;
            }

            animation.startTime = performance.now() - (animation.currentTime - animation.startTime);
            animation.isPlaying = true;

            if (!this.isAnimating) {
                this.isAnimating = true;
                this.animate();
            }

            if (animation.onResume) {
                animation.onResume();
            }

            this.notifySubscribers('resume', { id });
        });
    }

    /**
     * Update animations
     */
    animate() {
        if (!this.isAnimating) {
            return;
        }

        const currentTime = performance.now();
        let hasPlayingAnimations = false;

        this.animations.forEach((animation, id) => {
            if (!animation.isPlaying) {
                return;
            }

            animation.currentTime = currentTime;
            const elapsed = currentTime - animation.startTime;
            animation.progress = Math.min(elapsed / animation.duration, 1);

            if (animation.onUpdate) {
                animation.onUpdate(animation.progress);
            }

            if (animation.progress < 1) {
                hasPlayingAnimations = true;
            } else {
                animation.isPlaying = false;
                if (animation.onComplete) {
                    animation.onComplete();
                }
                this.notifySubscribers('complete', { id });
            }
        });

        this.isAnimating = hasPlayingAnimations;
        if (this.isAnimating) {
            this.animationFrame = requestAnimationFrame(this.animate.bind(this));
        }
    }

    /**
     * Get animation data
     * @param {string} id - The animation ID
     * @returns {Object} The animation data
     */
    getAnimationData(id) {
        return this.animations.get(id);
    }

    /**
     * Update animation data
     * @param {string} id - The animation ID
     * @param {Object} data - The new animation data
     */
    updateAnimationData(id, data) {
        const animation = this.animations.get(id);
        if (animation) {
            Object.assign(animation, data);
        }
    }

    /**
     * Check if an animation is playing
     * @param {string} id - The animation ID
     * @returns {boolean} Whether the animation is playing
     */
    isPlaying(id) {
        const animation = this.animations.get(id);
        return animation ? animation.isPlaying : false;
    }

    /**
     * Get animation progress
     * @param {string} id - The animation ID
     * @returns {number} The animation progress (0-1)
     */
    getProgress(id) {
        const animation = this.animations.get(id);
        return animation ? animation.progress : 0;
    }
}

// Create and export a singleton instance
const animationService = new AnimationService();
export default animationService; 