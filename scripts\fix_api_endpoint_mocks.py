#!/usr/bin/env python3
"""
<PERSON>ript to fix all API endpoint mocks to use AsyncMock correctly
"""

import re

def fix_api_endpoint_mocks():
    """Fix all API endpoint mocks to use AsyncMock for async methods"""
    
    file_path = "tests/unit/test_api_endpoints.py"
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Patterns to replace
    patterns_to_replace = [
        # Fix generate_tqr_achievement return_value
        (
            r'mock_ai_service\.generate_tqr_achievement\.return_value = ({[^}]+})',
            r'mock_ai_service.generate_tqr_achievement = AsyncMock(return_value=\1)'
        ),
        # Fix generate_tqr_achievement side_effect
        (
            r'mock_ai_service\.generate_tqr_achievement\.side_effect = ([^\n]+)',
            r'mock_ai_service.generate_tqr_achievement = AsyncMock(side_effect=\1)'
        ),
        # Fix health_check return_value
        (
            r'mock_ai_service\.health_check\.return_value = ({[^}]+})',
            r'mock_ai_service.health_check = AsyncMock(return_value=\1)'
        ),
        # Fix health_check side_effect
        (
            r'mock_ai_service\.health_check\.side_effect = ([^\n]+)',
            r'mock_ai_service.health_check = AsyncMock(side_effect=\1)'
        )
    ]
    
    for pattern, replacement in patterns_to_replace:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    # Write back the fixed content
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Fixed API endpoint mocks")

if __name__ == "__main__":
    fix_api_endpoint_mocks()
