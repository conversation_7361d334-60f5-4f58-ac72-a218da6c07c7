// Clipboard service
import config from '../config.js';
import { measurePerformance } from '../utils.js';
import notificationService from './notification.js';

class ClipboardService {
    constructor() {
        this.enabled = config.clipboard?.enabled || true;
        this.subscribers = new Set();
    }

    /**
     * Subscribe to clipboard events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of clipboard events
     * @param {string} action - The clipboard action
     * @param {*} data - The clipboard data
     */
    notifySubscribers(action, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(action, data);
            } catch (error) {
                console.error('Error in clipboard subscriber:', error);
            }
        });
    }

    /**
     * Copy text to clipboard
     * @param {string} text - The text to copy
     * @returns {Promise<boolean>} Whether the copy was successful
     */
    async copy(text) {
        return measurePerformance('clipboard_copy', async () => {
            if (!this.enabled) return false;

            try {
                await navigator.clipboard.writeText(text);
                notificationService.success('Copied to clipboard');
                this.notifySubscribers('copy', text);
                return true;
            } catch (error) {
                console.error('Error copying to clipboard:', error);
                notificationService.error('Failed to copy to clipboard');
                return false;
            }
        });
    }

    /**
     * Paste text from clipboard
     * @returns {Promise<string>} The pasted text
     */
    async paste() {
        return measurePerformance('clipboard_paste', async () => {
            if (!this.enabled) return '';

            try {
                const text = await navigator.clipboard.readText();
                this.notifySubscribers('paste', text);
                return text;
            } catch (error) {
                console.error('Error pasting from clipboard:', error);
                notificationService.error('Failed to paste from clipboard');
                return '';
            }
        });
    }

    /**
     * Copy HTML to clipboard
     * @param {string} html - The HTML to copy
     * @returns {Promise<boolean>} Whether the copy was successful
     */
    async copyHtml(html) {
        return measurePerformance('clipboard_copy_html', async () => {
            if (!this.enabled) return false;

            try {
                const blob = new Blob([html], { type: 'text/html' });
                const data = new ClipboardItem({ 'text/html': blob });
                await navigator.clipboard.write([data]);
                notificationService.success('Copied to clipboard');
                this.notifySubscribers('copyHtml', html);
                return true;
            } catch (error) {
                console.error('Error copying HTML to clipboard:', error);
                notificationService.error('Failed to copy to clipboard');
                return false;
            }
        });
    }

    /**
     * Copy image to clipboard
     * @param {Blob} image - The image to copy
     * @returns {Promise<boolean>} Whether the copy was successful
     */
    async copyImage(image) {
        return measurePerformance('clipboard_copy_image', async () => {
            if (!this.enabled) return false;

            try {
                const data = new ClipboardItem({ [image.type]: image });
                await navigator.clipboard.write([data]);
                notificationService.success('Copied to clipboard');
                this.notifySubscribers('copyImage', image);
                return true;
            } catch (error) {
                console.error('Error copying image to clipboard:', error);
                notificationService.error('Failed to copy to clipboard');
                return false;
            }
        });
    }

    /**
     * Enable clipboard operations
     */
    enable() {
        this.enabled = true;
    }

    /**
     * Disable clipboard operations
     */
    disable() {
        this.enabled = false;
    }

    /**
     * Check if clipboard operations are enabled
     * @returns {boolean} Whether clipboard operations are enabled
     */
    isEnabled() {
        return this.enabled;
    }

    /**
     * Check if clipboard API is available
     * @returns {boolean} Whether clipboard API is available
     */
    isAvailable() {
        return !!navigator.clipboard;
    }
}

// Create and export a singleton instance
const clipboardService = new ClipboardService();
export default clipboardService; 