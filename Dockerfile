# ImpactCV AI-Powered CV Generation System
# Multi-stage Docker build for production deployment

# ============================================================================
# STAGE 1: Base Python Environment
# ============================================================================
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Create non-root user for security
RUN groupadd -r impactcv && useradd -r -g impactcv impactcv

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    g++ \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# ============================================================================
# STAGE 2: Dependencies Installation
# ============================================================================
FROM base as dependencies

# Set working directory
WORKDIR /app

# Copy requirements files
COPY requirements.txt requirements-prod.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-prod.txt

# ============================================================================
# STAGE 3: Development Environment (for testing)
# ============================================================================
FROM dependencies as development

# Install development dependencies
COPY requirements-dev.txt ./
RUN pip install --no-cache-dir -r requirements-dev.txt

# Copy source code
COPY . .

# Change ownership to non-root user
RUN chown -R impactcv:impactcv /app

# Switch to non-root user
USER impactcv

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command for development
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# ============================================================================
# STAGE 4: Production Environment
# ============================================================================
FROM dependencies as production

# Set production environment
ENV ENVIRONMENT=production

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/uploads && \
    chown -R impactcv:impactcv /app

# Copy source code (excluding development files)
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini ./
COPY scripts/ ./scripts/

# Copy configuration files
COPY config/ ./config/

# Change ownership to non-root user
RUN chown -R impactcv:impactcv /app

# Switch to non-root user
USER impactcv

# Set working directory
WORKDIR /app

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Production command
CMD ["gunicorn", "app.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000", "--access-logfile", "-", "--error-logfile", "-"]

# ============================================================================
# STAGE 5: Security Scanning (for CI/CD)
# ============================================================================
FROM production as security-scan

# Switch back to root for security tools installation
USER root

# Install security scanning tools
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    && rm -rf /var/lib/apt/lists/*

# Install Trivy
RUN wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | apt-key add - && \
    echo "deb https://aquasecurity.github.io/trivy-repo/deb generic main" | tee -a /etc/apt/sources.list.d/trivy.list && \
    apt-get update && \
    apt-get install -y trivy && \
    rm -rf /var/lib/apt/lists/*

# Switch back to non-root user
USER impactcv

# Security scan command
CMD ["trivy", "fs", "--security-checks", "vuln,config,secret", "/app"]
