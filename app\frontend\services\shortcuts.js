// Keyboard shortcuts service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ShortcutsService {
    constructor() {
        this.shortcuts = new Map();
        this.enabled = config.shortcuts?.enabled || true;
        this.subscribers = new Set();
    }

    /**
     * Subscribe to shortcut events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of shortcut events
     * @param {string} shortcut - The triggered shortcut
     */
    notifySubscribers(shortcut) {
        this.subscribers.forEach(callback => {
            try {
                callback(shortcut);
            } catch (error) {
                console.error('Error in shortcut subscriber:', error);
            }
        });
    }

    /**
     * Register a keyboard shortcut
     * @param {string} key - The shortcut key combination
     * @param {Function} callback - The callback function
     * @param {Object} [options] - Shortcut options
     */
    register(key, callback, options = {}) {
        this.shortcuts.set(key, { callback, options });
    }

    /**
     * Unregister a keyboard shortcut
     * @param {string} key - The shortcut key combination
     */
    unregister(key) {
        this.shortcuts.delete(key);
    }

    /**
     * Handle keyboard events
     * @param {KeyboardEvent} event - The keyboard event
     */
    handleKeyEvent(event) {
        if (!this.enabled) return;

        const key = this.getKeyCombination(event);
        const shortcut = this.shortcuts.get(key);

        if (shortcut) {
            event.preventDefault();
            shortcut.callback(event);
            this.notifySubscribers(key);
        }
    }

    /**
     * Get the key combination from an event
     * @param {KeyboardEvent} event - The keyboard event
     * @returns {string} The key combination
     */
    getKeyCombination(event) {
        const keys = [];
        if (event.ctrlKey) keys.push('ctrl');
        if (event.altKey) keys.push('alt');
        if (event.shiftKey) keys.push('shift');
        if (event.metaKey) keys.push('meta');
        keys.push(event.key.toLowerCase());
        return keys.join('+');
    }

    /**
     * Enable shortcuts
     */
    enable() {
        this.enabled = true;
    }

    /**
     * Disable shortcuts
     */
    disable() {
        this.enabled = false;
    }

    /**
     * Check if shortcuts are enabled
     * @returns {boolean} Whether shortcuts are enabled
     */
    isEnabled() {
        return this.enabled;
    }

    /**
     * Get all registered shortcuts
     * @returns {Map} The registered shortcuts
     */
    getShortcuts() {
        return this.shortcuts;
    }

    /**
     * Initialize the shortcuts service
     */
    initialize() {
        document.addEventListener('keydown', this.handleKeyEvent.bind(this));
    }
}

// Create and export a singleton instance
const shortcutsService = new ShortcutsService();
export default shortcutsService; 