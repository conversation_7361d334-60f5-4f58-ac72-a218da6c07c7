"""
Base Database Model
Common functionality for all database models
"""

import uuid
from datetime import datetime
from typing import Any, Dict

from sqlalchemy import Column, DateTime, String, Text
from sqlalchemy.ext.declarative import as_declarative, declared_attr
from sqlalchemy.orm import Session


@as_declarative()
class Base:
    """
    Base class for all database models.
    
    Provides common functionality:
    - UUID primary keys
    - Created/updated timestamps
    - Soft delete capability
    - Audit trail support
    """
    
    # Generate table name automatically from class name
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name."""
        # Convert CamelCase to snake_case
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()
    
    # Common columns for all tables
    id = Column(
        String(36),  # UUID as string for SQLite compatibility
        primary_key=True,
        default=lambda: str(uuid.uuid4()),
        index=True,
        doc="Unique identifier for the record"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Timestamp when the record was created"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Timestamp when the record was last updated"
    )
    
    # Soft delete support
    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="Timestamp when the record was soft deleted"
    )
    
    # Audit trail
    created_by = Column(
        String(36),  # UUID as string for SQLite compatibility
        nullable=True,
        doc="ID of the user who created this record"
    )

    updated_by = Column(
        String(36),  # UUID as string for SQLite compatibility
        nullable=True,
        doc="ID of the user who last updated this record"
    )
    
    # Version control for optimistic locking
    version = Column(
        String(50),
        default="1.0",
        nullable=False,
        doc="Version number for optimistic locking"
    )
    
    # Metadata for extensibility
    metadata_json = Column(
        Text,
        nullable=True,
        doc="JSON metadata for extensible attributes"
    )
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"
    
    def to_dict(self, exclude_fields: set = None) -> Dict[str, Any]:
        """
        Convert model to dictionary.
        
        Args:
            exclude_fields: Set of field names to exclude
            
        Returns:
            Dict[str, Any]: Model as dictionary
        """
        exclude_fields = exclude_fields or set()
        
        result = {}
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                
                # Handle datetime serialization
                if isinstance(value, datetime):
                    value = value.isoformat()
                # Handle UUID serialization
                elif isinstance(value, uuid.UUID):
                    value = str(value)
                
                result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], exclude_fields: set = None) -> None:
        """
        Update model from dictionary.
        
        Args:
            data: Dictionary with field values
            exclude_fields: Set of field names to exclude from update
        """
        exclude_fields = exclude_fields or {
            'id', 'created_at', 'created_by'  # Never update these fields
        }
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
        
        # Update timestamp
        self.updated_at = datetime.utcnow()
    
    def soft_delete(self, deleted_by: uuid.UUID = None) -> None:
        """
        Soft delete the record.
        
        Args:
            deleted_by: ID of the user performing the deletion
        """
        self.deleted_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        if deleted_by:
            self.updated_by = deleted_by
    
    def restore(self, restored_by: uuid.UUID = None) -> None:
        """
        Restore a soft-deleted record.
        
        Args:
            restored_by: ID of the user performing the restoration
        """
        self.deleted_at = None
        self.updated_at = datetime.utcnow()
        if restored_by:
            self.updated_by = restored_by
    
    @property
    def is_deleted(self) -> bool:
        """Check if the record is soft deleted."""
        return self.deleted_at is not None
    
    @classmethod
    def get_active_query(cls, session: Session):
        """Get query for non-deleted records."""
        return session.query(cls).filter(cls.deleted_at.is_(None))
    
    def get_metadata(self) -> Dict[str, Any]:
        """
        Get metadata as dictionary.
        
        Returns:
            Dict[str, Any]: Metadata dictionary
        """
        if self.metadata_json:
            import json
            try:
                return json.loads(self.metadata_json)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    def set_metadata(self, metadata: Dict[str, Any]) -> None:
        """
        Set metadata from dictionary.
        
        Args:
            metadata: Metadata dictionary
        """
        import json
        self.metadata_json = json.dumps(metadata, default=str)
    
    def add_metadata(self, key: str, value: Any) -> None:
        """
        Add a single metadata field.
        
        Args:
            key: Metadata key
            value: Metadata value
        """
        metadata = self.get_metadata()
        metadata[key] = value
        self.set_metadata(metadata)
    
    def get_metadata_field(self, key: str, default: Any = None) -> Any:
        """
        Get a single metadata field.
        
        Args:
            key: Metadata key
            default: Default value if key not found
            
        Returns:
            Any: Metadata value or default
        """
        metadata = self.get_metadata()
        return metadata.get(key, default)
