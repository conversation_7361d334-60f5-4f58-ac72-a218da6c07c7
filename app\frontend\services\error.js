// Error reporting service
import config from '../config.js';
import analyticsService from './analytics.js';

class ErrorService {
    constructor() {
        this.showDetailedErrors = config.errors.showDetailedErrors;
        this.maxErrorLogs = config.errors.maxErrorLogs;
        this.errorReportingEndpoint = config.errors.errorReportingEndpoint;
        this.errorLogs = [];
    }

    /**
     * Handle an error
     * @param {Error} error - The error to handle
     * @param {Object} context - Additional context
     */
    async handleError(error, context = {}) {
        const errorDetails = this.formatError(error, context);
        this.logError(errorDetails);
        await this.reportError(errorDetails);
        await this.trackError(errorDetails);
    }

    /**
     * Format an error for reporting
     * @param {Error} error - The error to format
     * @param {Object} context - Additional context
     * @returns {Object} Formatted error details
     */
    formatError(error, context) {
        const errorDetails = {
            message: error.message,
            name: error.name,
            timestamp: new Date().toISOString(),
            ...context,
        };

        if (this.showDetailedErrors) {
            errorDetails.stack = error.stack;
            errorDetails.details = error.response?.data;
        }

        return errorDetails;
    }

    /**
     * Log an error
     * @param {Object} errorDetails - The error details to log
     */
    logError(errorDetails) {
        // Add to error logs
        this.errorLogs.unshift(errorDetails);
        if (this.errorLogs.length > this.maxErrorLogs) {
            this.errorLogs.pop();
        }

        // Log to console
        if (this.showDetailedErrors) {
            console.error('Error details:', errorDetails);
        } else {
            console.error('Error:', errorDetails.message);
        }
    }

    /**
     * Report an error to the error reporting endpoint
     * @param {Object} errorDetails - The error details to report
     */
    async reportError(errorDetails) {
        if (!this.errorReportingEndpoint) return;

        try {
            await fetch(this.errorReportingEndpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(errorDetails),
            });
        } catch (error) {
            console.error('Error reporting error:', error);
        }
    }

    /**
     * Track an error in analytics
     * @param {Object} errorDetails - The error details to track
     */
    async trackError(errorDetails) {
        await analyticsService.trackEvent('error', {
            errorType: errorDetails.name,
            errorMessage: errorDetails.message,
        });
    }

    /**
     * Get error logs
     * @returns {Array} The error logs
     */
    getErrorLogs() {
        return [...this.errorLogs];
    }

    /**
     * Clear error logs
     */
    clearErrorLogs() {
        this.errorLogs = [];
    }

    /**
     * Set up global error handlers
     */
    setupGlobalHandlers() {
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, {
                type: 'unhandledrejection',
            });
        });

        // Handle uncaught errors
        window.addEventListener('error', (event) => {
            this.handleError(event.error || new Error(event.message), {
                type: 'uncaught',
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
            });
        });
    }
}

// Create and export a singleton instance
const errorService = new ErrorService();
export default errorService; 