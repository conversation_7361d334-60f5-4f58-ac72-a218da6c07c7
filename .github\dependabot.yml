# ImpactCV Dependabot Configuration
# Automated dependency updates with security focus

version: 2

updates:
  # ============================================================================
  # PYTHON DEPENDENCIES
  # ============================================================================
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    
    # Security updates
    open-pull-requests-limit: 10
    
    # Commit message configuration
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    
    # Reviewers and assignees
    reviewers:
      - "dasotillop"
    assignees:
      - "dasotillop"
    
    # Labels for categorization
    labels:
      - "dependencies"
      - "security"
      - "automated"
    
    # Version update strategy
    versioning-strategy: "increase"
    
    # Allow specific dependency types
    allow:
      - dependency-type: "direct"
      - dependency-type: "indirect"
    
    # Ignore specific packages (if needed)
    ignore:
      # Example: ignore major version updates for stable packages
      # - dependency-name: "fastapi"
      #   update-types: ["version-update:semver-major"]
      
      # Ignore development dependencies with frequent updates
      - dependency-name: "black"
        update-types: ["version-update:semver-patch"]
      - dependency-name: "flake8"
        update-types: ["version-update:semver-patch"]
    
    # Group related updates
    groups:
      # Security-related packages
      security:
        patterns:
          - "cryptography"
          - "pyjwt"
          - "passlib"
          - "python-jose"
          - "bcrypt"
        
      # FastAPI ecosystem
      fastapi:
        patterns:
          - "fastapi*"
          - "uvicorn*"
          - "starlette*"
          - "pydantic*"
        
      # AI/ML packages
      ai-ml:
        patterns:
          - "openai"
          - "langchain*"
          - "faiss*"
          - "numpy"
          - "pandas"
          - "scikit-learn"
        
      # Database packages
      database:
        patterns:
          - "sqlalchemy*"
          - "alembic"
          - "psycopg*"
          - "asyncpg"
        
      # Testing packages
      testing:
        patterns:
          - "pytest*"
          - "coverage"
          - "factory-boy"
          - "faker"
        
      # Development tools
      dev-tools:
        patterns:
          - "black"
          - "isort"
          - "flake8*"
          - "mypy"
          - "pre-commit"

  # ============================================================================
  # DOCKER DEPENDENCIES
  # ============================================================================
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
      timezone: "UTC"
    
    open-pull-requests-limit: 5
    
    commit-message:
      prefix: "docker"
      include: "scope"
    
    reviewers:
      - "dasotillop"
    assignees:
      - "dasotillop"
    
    labels:
      - "docker"
      - "infrastructure"
      - "security"

  # ============================================================================
  # GITHUB ACTIONS DEPENDENCIES
  # ============================================================================
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "09:00"
      timezone: "UTC"
    
    open-pull-requests-limit: 5
    
    commit-message:
      prefix: "ci"
      include: "scope"
    
    reviewers:
      - "dasotillop"
    assignees:
      - "dasotillop"
    
    labels:
      - "github-actions"
      - "ci-cd"
      - "security"

  # ============================================================================
  # TERRAFORM DEPENDENCIES
  # ============================================================================
  - package-ecosystem: "terraform"
    directory: "/terraform"
    schedule:
      interval: "weekly"
      day: "thursday"
      time: "09:00"
      timezone: "UTC"
    
    open-pull-requests-limit: 5
    
    commit-message:
      prefix: "terraform"
      include: "scope"
    
    reviewers:
      - "dasotillop"
    assignees:
      - "dasotillop"
    
    labels:
      - "terraform"
      - "infrastructure"
      - "security"

  # ============================================================================
  # NPM DEPENDENCIES (if frontend is added)
  # ============================================================================
  # - package-ecosystem: "npm"
  #   directory: "/frontend"
  #   schedule:
  #     interval: "weekly"
  #     day: "friday"
  #     time: "09:00"
  #     timezone: "UTC"
  #   
  #   open-pull-requests-limit: 10
  #   
  #   commit-message:
  #     prefix: "npm"
  #     include: "scope"
  #   
  #   reviewers:
  #     - "dasotillop"
  #   assignees:
  #     - "dasotillop"
  #   
  #   labels:
  #     - "frontend"
  #     - "npm"
  #     - "security"
  #   
  #   groups:
  #     # React ecosystem
  #     react:
  #       patterns:
  #         - "react*"
  #         - "@types/react*"
  #     
  #     # Build tools
  #     build-tools:
  #       patterns:
  #         - "vite*"
  #         - "webpack*"
  #         - "babel*"
  #         - "typescript"
  #     
  #     # Testing tools
  #     testing:
  #       patterns:
  #         - "jest*"
  #         - "@testing-library/*"
  #         - "vitest*"
