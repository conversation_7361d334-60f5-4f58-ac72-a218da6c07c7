// Scroll to top service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ScrollToTopService {
    constructor() {
        this.buttons = new Map();
        this.subscribers = new Set();
        this.initialize();
    }

    /**
     * Initialize the scroll to top service
     */
    initialize() {
        // Add scroll event listener
        window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        window.addEventListener('resize', this.handleResize.bind(this), { passive: true });
    }

    /**
     * Subscribe to scroll to top events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of scroll to top events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in scroll to top subscriber:', error);
            }
        });
    }

    /**
     * Register a scroll to top button
     * @param {string} id - The button ID
     * @param {Object} options - The scroll to top options
     */
    registerButton(id, options = {}) {
        const button = document.getElementById(id);
        if (!button) {
            console.error(`Button ${id} not found`);
            return;
        }

        // Set button styles
        button.style.position = 'fixed';
        button.style.bottom = options.bottom || '20px';
        button.style.right = options.right || '20px';
        button.style.display = 'none';
        button.style.zIndex = options.zIndex || '9999';
        button.style.cursor = 'pointer';
        button.style.transition = 'opacity 0.3s ease-in-out';
        button.style.opacity = '0';

        // Add click event listener
        button.addEventListener('click', () => {
            this.scrollToTop(id);
        });

        this.buttons.set(id, {
            ...options,
            button,
            visible: false,
        });

        // Initial update
        this.updateButton(id);
    }

    /**
     * Handle scroll events
     */
    handleScroll() {
        return measurePerformance('scroll_to_top_scroll', () => {
            this.buttons.forEach((button, id) => {
                this.updateButton(id);
            });
        });
    }

    /**
     * Handle resize events
     */
    handleResize() {
        return measurePerformance('scroll_to_top_resize', () => {
            this.buttons.forEach((button, id) => {
                this.updateButton(id);
            });
        });
    }

    /**
     * Update a scroll to top button
     * @param {string} id - The button ID
     */
    updateButton(id) {
        return measurePerformance('scroll_to_top_update', () => {
            const button = this.buttons.get(id);
            if (!button) {
                return;
            }

            const { button: element, options } = button;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const viewportHeight = window.innerHeight;
            const threshold = options.threshold || viewportHeight;

            // Show/hide button
            const shouldShow = scrollTop > threshold;
            if (shouldShow !== button.visible) {
                button.visible = shouldShow;
                element.style.display = shouldShow ? 'block' : 'none';
                element.style.opacity = shouldShow ? '1' : '0';

                if (button.onVisibilityChange) {
                    button.onVisibilityChange({
                        button: element,
                        visible: shouldShow,
                    });
                }

                this.notifySubscribers('visibilitychange', { id, visible: shouldShow });
            }
        });
    }

    /**
     * Scroll to top
     * @param {string} id - The button ID
     */
    scrollToTop(id) {
        return measurePerformance('scroll_to_top', () => {
            const button = this.buttons.get(id);
            if (!button) {
                return;
            }

            const { button: element, options } = button;

            // Scroll to top
            window.scrollTo({
                top: 0,
                left: 0,
                behavior: options.behavior || 'smooth',
            });

            if (button.onScroll) {
                button.onScroll({
                    button: element,
                });
            }

            this.notifySubscribers('scroll', { id });
        });
    }

    /**
     * Reset a scroll to top button
     * @param {string} id - The button ID
     */
    resetButton(id) {
        return measurePerformance('scroll_to_top_reset', () => {
            const button = this.buttons.get(id);
            if (!button) {
                return;
            }

            const { button: element } = button;

            // Reset button
            element.style.display = 'none';
            element.style.opacity = '0';
            button.visible = false;

            if (button.onReset) {
                button.onReset({
                    button: element,
                });
            }

            this.notifySubscribers('reset', { id });
        });
    }

    /**
     * Get button data
     * @param {string} id - The button ID
     * @returns {Object} The button data
     */
    getButtonData(id) {
        return this.buttons.get(id);
    }

    /**
     * Update button data
     * @param {string} id - The button ID
     * @param {Object} data - The new button data
     */
    updateButtonData(id, data) {
        const button = this.buttons.get(id);
        if (button) {
            Object.assign(button, data);
            this.updateButton(id);
        }
    }
}

// Create and export a singleton instance
const scrollToTopService = new ScrollToTopService();
export default scrollToTopService; 