# ImpactCV AI-Powered CV Generation System
# Terraform Variables Example Configuration
# Copy this file to terraform.tfvars and customize for your environment

# ============================================================================
# GENERAL CONFIGURATION
# ============================================================================
environment = "dev"
aws_region  = "us-west-2"

# ============================================================================
# NETWORKING CONFIGURATION
# ============================================================================
vpc_cidr = "10.0.0.0/16"

private_subnets = [
  "********/24",
  "********/24",
  "********/24"
]

public_subnets = [
  "**********/24",
  "**********/24",
  "**********/24"
]

# Restrict this in production to your office/VPN IPs
allowed_cidr_blocks = ["0.0.0.0/0"]

# ============================================================================
# EKS CONFIGURATION
# ============================================================================
kubernetes_version = "1.28"

eks_node_groups = {
  general = {
    instance_types = ["t3.medium"]
    capacity_type  = "ON_DEMAND"
    min_size      = 1
    max_size      = 5
    desired_size  = 2
    disk_size     = 50
    ami_type      = "AL2_x86_64"
    labels = {
      role = "general"
    }
    taints = []
  }
  
  # Uncomment for production workloads
  # compute = {
  #   instance_types = ["c5.large", "c5.xlarge"]
  #   capacity_type  = "SPOT"
  #   min_size      = 0
  #   max_size      = 10
  #   desired_size  = 1
  #   disk_size     = 100
  #   ami_type      = "AL2_x86_64"
  #   labels = {
  #     role = "compute"
  #   }
  #   taints = [{
  #     key    = "compute"
  #     value  = "true"
  #     effect = "NO_SCHEDULE"
  #   }]
  # }
}

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================
postgres_version            = "15.4"
db_instance_class          = "db.t3.micro"  # Use db.r6g.large for production
db_allocated_storage       = 20
db_backup_retention_period = 7
db_backup_window          = "03:00-04:00"
db_maintenance_window     = "sun:04:00-sun:05:00"

# ============================================================================
# CACHE CONFIGURATION
# ============================================================================
redis_node_type      = "cache.t3.micro"  # Use cache.r6g.large for production
redis_num_nodes      = 1
redis_parameter_group = "default.redis7"
# redis_auth_token    = "your-secure-redis-token"  # Uncomment and set

# ============================================================================
# MONITORING CONFIGURATION
# ============================================================================
log_retention_days = 30
enable_prometheus  = true
enable_grafana     = true

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
domain_name = "impactcv.local"  # Change to your domain
enable_waf  = true

# ============================================================================
# APPLICATION CONFIGURATION
# ============================================================================
# REQUIRED: Set your OpenAI API key
openai_api_key = "sk-your-openai-api-key-here"

# ============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# ============================================================================
environment_configs = {
  dev = {
    db_instance_class     = "db.t3.micro"
    redis_node_type      = "cache.t3.micro"
    eks_node_desired_size = 1
    log_retention_days   = 7
  }
  
  staging = {
    db_instance_class     = "db.t3.small"
    redis_node_type      = "cache.t3.small"
    eks_node_desired_size = 2
    log_retention_days   = 14
  }
  
  prod = {
    db_instance_class     = "db.r6g.large"
    redis_node_type      = "cache.r6g.large"
    eks_node_desired_size = 3
    log_retention_days   = 90
  }
}

# ============================================================================
# FEATURE FLAGS
# ============================================================================
feature_flags = {
  enable_backup_bucket     = true
  enable_cdn              = false  # Set to true for production
  enable_elasticsearch    = false  # Set to true if you need centralized logging
  enable_external_secrets = false  # Set to true for advanced secret management
  enable_istio           = false  # Set to true for service mesh
  enable_vault           = false  # Set to true for advanced secret management
}

# ============================================================================
# COST OPTIMIZATION
# ============================================================================
cost_optimization = {
  use_spot_instances      = false  # Set to true for cost savings (with some risk)
  enable_cluster_autoscaler = true
  enable_vertical_pod_autoscaler = false
  enable_scheduled_scaling = false
}

# ============================================================================
# COMPLIANCE CONFIGURATION
# ============================================================================
compliance_settings = {
  enable_config_rules     = true
  enable_cloudtrail      = true
  enable_guardduty       = true
  enable_security_hub    = true
  enable_inspector       = true
}

# ============================================================================
# BACKUP CONFIGURATION
# ============================================================================
backup_settings = {
  enable_cross_region_backup = false  # Set to true for production
  backup_schedule           = "cron(0 2 * * ? *)"  # Daily at 2 AM
  backup_retention_days     = 30
  enable_point_in_time_recovery = true
}

# ============================================================================
# SCALING CONFIGURATION
# ============================================================================
scaling_settings = {
  target_cpu_utilization    = 70
  target_memory_utilization = 80
  scale_up_cooldown         = 300
  scale_down_cooldown       = 300
}

# ============================================================================
# DEVELOPMENT ENVIRONMENT SPECIFIC
# ============================================================================
# Uncomment these for development environment
# allowed_cidr_blocks = ["YOUR_OFFICE_IP/32", "YOUR_VPN_IP/32"]

# ============================================================================
# STAGING ENVIRONMENT SPECIFIC
# ============================================================================
# Uncomment these for staging environment
# environment = "staging"
# db_instance_class = "db.t3.small"
# redis_node_type = "cache.t3.small"

# ============================================================================
# PRODUCTION ENVIRONMENT SPECIFIC
# ============================================================================
# Uncomment these for production environment
# environment = "prod"
# db_instance_class = "db.r6g.large"
# redis_node_type = "cache.r6g.large"
# log_retention_days = 90
# 
# feature_flags = {
#   enable_backup_bucket     = true
#   enable_cdn              = true
#   enable_elasticsearch    = true
#   enable_external_secrets = true
#   enable_istio           = false
#   enable_vault           = true
# }
# 
# backup_settings = {
#   enable_cross_region_backup = true
#   backup_schedule           = "cron(0 2 * * ? *)"
#   backup_retention_days     = 90
#   enable_point_in_time_recovery = true
# }
# 
# cost_optimization = {
#   use_spot_instances      = true
#   enable_cluster_autoscaler = true
#   enable_vertical_pod_autoscaler = true
#   enable_scheduled_scaling = true
# }
