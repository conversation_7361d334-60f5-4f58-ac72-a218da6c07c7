"""
In-Memory Cache Service
Alternative to Redis for development
"""

import asyncio
import json
import time
from typing import Any, Dict, Optional, Union
from cachetools import TTLCache
import logging

logger = logging.getLogger(__name__)

class InMemoryCache:
    """
    In-memory cache service using TTLCache as an alternative to Redis.
    Suitable for development and single-instance deployments.
    """
    
    def __init__(self, maxsize: int = 1000, ttl: int = 3600):
        """
        Initialize the in-memory cache.
        
        Args:
            maxsize: Maximum number of items in cache
            ttl: Default time-to-live in seconds
        """
        self._cache = TTLCache(maxsize=maxsize, ttl=ttl)
        self._lock = asyncio.Lock()
        self._initialized = False
        
    async def initialize(self) -> None:
        """Initialize the cache service."""
        if not self._initialized:
            logger.info("🔄 Initializing in-memory cache service...")
            self._initialized = True
            logger.info("✅ In-memory cache service initialized")
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Get a value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        async with self._lock:
            try:
                value = self._cache.get(key)
                if value is not None:
                    # Deserialize JSON if it's a string
                    if isinstance(value, str):
                        try:
                            return json.loads(value)
                        except json.JSONDecodeError:
                            return value
                    return value
                return None
            except Exception as e:
                logger.error(f"Error getting cache key {key}: {e}")
                return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set a value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (optional)
            
        Returns:
            True if successful, False otherwise
        """
        async with self._lock:
            try:
                # Serialize complex objects to JSON
                if isinstance(value, (dict, list)):
                    value = json.dumps(value)
                
                if ttl:
                    # Create a new TTLCache with custom TTL for this item
                    # Note: TTLCache doesn't support per-item TTL, so we store expiry time
                    expiry_time = time.time() + ttl
                    self._cache[key] = {"value": value, "expires": expiry_time}
                else:
                    self._cache[key] = value
                
                return True
            except Exception as e:
                logger.error(f"Error setting cache key {key}: {e}")
                return False
    
    async def delete(self, key: str) -> bool:
        """
        Delete a key from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if key was deleted, False if not found
        """
        async with self._lock:
            try:
                if key in self._cache:
                    del self._cache[key]
                    return True
                return False
            except Exception as e:
                logger.error(f"Error deleting cache key {key}: {e}")
                return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if key exists, False otherwise
        """
        async with self._lock:
            return key in self._cache
    
    async def clear(self) -> bool:
        """
        Clear all cache entries.
        
        Returns:
            True if successful
        """
        async with self._lock:
            try:
                self._cache.clear()
                return True
            except Exception as e:
                logger.error(f"Error clearing cache: {e}")
                return False
    
    async def keys(self, pattern: str = "*") -> list:
        """
        Get all keys matching pattern.
        
        Args:
            pattern: Key pattern (simplified, only supports *)
            
        Returns:
            List of matching keys
        """
        async with self._lock:
            try:
                if pattern == "*":
                    return list(self._cache.keys())
                else:
                    # Simple pattern matching
                    pattern = pattern.replace("*", "")
                    return [key for key in self._cache.keys() if pattern in key]
            except Exception as e:
                logger.error(f"Error getting keys with pattern {pattern}: {e}")
                return []
    
    async def info(self) -> Dict[str, Any]:
        """
        Get cache information.
        
        Returns:
            Dictionary with cache statistics
        """
        async with self._lock:
            return {
                "type": "in_memory",
                "maxsize": self._cache.maxsize,
                "current_size": len(self._cache),
                "ttl": self._cache.ttl,
                "hits": getattr(self._cache, 'hits', 0),
                "misses": getattr(self._cache, 'misses', 0)
            }
    
    async def close(self) -> None:
        """Close the cache service."""
        if self._initialized:
            await self.clear()
            self._initialized = False
            logger.info("🔄 In-memory cache service closed")

# Global cache instance
cache_service = InMemoryCache()

# Compatibility functions for Redis-like interface
async def get_cache(key: str) -> Optional[Any]:
    """Get value from cache."""
    return await cache_service.get(key)

async def set_cache(key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """Set value in cache."""
    return await cache_service.set(key, value, ttl)

async def delete_cache(key: str) -> bool:
    """Delete key from cache."""
    return await cache_service.delete(key)

async def clear_cache() -> bool:
    """Clear all cache."""
    return await cache_service.clear()
