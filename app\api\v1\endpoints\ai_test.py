"""
AI Test Endpoints
Endpoints para probar la funcionalidad de IA con Mistral 7B local
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field

from app.services.ai_service import ai_service
from app.services.llm_service import llm_service, LLMRequest
from app.core.security import InputSanitizer

logger = logging.getLogger(__name__)

router = APIRouter()

class TQRRequest(BaseModel):
    """Request para generar logro TQR"""
    name: str = Field(..., description="Nombre del candidato")
    position: str = Field(..., description="Posición actual")
    experience_years: int = Field(..., description="Años de experiencia")
    achievement_description: str = Field(..., description="Descripción del logro")

class TQRResponse(BaseModel):
    """Response con logro TQR generado"""
    tarea: str = Field(..., description="Tarea realizada")
    cuantificacion: str = Field(..., description="Métricas cuantificables")
    resultado: str = Field(..., description="Resultado obtenido")
    original_description: str = Field(..., description="Descripción original")
    processing_time_ms: float = Field(..., description="Tiempo de procesamiento")

class AIHealthResponse(BaseModel):
    """Response del health check de IA"""
    status: str = Field(..., description="Estado del servicio")
    provider: str = Field(..., description="Proveedor de IA activo")
    details: Dict[str, Any] = Field(..., description="Detalles adicionales")

@router.get("/health", response_model=AIHealthResponse)
async def ai_health_check():
    """
    Verificar el estado del servicio de IA
    """
    try:
        health_result = await ai_service.health_check()
        
        return AIHealthResponse(
            status=health_result.get("status", "unknown"),
            provider=health_result.get("provider", "unknown"),
            details=health_result
        )
        
    except Exception as e:
        logger.error(f"AI health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI health check failed: {str(e)}"
        )

@router.get("/llm-health")
async def llm_health_check():
    """
    Verificar el estado del servicio LLM
    """
    try:
        health_result = await llm_service.health_check()
        return health_result
        
    except Exception as e:
        logger.error(f"LLM health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"LLM health check failed: {str(e)}"
        )

@router.post("/generate-tqr", response_model=TQRResponse)
async def generate_tqr_achievement(request: TQRRequest):
    """
    Generar logro en formato TQR usando IA
    
    Transforma una descripción de experiencia en un logro estructurado
    usando la metodología TQR (Tarea, Cuantificación, Resultado).
    """
    try:
        import time
        start_time = time.time()
        
        # Sanitizar inputs
        sanitized_request = TQRRequest(
            name=InputSanitizer.sanitize_string(request.name),
            position=InputSanitizer.sanitize_string(request.position),
            experience_years=request.experience_years,
            achievement_description=InputSanitizer.sanitize_string(request.achievement_description)
        )
        
        # Preparar datos del perfil
        profile_data = {
            "name": sanitized_request.name,
            "position": sanitized_request.position,
            "experience_years": sanitized_request.experience_years
        }
        
        # Generar logro TQR usando el servicio de IA
        tqr_result = await ai_service.generate_tqr_achievement(
            profile_data=profile_data,
            achievement_description=sanitized_request.achievement_description,
            temperature=0.7,
            max_tokens=400
        )
        
        # Calcular tiempo de procesamiento
        processing_time_ms = (time.time() - start_time) * 1000
        
        # Crear respuesta
        response = TQRResponse(
            tarea=tqr_result.get("tarea", ""),
            cuantificacion=tqr_result.get("cuantificacion", ""),
            resultado=tqr_result.get("resultado", ""),
            original_description=sanitized_request.achievement_description,
            processing_time_ms=processing_time_ms
        )
        
        logger.info(f"TQR generation completed in {processing_time_ms:.2f}ms")
        return response
        
    except Exception as e:
        logger.error(f"TQR generation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"TQR generation failed: {str(e)}"
        )

@router.post("/test-completion")
async def test_ai_completion(
    prompt: str = "Escribe un saludo profesional en español",
    system_prompt: str = "Eres un asistente profesional que responde en español",
    temperature: float = 0.7,
    max_tokens: int = 100
):
    """
    Probar la generación de texto con IA
    """
    try:
        import time
        start_time = time.time()
        
        # Sanitizar prompt
        clean_prompt = InputSanitizer.sanitize_string(prompt)
        clean_system_prompt = InputSanitizer.sanitize_string(system_prompt) if system_prompt else None
        
        # Generar completion
        result = await ai_service.generate_completion(
            prompt=clean_prompt,
            system_prompt=clean_system_prompt,
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        processing_time_ms = (time.time() - start_time) * 1000
        
        return {
            "prompt": clean_prompt,
            "system_prompt": clean_system_prompt,
            "response": result,
            "processing_time_ms": processing_time_ms,
            "provider": ai_service.settings.AI_PROVIDER,
            "parameters": {
                "temperature": temperature,
                "max_tokens": max_tokens
            }
        }
        
    except Exception as e:
        logger.error(f"AI completion test failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI completion test failed: {str(e)}"
        )

@router.post("/test-json-completion")
async def test_ai_json_completion(
    prompt: str = "Genera un ejemplo de CV en JSON con nombre, email y habilidades",
    system_prompt: str = "Eres un experto en CVs que responde únicamente en JSON válido",
    temperature: float = 0.3
):
    """
    Probar la generación de JSON con IA
    """
    try:
        import time
        start_time = time.time()
        
        # Sanitizar prompts
        clean_prompt = InputSanitizer.sanitize_string(prompt)
        clean_system_prompt = InputSanitizer.sanitize_string(system_prompt) if system_prompt else None
        
        # Ejemplo de esquema
        schema_example = {
            "nombre": "string",
            "email": "string",
            "habilidades": ["string"]
        }
        
        # Generar JSON completion
        result = await ai_service.generate_json_completion(
            prompt=clean_prompt,
            system_prompt=clean_system_prompt,
            schema_example=schema_example,
            temperature=temperature,
            max_tokens=300
        )
        
        processing_time_ms = (time.time() - start_time) * 1000
        
        return {
            "prompt": clean_prompt,
            "system_prompt": clean_system_prompt,
            "json_response": result,
            "processing_time_ms": processing_time_ms,
            "provider": ai_service.settings.AI_PROVIDER,
            "schema_example": schema_example
        }
        
    except Exception as e:
        logger.error(f"AI JSON completion test failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI JSON completion test failed: {str(e)}"
        )
