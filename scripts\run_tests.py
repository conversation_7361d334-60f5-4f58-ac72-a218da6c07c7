#!/usr/bin/env python3
"""
Test Runner Script
Runs all tests and generates a comprehensive report
"""

import os
import sys
import json
import time
import subprocess
from datetime import datetime
from typing import Dict, Any, List

def run_command(command: List[str]) -> Dict[str, Any]:
    """Run a command and return its output"""
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True
        )
        return {
            "success": True,
            "output": result.stdout,
            "error": result.stderr
        }
    except subprocess.CalledProcessError as e:
        return {
            "success": False,
            "output": e.stdout,
            "error": e.stderr
        }

def run_unit_tests() -> Dict[str, Any]:
    """Run unit tests with coverage"""
    print("Running unit tests...")
    return run_command([
        "pytest",
        "tests/unit",
        "--cov=app",
        "--cov-report=json",
        "--cov-report=term-missing"
    ])

def run_performance_tests() -> Dict[str, Any]:
    """Run performance tests"""
    print("Running performance tests...")
    return run_command([
        "python",
        "app/tests/performance/test_api_performance.py"
    ])

def run_ui_tests() -> Dict[str, Any]:
    """Run UI tests"""
    print("Running UI tests...")
    return run_command([
        "python",
        "-m",
        "unittest",
        "app/tests/ui/test_frontend.py"
    ])

def generate_report(results: Dict[str, Any]) -> None:
    """Generate a comprehensive test report"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"test_report_{timestamp}.json"
    
    # Add metadata
    results["timestamp"] = datetime.now().isoformat()
    results["environment"] = {
        "python_version": sys.version,
        "platform": sys.platform,
        "cwd": os.getcwd()
    }
    
    # Save report
    with open(report_file, "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\nTest report saved to: {report_file}")
    
    # Print summary
    print("\nTest Summary:")
    print(f"Unit Tests: {'✅' if results['unit_tests']['success'] else '❌'}")
    print(f"Performance Tests: {'✅' if results['performance_tests']['success'] else '❌'}")
    print(f"UI Tests: {'✅' if results['ui_tests']['success'] else '❌'}")
    
    # Print coverage if available
    if os.path.exists("coverage.json"):
        with open("coverage.json") as f:
            coverage = json.load(f)
            total_coverage = coverage.get("totals", {}).get("percent_covered", 0)
            print(f"\nTotal Coverage: {total_coverage:.2f}%")

def main():
    """Main function to run all tests"""
    start_time = time.time()
    
    # Run all tests
    results = {
        "unit_tests": run_unit_tests(),
        "performance_tests": run_performance_tests(),
        "ui_tests": run_ui_tests()
    }
    
    # Calculate total time
    total_time = time.time() - start_time
    results["total_time"] = total_time
    
    # Generate report
    generate_report(results)
    
    # Return success if all tests passed
    return 0 if all(r["success"] for r in results.values()) else 1

if __name__ == "__main__":
    sys.exit(main())
