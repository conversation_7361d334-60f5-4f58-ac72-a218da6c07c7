# 🔐 OAuth 2.0 & OpenID Connect Authentication Flows

> **Enterprise Authentication Architecture for ImpactCV**  
> **Standards:** OAuth 2.0 RFC 6749 | OpenID Connect Core 1.0 | PKCE RFC 7636

---

## 📋 **EXECUTIVE SUMMARY**

### **Authentication Strategy**
ImpactCV implements a comprehensive OAuth 2.0 and OpenID Connect authentication system with multi-factor authentication (MFA), supporting both local development and enterprise-grade production deployment. The system follows security best practices including PKCE, state validation, and JWT token management.

### **Supported Flows**
- **Authorization Code Flow with PKCE** (Primary for web applications)
- **Client Credentials Flow** (Service-to-service authentication)
- **Device Authorization Flow** (For CLI tools and IoT devices)
- **Refresh Token Flow** (Token renewal)

---

## 🔄 **AUTHENTICATION FLOWS**

### **1. Authorization Code Flow with PKCE**

```mermaid
sequenceDiagram
    participant User as User Browser
    participant Client as Web Application
    participant Auth as Auth Service
    participant API as API Gateway
    participant Resource as CV Service
    
    User->>Client: 1. Access protected resource
    Client->>Client: 2. Generate PKCE code_verifier & code_challenge
    Client->>Auth: 3. Redirect to /authorize with PKCE
    Auth->>User: 4. Show login form
    User->>Auth: 5. Submit credentials + MFA
    Auth->>Auth: 6. Validate credentials & MFA
    Auth->>Client: 7. Redirect with authorization code
    Client->>Auth: 8. Exchange code for tokens (with code_verifier)
    Auth->>Client: 9. Return access_token + id_token + refresh_token
    Client->>API: 10. API call with Bearer token
    API->>API: 11. Validate JWT token
    API->>Resource: 12. Forward request
    Resource->>API: 13. Return response
    API->>Client: 14. Return response
    Client->>User: 15. Display protected content
```

### **2. Client Credentials Flow (Service-to-Service)**

```mermaid
sequenceDiagram
    participant Service1 as CV Generation Service
    participant Auth as Auth Service
    participant Service2 as RAG Service
    
    Service1->>Auth: 1. POST /token with client_credentials
    Note over Service1,Auth: client_id, client_secret, scope
    Auth->>Auth: 2. Validate client credentials
    Auth->>Service1: 3. Return access_token (JWT)
    Service1->>Service2: 4. API call with Bearer token
    Service2->>Service2: 5. Validate JWT token
    Service2->>Service1: 6. Return response
```

---

## 🏗️ **AUTHENTICATION SERVICE ARCHITECTURE**

### **Core Components**

```python
# Authentication Service Implementation
from fastapi import FastAPI, HTTPException, Depends
from fastapi.security import OAuth2AuthorizationCodeBearer
import jwt
import secrets
import hashlib
import base64
from datetime import datetime, timedelta

class AuthenticationService:
    def __init__(self):
        self.app = FastAPI(title="ImpactCV Auth Service")
        self.jwt_secret = os.getenv("JWT_SECRET_KEY")
        self.jwt_algorithm = "HS256"
        self.access_token_expire = 30  # minutes
        self.refresh_token_expire = 7  # days
        
        # OAuth 2.0 Configuration
        self.oauth_config = {
            "authorization_endpoint": "/oauth/authorize",
            "token_endpoint": "/oauth/token",
            "userinfo_endpoint": "/oauth/userinfo",
            "jwks_uri": "/oauth/jwks",
            "scopes_supported": ["openid", "profile", "email", "cv:read", "cv:write"],
            "response_types_supported": ["code"],
            "grant_types_supported": ["authorization_code", "client_credentials", "refresh_token"],
            "code_challenge_methods_supported": ["S256"]
        }
        
        self.setup_routes()
    
    def setup_routes(self):
        @self.app.get("/.well-known/openid_configuration")
        async def openid_configuration():
            return self.oauth_config
        
        @self.app.get("/oauth/authorize")
        async def authorize(
            client_id: str,
            redirect_uri: str,
            response_type: str = "code",
            scope: str = "openid profile",
            state: str = None,
            code_challenge: str = None,
            code_challenge_method: str = "S256"
        ):
            # Validate client and redirect URI
            client = await self.validate_client(client_id, redirect_uri)
            if not client:
                raise HTTPException(400, "Invalid client or redirect URI")
            
            # Store PKCE challenge for later verification
            if code_challenge:
                await self.store_pkce_challenge(client_id, code_challenge, code_challenge_method)
            
            # Return login form or redirect to login page
            return {"login_url": f"/login?client_id={client_id}&state={state}"}
        
        @self.app.post("/oauth/token")
        async def token(
            grant_type: str,
            client_id: str = None,
            client_secret: str = None,
            code: str = None,
            redirect_uri: str = None,
            code_verifier: str = None,
            refresh_token: str = None,
            scope: str = None
        ):
            if grant_type == "authorization_code":
                return await self.handle_authorization_code_flow(
                    client_id, client_secret, code, redirect_uri, code_verifier
                )
            elif grant_type == "client_credentials":
                return await self.handle_client_credentials_flow(
                    client_id, client_secret, scope
                )
            elif grant_type == "refresh_token":
                return await self.handle_refresh_token_flow(refresh_token)
            else:
                raise HTTPException(400, "Unsupported grant type")
```

### **PKCE Implementation**

```python
# PKCE (Proof Key for Code Exchange) Implementation
class PKCEHandler:
    @staticmethod
    def generate_code_verifier() -> str:
        """Generate a cryptographically random code verifier"""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    @staticmethod
    def generate_code_challenge(code_verifier: str) -> str:
        """Generate code challenge from verifier using S256 method"""
        digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')
    
    @staticmethod
    def verify_code_challenge(code_verifier: str, code_challenge: str) -> bool:
        """Verify that code_verifier matches the stored code_challenge"""
        expected_challenge = PKCEHandler.generate_code_challenge(code_verifier)
        return secrets.compare_digest(expected_challenge, code_challenge)

# Client-side PKCE implementation (JavaScript)
class PKCEClient:
    def __init__(self):
        self.code_verifier = None
        self.code_challenge = None
    
    def generate_pkce_pair(self):
        """Generate PKCE code verifier and challenge pair"""
        self.code_verifier = self.generate_code_verifier()
        self.code_challenge = self.generate_code_challenge(self.code_verifier)
        return {
            "code_verifier": self.code_verifier,
            "code_challenge": self.code_challenge
        }
    
    def build_authorization_url(self, client_id: str, redirect_uri: str, scope: str = "openid profile"):
        """Build authorization URL with PKCE parameters"""
        pkce_pair = self.generate_pkce_pair()
        state = secrets.token_urlsafe(32)
        
        params = {
            "client_id": client_id,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "scope": scope,
            "state": state,
            "code_challenge": pkce_pair["code_challenge"],
            "code_challenge_method": "S256"
        }
        
        return f"/oauth/authorize?{urlencode(params)}"
```

---

## 🔐 **MULTI-FACTOR AUTHENTICATION (MFA)**

### **MFA Flow Integration**

```python
# MFA Integration with OAuth Flow
class MFAService:
    def __init__(self):
        self.totp_service = TOTPService()
        self.email_service = EmailService()
        self.sms_service = SMSService()  # Optional
    
    async def initiate_mfa(self, user_id: str, primary_auth_token: str) -> MFAChallenge:
        """Initiate MFA challenge after successful primary authentication"""
        user = await self.get_user(user_id)
        
        # Determine available MFA methods for user
        available_methods = []
        if user.totp_enabled:
            available_methods.append("totp")
        if user.email_verified:
            available_methods.append("email")
        if user.phone_verified:
            available_methods.append("sms")
        
        # Generate MFA challenge
        challenge_id = str(uuid.uuid4())
        challenge = MFAChallenge(
            challenge_id=challenge_id,
            user_id=user_id,
            available_methods=available_methods,
            expires_at=datetime.utcnow() + timedelta(minutes=5)
        )
        
        # Store challenge temporarily
        await self.store_mfa_challenge(challenge)
        
        return challenge
    
    async def verify_mfa(self, challenge_id: str, method: str, code: str) -> MFAResult:
        """Verify MFA code and complete authentication"""
        challenge = await self.get_mfa_challenge(challenge_id)
        if not challenge or challenge.expires_at < datetime.utcnow():
            return MFAResult(success=False, error="Challenge expired or not found")
        
        # Verify the provided code based on method
        if method == "totp":
            is_valid = await self.totp_service.verify_code(challenge.user_id, code)
        elif method == "email":
            is_valid = await self.email_service.verify_code(challenge.user_id, code)
        elif method == "sms":
            is_valid = await self.sms_service.verify_code(challenge.user_id, code)
        else:
            return MFAResult(success=False, error="Invalid MFA method")
        
        if is_valid:
            # Clean up challenge
            await self.delete_mfa_challenge(challenge_id)
            return MFAResult(success=True, user_id=challenge.user_id)
        else:
            return MFAResult(success=False, error="Invalid MFA code")
```

### **TOTP Implementation**

```python
# Time-based One-Time Password (TOTP) Implementation
import pyotp
import qrcode
from io import BytesIO

class TOTPService:
    def __init__(self):
        self.issuer = "ImpactCV"
    
    def generate_secret(self, user_id: str) -> str:
        """Generate a new TOTP secret for user"""
        secret = pyotp.random_base32()
        # Store secret securely in database
        await self.store_totp_secret(user_id, secret)
        return secret
    
    def generate_qr_code(self, user_email: str, secret: str) -> bytes:
        """Generate QR code for TOTP setup"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=self.issuer
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        img_buffer = BytesIO()
        img.save(img_buffer, format='PNG')
        return img_buffer.getvalue()
    
    async def verify_code(self, user_id: str, code: str) -> bool:
        """Verify TOTP code for user"""
        secret = await self.get_totp_secret(user_id)
        if not secret:
            return False
        
        totp = pyotp.TOTP(secret)
        return totp.verify(code, valid_window=1)  # Allow 1 time step tolerance
```

---

## 🛡️ **JWT TOKEN MANAGEMENT**

### **JWT Token Structure**

```python
# JWT Token Implementation
class JWTTokenManager:
    def __init__(self):
        self.secret_key = os.getenv("JWT_SECRET_KEY")
        self.algorithm = "HS256"
        self.issuer = "impactcv-auth"
        self.audience = "impactcv-api"
    
    def create_access_token(self, user_id: str, scopes: List[str], client_id: str = None) -> str:
        """Create JWT access token"""
        now = datetime.utcnow()
        payload = {
            # Standard claims
            "iss": self.issuer,
            "aud": self.audience,
            "sub": user_id,
            "iat": now,
            "exp": now + timedelta(minutes=30),
            "jti": str(uuid.uuid4()),
            
            # Custom claims
            "scope": " ".join(scopes),
            "client_id": client_id,
            "token_type": "access_token"
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def create_id_token(self, user: User, client_id: str, nonce: str = None) -> str:
        """Create OpenID Connect ID token"""
        now = datetime.utcnow()
        payload = {
            # Standard OIDC claims
            "iss": self.issuer,
            "aud": client_id,
            "sub": user.id,
            "iat": now,
            "exp": now + timedelta(minutes=30),
            "auth_time": user.last_login.timestamp(),
            
            # Profile claims
            "name": user.full_name,
            "email": user.email,
            "email_verified": user.email_verified,
            "picture": user.profile_picture_url,
            
            # Optional nonce for replay protection
            "nonce": nonce
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, user_id: str, client_id: str) -> str:
        """Create refresh token"""
        now = datetime.utcnow()
        payload = {
            "iss": self.issuer,
            "sub": user_id,
            "iat": now,
            "exp": now + timedelta(days=7),
            "jti": str(uuid.uuid4()),
            "client_id": client_id,
            "token_type": "refresh_token"
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def validate_token(self, token: str) -> TokenValidation:
        """Validate and decode JWT token"""
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm],
                audience=self.audience,
                issuer=self.issuer
            )
            
            # Check if token is revoked
            if await self.is_token_revoked(payload["jti"]):
                return TokenValidation(valid=False, error="Token revoked")
            
            return TokenValidation(
                valid=True,
                user_id=payload["sub"],
                scopes=payload.get("scope", "").split(),
                client_id=payload.get("client_id"),
                expires_at=datetime.fromtimestamp(payload["exp"])
            )
            
        except jwt.ExpiredSignatureError:
            return TokenValidation(valid=False, error="Token expired")
        except jwt.InvalidTokenError as e:
            return TokenValidation(valid=False, error=str(e))
```

---

## 🔒 **SECURITY MIDDLEWARE**

### **FastAPI Security Integration**

```python
# FastAPI Security Middleware
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

class AuthenticationMiddleware:
    def __init__(self, jwt_manager: JWTTokenManager):
        self.jwt_manager = jwt_manager
    
    async def get_current_user(
        self, 
        credentials: HTTPAuthorizationCredentials = Depends(security),
        required_scopes: List[str] = None
    ) -> User:
        """Extract and validate user from JWT token"""
        token = credentials.credentials
        validation = await self.jwt_manager.validate_token(token)
        
        if not validation.valid:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=validation.error,
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Check required scopes
        if required_scopes:
            if not all(scope in validation.scopes for scope in required_scopes):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions"
                )
        
        user = await self.get_user_by_id(validation.user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found"
            )
        
        return user

# Usage in API endpoints
@app.post("/cv/generate")
async def generate_cv(
    request: CVGenerationRequest,
    current_user: User = Depends(auth_middleware.get_current_user)
):
    # User is authenticated and authorized
    return await cv_service.generate(request, current_user)

@app.get("/admin/users")
async def list_users(
    current_user: User = Depends(
        lambda: auth_middleware.get_current_user(required_scopes=["admin:read"])
    )
):
    # User must have admin:read scope
    return await user_service.list_all_users()
```

---

## 📊 **MONITORING & ANALYTICS**

### **Authentication Metrics**

```python
# Authentication Metrics Collection
class AuthMetrics:
    def __init__(self):
        self.login_attempts = Counter("auth_login_attempts_total", ["result", "method"])
        self.token_validations = Counter("auth_token_validations_total", ["result"])
        self.mfa_challenges = Counter("auth_mfa_challenges_total", ["method", "result"])
        self.session_duration = Histogram("auth_session_duration_seconds")
    
    def record_login_attempt(self, result: str, method: str = "password"):
        self.login_attempts.labels(result=result, method=method).inc()
    
    def record_token_validation(self, result: str):
        self.token_validations.labels(result=result).inc()
    
    def record_mfa_challenge(self, method: str, result: str):
        self.mfa_challenges.labels(method=method, result=result).inc()
    
    def record_session_end(self, duration_seconds: float):
        self.session_duration.observe(duration_seconds)
```

---

## ✅ **IMPLEMENTATION CHECKLIST**

### **OAuth 2.0 Implementation**
- [ ] Authorization Code Flow with PKCE
- [ ] Client Credentials Flow
- [ ] Refresh Token Flow
- [ ] Token introspection endpoint
- [ ] Token revocation endpoint
- [ ] JWKS endpoint for public keys

### **OpenID Connect Implementation**
- [ ] ID Token generation
- [ ] UserInfo endpoint
- [ ] Discovery document (/.well-known/openid_configuration)
- [ ] Standard claims support (profile, email, etc.)
- [ ] Nonce support for replay protection

### **Security Features**
- [ ] PKCE implementation
- [ ] State parameter validation
- [ ] Token binding
- [ ] Rate limiting on auth endpoints
- [ ] Brute force protection
- [ ] Session management

### **MFA Integration**
- [ ] TOTP support with QR code generation
- [ ] Email-based MFA
- [ ] SMS-based MFA (optional)
- [ ] Backup codes
- [ ] MFA enrollment flow

---

*This comprehensive authentication system ensures secure, standards-compliant user authentication with enterprise-grade features including MFA, PKCE, and comprehensive monitoring.*
