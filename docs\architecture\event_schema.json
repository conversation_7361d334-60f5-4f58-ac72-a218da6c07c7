{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "ImpactCV Event Schema", "description": "Event schema definitions for ImpactCV microservices event-driven architecture", "version": "1.0.0", "definitions": {"baseEvent": {"type": "object", "required": ["event_type", "event_id", "timestamp", "version"], "properties": {"event_type": {"type": "string", "description": "Type of event in dot notation (service.action.status)"}, "event_id": {"type": "string", "format": "uuid", "description": "Unique identifier for this event"}, "timestamp": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when event occurred"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Event schema version (semantic versioning)"}, "correlation_id": {"type": "string", "format": "uuid", "description": "Correlation ID for tracking related events"}, "user_id": {"type": "string", "format": "uuid", "description": "ID of the user who triggered this event"}, "session_id": {"type": "string", "format": "uuid", "description": "Session ID for request tracking"}, "source_service": {"type": "string", "description": "Name of the service that generated this event"}}}, "cvGenerationStarted": {"allOf": [{"$ref": "#/definitions/baseEvent"}, {"properties": {"event_type": {"const": "cv.generation.started"}, "payload": {"type": "object", "required": ["cv_id", "template_id", "input_documents"], "properties": {"cv_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the CV generation job"}, "template_id": {"type": "string", "format": "uuid", "description": "ID of the CV template to use"}, "input_documents": {"type": "array", "items": {"type": "object", "properties": {"document_id": {"type": "string", "format": "uuid"}, "filename": {"type": "string"}, "file_type": {"type": "string", "enum": ["pdf", "docx"]}, "file_size": {"type": "integer", "minimum": 0}}}, "minItems": 1, "maxItems": 5}, "preferences": {"type": "object", "properties": {"style": {"type": "string", "enum": ["professional", "creative", "minimal"]}, "color_scheme": {"type": "string", "enum": ["blue", "green", "red", "black"]}, "include_photo": {"type": "boolean"}}}, "priority": {"type": "string", "enum": ["low", "normal", "high", "urgent"], "default": "normal"}}}}}]}, "cvGenerationCompleted": {"allOf": [{"$ref": "#/definitions/baseEvent"}, {"properties": {"event_type": {"const": "cv.generation.completed"}, "payload": {"type": "object", "required": ["cv_id", "status", "processing_time"], "properties": {"cv_id": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["success", "failed"]}, "processing_time": {"type": "number", "description": "Processing time in seconds"}, "output_file": {"type": "object", "properties": {"file_id": {"type": "string", "format": "uuid"}, "filename": {"type": "string"}, "file_size": {"type": "integer"}, "download_url": {"type": "string", "format": "uri"}, "expires_at": {"type": "string", "format": "date-time"}}}, "quality_metrics": {"type": "object", "properties": {"overall_score": {"type": "number", "minimum": 0, "maximum": 1}, "ats_compatibility": {"type": "number", "minimum": 0, "maximum": 1}, "professional_tone": {"type": "number", "minimum": 0, "maximum": 1}, "content_completeness": {"type": "number", "minimum": 0, "maximum": 1}}}, "error_details": {"type": "object", "properties": {"error_code": {"type": "string"}, "error_message": {"type": "string"}, "stack_trace": {"type": "string"}}}}}}}]}, "dataProcessingCompleted": {"allOf": [{"$ref": "#/definitions/baseEvent"}, {"properties": {"event_type": {"const": "data.processing.completed"}, "payload": {"type": "object", "required": ["document_id", "processing_status"], "properties": {"document_id": {"type": "string", "format": "uuid"}, "processing_status": {"type": "string", "enum": ["success", "failed", "partial"]}, "extracted_data": {"type": "object", "properties": {"personal_info": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "location": {"type": "string"}}}, "experience": {"type": "array", "items": {"type": "object", "properties": {"company": {"type": "string"}, "position": {"type": "string"}, "start_date": {"type": "string", "format": "date"}, "end_date": {"type": "string", "format": "date"}, "description": {"type": "string"}}}}, "education": {"type": "array", "items": {"type": "object", "properties": {"institution": {"type": "string"}, "degree": {"type": "string"}, "field": {"type": "string"}, "graduation_date": {"type": "string", "format": "date"}}}}, "skills": {"type": "array", "items": {"type": "string"}}}}, "quality_metrics": {"type": "object", "properties": {"completeness_score": {"type": "number", "minimum": 0, "maximum": 1}, "accuracy_score": {"type": "number", "minimum": 0, "maximum": 1}, "consistency_score": {"type": "number", "minimum": 0, "maximum": 1}, "overall_quality": {"type": "number", "minimum": 0, "maximum": 1}}}, "pii_detection": {"type": "object", "properties": {"pii_detected": {"type": "boolean"}, "pii_types": {"type": "array", "items": {"type": "string", "enum": ["ssn", "credit_card", "passport", "driver_license"]}}, "anonymization_applied": {"type": "boolean"}}}, "validation_results": {"type": "object", "properties": {"format_validation": {"type": "boolean"}, "business_rules_validation": {"type": "boolean"}, "gdpr_compliance": {"type": "boolean"}}}}}}}]}, "ragEnhancementCompleted": {"allOf": [{"$ref": "#/definitions/baseEvent"}, {"properties": {"event_type": {"const": "rag.enhancement.completed"}, "payload": {"type": "object", "required": ["cv_id", "enhancement_status"], "properties": {"cv_id": {"type": "string", "format": "uuid"}, "enhancement_status": {"type": "string", "enum": ["success", "failed", "partial"]}, "enhanced_content": {"type": "object", "properties": {"sections": {"type": "array", "items": {"type": "object", "properties": {"section_type": {"type": "string"}, "original_content": {"type": "string"}, "enhanced_content": {"type": "string"}, "enhancement_score": {"type": "number", "minimum": 0, "maximum": 1}}}}}}, "rag_metrics": {"type": "object", "properties": {"retrieval_latency": {"type": "number"}, "generation_latency": {"type": "number"}, "relevance_score": {"type": "number", "minimum": 0, "maximum": 1}, "context_sources": {"type": "array", "items": {"type": "object", "properties": {"source": {"type": "string"}, "relevance_score": {"type": "number", "minimum": 0, "maximum": 1}}}}}}, "ai_safety_checks": {"type": "object", "properties": {"content_safety": {"type": "boolean"}, "bias_detection": {"type": "boolean"}, "factual_consistency": {"type": "boolean"}, "professional_appropriateness": {"type": "boolean"}}}}}}}]}, "userAuthenticated": {"allOf": [{"$ref": "#/definitions/baseEvent"}, {"properties": {"event_type": {"const": "user.authenticated"}, "payload": {"type": "object", "required": ["authentication_method", "success"], "properties": {"authentication_method": {"type": "string", "enum": ["password", "o<PERSON>h", "mfa", "api_key"]}, "success": {"type": "boolean"}, "ip_address": {"type": "string", "format": "ipv4"}, "user_agent": {"type": "string"}, "location": {"type": "object", "properties": {"country": {"type": "string"}, "city": {"type": "string"}, "coordinates": {"type": "object", "properties": {"latitude": {"type": "number"}, "longitude": {"type": "number"}}}}}, "risk_score": {"type": "number", "minimum": 0, "maximum": 1, "description": "Risk score for this authentication attempt"}, "failure_reason": {"type": "string", "enum": ["invalid_credentials", "account_locked", "mfa_failed", "rate_limited"]}}}}}]}, "systemHealthCheck": {"allOf": [{"$ref": "#/definitions/baseEvent"}, {"properties": {"event_type": {"const": "system.health.check"}, "payload": {"type": "object", "required": ["service_name", "health_status"], "properties": {"service_name": {"type": "string"}, "health_status": {"type": "string", "enum": ["healthy", "degraded", "unhealthy"]}, "metrics": {"type": "object", "properties": {"cpu_usage": {"type": "number", "minimum": 0, "maximum": 100}, "memory_usage": {"type": "number", "minimum": 0, "maximum": 100}, "disk_usage": {"type": "number", "minimum": 0, "maximum": 100}, "response_time": {"type": "number", "minimum": 0}, "error_rate": {"type": "number", "minimum": 0, "maximum": 100}}}, "dependencies": {"type": "array", "items": {"type": "object", "properties": {"service": {"type": "string"}, "status": {"type": "string", "enum": ["connected", "disconnected", "timeout"]}, "response_time": {"type": "number"}}}}}}}}]}}, "events": {"cv.generation.started": {"$ref": "#/definitions/cvGenerationStarted"}, "cv.generation.completed": {"$ref": "#/definitions/cvGenerationCompleted"}, "data.processing.completed": {"$ref": "#/definitions/dataProcessingCompleted"}, "rag.enhancement.completed": {"$ref": "#/definitions/ragEnhancementCompleted"}, "user.authenticated": {"$ref": "#/definitions/userAuthenticated"}, "system.health.check": {"$ref": "#/definitions/systemHealthCheck"}}, "examples": {"cv_generation_started_example": {"event_type": "cv.generation.started", "event_id": "123e4567-e89b-12d3-a456-426614174000", "timestamp": "2025-01-02T10:30:00Z", "version": "1.0.0", "correlation_id": "987fcdeb-51a2-43d1-b789-123456789abc", "user_id": "user-123e4567-e89b-12d3-a456-426614174000", "session_id": "session-123e4567-e89b-12d3-a456-426614174000", "source_service": "cv-generation-service", "payload": {"cv_id": "cv-123e4567-e89b-12d3-a456-426614174000", "template_id": "template-professional-modern", "input_documents": [{"document_id": "doc-123e4567-e89b-12d3-a456-426614174000", "filename": "resume.pdf", "file_type": "pdf", "file_size": 1024000}], "preferences": {"style": "professional", "color_scheme": "blue", "include_photo": false}, "priority": "normal"}}}}