// Parallax service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ParallaxService {
    constructor() {
        this.elements = new Map();
        this.subscribers = new Set();
        this.initialize();
    }

    /**
     * Initialize the parallax service
     */
    initialize() {
        // Add scroll event listener
        window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        window.addEventListener('resize', this.handleResize.bind(this), { passive: true });
    }

    /**
     * Subscribe to parallax events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of parallax events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in parallax subscriber:', error);
            }
        });
    }

    /**
     * Register a parallax element
     * @param {string} id - The element ID
     * @param {Object} options - The parallax options
     */
    registerElement(id, options = {}) {
        const element = document.getElementById(id);
        if (!element) {
            console.error(`Element ${id} not found`);
            return;
        }

        this.elements.set(id, {
            ...options,
            element,
            originalTransform: element.style.transform,
            originalPosition: element.style.position,
            originalTop: element.style.top,
            originalLeft: element.style.left,
            originalWidth: element.style.width,
            originalHeight: element.style.height,
            originalZIndex: element.style.zIndex,
            originalOpacity: element.style.opacity,
            originalScale: element.style.transform ? parseFloat(element.style.transform.match(/scale\((.*?)\)/)?.[1] || 1) : 1,
            originalRotate: element.style.transform ? parseFloat(element.style.transform.match(/rotate\((.*?)deg\)/)?.[1] || 0) : 0,
            originalTranslateX: element.style.transform ? parseFloat(element.style.transform.match(/translateX\((.*?)px\)/)?.[1] || 0) : 0,
            originalTranslateY: element.style.transform ? parseFloat(element.style.transform.match(/translateY\((.*?)px\)/)?.[1] || 0) : 0,
            originalTranslateZ: element.style.transform ? parseFloat(element.style.transform.match(/translateZ\((.*?)px\)/)?.[1] || 0) : 0,
            originalPerspective: element.style.perspective || '1000px',
            originalTransformStyle: element.style.transformStyle || 'flat',
            originalBackfaceVisibility: element.style.backfaceVisibility || 'visible',
            originalTransformOrigin: element.style.transformOrigin || 'center center',
            originalTransition: element.style.transition,
            originalWillChange: element.style.willChange,
            originalFilter: element.style.filter,
            originalBackdropFilter: element.style.backdropFilter,
            originalMixBlendMode: element.style.mixBlendMode,
            originalIsolation: element.style.isolation,
            originalClipPath: element.style.clipPath,
            originalMask: element.style.mask,
            originalMaskImage: element.style.maskImage,
            originalMaskSize: element.style.maskSize,
            originalMaskPosition: element.style.maskPosition,
            originalMaskRepeat: element.style.maskRepeat,
            originalMaskOrigin: element.style.maskOrigin,
            originalMaskClip: element.style.maskClip,
            originalMaskComposite: element.style.maskComposite,
            originalMaskMode: element.style.maskMode,
            originalMaskType: element.style.maskType,
            originalMaskBorder: element.style.maskBorder,
            originalMaskBorderSource: element.style.maskBorderSource,
            originalMaskBorderSlice: element.style.maskBorderSlice,
            originalMaskBorderWidth: element.style.maskBorderWidth,
            originalMaskBorderOutset: element.style.maskBorderOutset,
            originalMaskBorderRepeat: element.style.maskBorderRepeat,
            originalMaskBorderMode: element.style.maskBorderMode,
            originalMaskClipPath: element.style.maskClipPath,
            originalMaskImage: element.style.maskImage,
            originalMaskSize: element.style.maskSize,
            originalMaskPosition: element.style.maskPosition,
            originalMaskRepeat: element.style.maskRepeat,
            originalMaskOrigin: element.style.maskOrigin,
            originalMaskClip: element.style.maskClip,
            originalMaskComposite: element.style.maskComposite,
            originalMaskMode: element.style.maskMode,
            originalMaskType: element.style.maskType,
            originalMaskBorder: element.style.maskBorder,
            originalMaskBorderSource: element.style.maskBorderSource,
            originalMaskBorderSlice: element.style.maskBorderSlice,
            originalMaskBorderWidth: element.style.maskBorderWidth,
            originalMaskBorderOutset: element.style.maskBorderOutset,
            originalMaskBorderRepeat: element.style.maskBorderRepeat,
            originalMaskBorderMode: element.style.maskBorderMode,
            originalMaskClipPath: element.style.maskClipPath,
        });

        // Initial update
        this.updateElement(id);
    }

    /**
     * Handle scroll events
     */
    handleScroll() {
        return measurePerformance('parallax_scroll', () => {
            this.elements.forEach((parallax, id) => {
                this.updateElement(id);
            });
        });
    }

    /**
     * Handle resize events
     */
    handleResize() {
        return measurePerformance('parallax_resize', () => {
            this.elements.forEach((parallax, id) => {
                this.updateElement(id);
            });
        });
    }

    /**
     * Update a parallax element
     * @param {string} id - The element ID
     */
    updateElement(id) {
        return measurePerformance('parallax_update', () => {
            const parallax = this.elements.get(id);
            if (!parallax) {
                return;
            }

            const { element, options } = parallax;
            const rect = element.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

            // Calculate progress
            const progress = {
                x: (rect.left + scrollLeft) / viewportWidth,
                y: (rect.top + scrollTop) / viewportHeight,
                scroll: scrollTop / (document.documentElement.scrollHeight - viewportHeight),
                viewport: {
                    x: rect.left / viewportWidth,
                    y: rect.top / viewportHeight,
                    width: rect.width / viewportWidth,
                    height: rect.height / viewportHeight,
                },
            };

            // Apply transforms
            const transforms = [];

            // Position
            if (options.position) {
                const { x, y, z } = options.position;
                if (x) {
                    transforms.push(`translateX(${x * progress.x}px)`);
                }
                if (y) {
                    transforms.push(`translateY(${y * progress.y}px)`);
                }
                if (z) {
                    transforms.push(`translateZ(${z * progress.scroll}px)`);
                }
            }

            // Scale
            if (options.scale) {
                const { x, y, z } = options.scale;
                if (x) {
                    transforms.push(`scaleX(${1 + x * progress.x})`);
                }
                if (y) {
                    transforms.push(`scaleY(${1 + y * progress.y})`);
                }
                if (z) {
                    transforms.push(`scaleZ(${1 + z * progress.scroll})`);
                }
            }

            // Rotate
            if (options.rotate) {
                const { x, y, z } = options.rotate;
                if (x) {
                    transforms.push(`rotateX(${x * progress.x}deg)`);
                }
                if (y) {
                    transforms.push(`rotateY(${y * progress.y}deg)`);
                }
                if (z) {
                    transforms.push(`rotateZ(${z * progress.scroll}deg)`);
                }
            }

            // Skew
            if (options.skew) {
                const { x, y } = options.skew;
                if (x) {
                    transforms.push(`skewX(${x * progress.x}deg)`);
                }
                if (y) {
                    transforms.push(`skewY(${y * progress.y}deg)`);
                }
            }

            // Apply transforms
            if (transforms.length > 0) {
                element.style.transform = transforms.join(' ');
            }

            // Apply opacity
            if (options.opacity) {
                const { start, end } = options.opacity;
                const opacity = start + (end - start) * progress.scroll;
                element.style.opacity = opacity;
            }

            // Apply filter
            if (options.filter) {
                const { blur, brightness, contrast, grayscale, hueRotate, invert, saturate, sepia } = options.filter;
                const filters = [];

                if (blur) {
                    filters.push(`blur(${blur * progress.scroll}px)`);
                }
                if (brightness) {
                    filters.push(`brightness(${brightness * progress.scroll})`);
                }
                if (contrast) {
                    filters.push(`contrast(${contrast * progress.scroll})`);
                }
                if (grayscale) {
                    filters.push(`grayscale(${grayscale * progress.scroll})`);
                }
                if (hueRotate) {
                    filters.push(`hue-rotate(${hueRotate * progress.scroll}deg)`);
                }
                if (invert) {
                    filters.push(`invert(${invert * progress.scroll})`);
                }
                if (saturate) {
                    filters.push(`saturate(${saturate * progress.scroll})`);
                }
                if (sepia) {
                    filters.push(`sepia(${sepia * progress.scroll})`);
                }

                if (filters.length > 0) {
                    element.style.filter = filters.join(' ');
                }
            }

            // Apply backdrop filter
            if (options.backdropFilter) {
                const { blur, brightness, contrast, grayscale, hueRotate, invert, saturate, sepia } = options.backdropFilter;
                const filters = [];

                if (blur) {
                    filters.push(`blur(${blur * progress.scroll}px)`);
                }
                if (brightness) {
                    filters.push(`brightness(${brightness * progress.scroll})`);
                }
                if (contrast) {
                    filters.push(`contrast(${contrast * progress.scroll})`);
                }
                if (grayscale) {
                    filters.push(`grayscale(${grayscale * progress.scroll})`);
                }
                if (hueRotate) {
                    filters.push(`hue-rotate(${hueRotate * progress.scroll}deg)`);
                }
                if (invert) {
                    filters.push(`invert(${invert * progress.scroll})`);
                }
                if (saturate) {
                    filters.push(`saturate(${saturate * progress.scroll})`);
                }
                if (sepia) {
                    filters.push(`sepia(${sepia * progress.scroll})`);
                }

                if (filters.length > 0) {
                    element.style.backdropFilter = filters.join(' ');
                }
            }

            // Apply clip path
            if (options.clipPath) {
                const { type, value } = options.clipPath;
                if (type === 'circle') {
                    const { radius, x, y } = value;
                    const r = radius * progress.scroll;
                    const cx = x * progress.x;
                    const cy = y * progress.y;
                    element.style.clipPath = `circle(${r}px at ${cx}px ${cy}px)`;
                } else if (type === 'ellipse') {
                    const { rx, ry, x, y } = value;
                    const rxx = rx * progress.scroll;
                    const ryy = ry * progress.scroll;
                    const cx = x * progress.x;
                    const cy = y * progress.y;
                    element.style.clipPath = `ellipse(${rxx}px ${ryy}px at ${cx}px ${cy}px)`;
                } else if (type === 'inset') {
                    const { top, right, bottom, left } = value;
                    const t = top * progress.scroll;
                    const r = right * progress.scroll;
                    const b = bottom * progress.scroll;
                    const l = left * progress.scroll;
                    element.style.clipPath = `inset(${t}px ${r}px ${b}px ${l}px)`;
                } else if (type === 'polygon') {
                    const { points } = value;
                    const p = points.map(point => {
                        const [x, y] = point;
                        return `${x * progress.x}px ${y * progress.y}px`;
                    });
                    element.style.clipPath = `polygon(${p.join(', ')})`;
                }
            }

            // Apply mask
            if (options.mask) {
                const { type, value } = options.mask;
                if (type === 'circle') {
                    const { radius, x, y } = value;
                    const r = radius * progress.scroll;
                    const cx = x * progress.x;
                    const cy = y * progress.y;
                    element.style.mask = `circle(${r}px at ${cx}px ${cy}px)`;
                } else if (type === 'ellipse') {
                    const { rx, ry, x, y } = value;
                    const rxx = rx * progress.scroll;
                    const ryy = ry * progress.scroll;
                    const cx = x * progress.x;
                    const cy = y * progress.y;
                    element.style.mask = `ellipse(${rxx}px ${ryy}px at ${cx}px ${cy}px)`;
                } else if (type === 'inset') {
                    const { top, right, bottom, left } = value;
                    const t = top * progress.scroll;
                    const r = right * progress.scroll;
                    const b = bottom * progress.scroll;
                    const l = left * progress.scroll;
                    element.style.mask = `inset(${t}px ${r}px ${b}px ${l}px)`;
                } else if (type === 'polygon') {
                    const { points } = value;
                    const p = points.map(point => {
                        const [x, y] = point;
                        return `${x * progress.x}px ${y * progress.y}px`;
                    });
                    element.style.mask = `polygon(${p.join(', ')})`;
                }
            }

            if (parallax.onUpdate) {
                parallax.onUpdate({
                    element,
                    progress,
                });
            }

            this.notifySubscribers('update', { id, progress });
        });
    }

    /**
     * Reset a parallax element
     * @param {string} id - The element ID
     */
    resetElement(id) {
        return measurePerformance('parallax_reset', () => {
            const parallax = this.elements.get(id);
            if (!parallax) {
                return;
            }

            const { element } = parallax;

            // Reset styles
            element.style.transform = parallax.originalTransform;
            element.style.position = parallax.originalPosition;
            element.style.top = parallax.originalTop;
            element.style.left = parallax.originalLeft;
            element.style.width = parallax.originalWidth;
            element.style.height = parallax.originalHeight;
            element.style.zIndex = parallax.originalZIndex;
            element.style.opacity = parallax.originalOpacity;
            element.style.perspective = parallax.originalPerspective;
            element.style.transformStyle = parallax.originalTransformStyle;
            element.style.backfaceVisibility = parallax.originalBackfaceVisibility;
            element.style.transformOrigin = parallax.originalTransformOrigin;
            element.style.transition = parallax.originalTransition;
            element.style.willChange = parallax.originalWillChange;
            element.style.filter = parallax.originalFilter;
            element.style.backdropFilter = parallax.originalBackdropFilter;
            element.style.mixBlendMode = parallax.originalMixBlendMode;
            element.style.isolation = parallax.originalIsolation;
            element.style.clipPath = parallax.originalClipPath;
            element.style.mask = parallax.originalMask;
            element.style.maskImage = parallax.originalMaskImage;
            element.style.maskSize = parallax.originalMaskSize;
            element.style.maskPosition = parallax.originalMaskPosition;
            element.style.maskRepeat = parallax.originalMaskRepeat;
            element.style.maskOrigin = parallax.originalMaskOrigin;
            element.style.maskClip = parallax.originalMaskClip;
            element.style.maskComposite = parallax.originalMaskComposite;
            element.style.maskMode = parallax.originalMaskMode;
            element.style.maskType = parallax.originalMaskType;
            element.style.maskBorder = parallax.originalMaskBorder;
            element.style.maskBorderSource = parallax.originalMaskBorderSource;
            element.style.maskBorderSlice = parallax.originalMaskBorderSlice;
            element.style.maskBorderWidth = parallax.originalMaskBorderWidth;
            element.style.maskBorderOutset = parallax.originalMaskBorderOutset;
            element.style.maskBorderRepeat = parallax.originalMaskBorderRepeat;
            element.style.maskBorderMode = parallax.originalMaskBorderMode;
            element.style.maskClipPath = parallax.originalMaskClipPath;

            if (parallax.onReset) {
                parallax.onReset({
                    element,
                });
            }

            this.notifySubscribers('reset', { id });
        });
    }

    /**
     * Get element data
     * @param {string} id - The element ID
     * @returns {Object} The element data
     */
    getElementData(id) {
        return this.elements.get(id);
    }

    /**
     * Update element data
     * @param {string} id - The element ID
     * @param {Object} data - The new element data
     */
    updateElementData(id, data) {
        const parallax = this.elements.get(id);
        if (parallax) {
            Object.assign(parallax, data);
            this.updateElement(id);
        }
    }
}

// Create and export a singleton instance
const parallaxService = new ParallaxService();
export default parallaxService; 