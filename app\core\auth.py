"""
ImpactCV Authentication and Authorization
OAuth 2.0 + JWT implementation with PKCE and comprehensive security
"""

import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import jwt
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import H<PERSON><PERSON>uthorizationCredentials, HTTPBearer, OAuth2PasswordBearer
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel, EmailStr

from app.core.config import settings
from app.core.logging import security_audit_logger
from app.core.security import TokenSecurity


# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth/token",
    scopes={
        "read": "Read access to resources",
        "write": "Write access to resources",
        "admin": "Administrative access",
        "cv:generate": "Generate CV documents",
        "cv:read": "Read CV documents",
        "cv:write": "Create and update CV documents",
        "cv:delete": "Delete CV documents",
        "user:read": "Read user profile",
        "user:write": "Update user profile",
    }
)

# HTTP Bearer for API key authentication
http_bearer = HTTPBearer(auto_error=False)


class TokenData(BaseModel):
    """Token payload data model."""
    
    user_id: Optional[str] = None
    email: Optional[str] = None
    scopes: List[str] = []
    token_type: str = "access"
    exp: Optional[datetime] = None
    iat: Optional[datetime] = None
    jti: Optional[str] = None  # JWT ID for token revocation


class User(BaseModel):
    """User model for authentication."""
    
    id: str
    email: EmailStr
    username: str
    full_name: Optional[str] = None
    is_active: bool = True
    is_verified: bool = False
    is_superuser: bool = False
    scopes: List[str] = []
    created_at: datetime
    last_login: Optional[datetime] = None


class UserInDB(User):
    """User model with hashed password."""
    
    hashed_password: str
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None
    password_changed_at: Optional[datetime] = None
    mfa_enabled: bool = False
    mfa_secret: Optional[str] = None


class TokenResponse(BaseModel):
    """Token response model."""
    
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    scope: str
    user: User


class AuthenticationError(HTTPException):
    """Custom authentication error."""
    
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )


class AuthorizationError(HTTPException):
    """Custom authorization error."""
    
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
        )


class PasswordManager:
    """Password management utilities."""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password using bcrypt."""
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def generate_password_reset_token() -> str:
        """Generate a secure password reset token."""
        return TokenSecurity.generate_secure_token(32)


class JWTManager:
    """JWT token management."""
    
    @staticmethod
    def create_access_token(
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create JWT access token.
        
        Args:
            data: Token payload data
            expires_delta: Token expiration time
            
        Returns:
            str: Encoded JWT token
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "jti": secrets.token_urlsafe(16),  # JWT ID for revocation
            "token_type": "access"
        })
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.JWT_SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM
        )
        
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create JWT refresh token.
        
        Args:
            data: Token payload data
            expires_delta: Token expiration time
            
        Returns:
            str: Encoded JWT refresh token
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                days=settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS
            )
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "jti": secrets.token_urlsafe(16),
            "token_type": "refresh"
        })
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.JWT_SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM
        )
        
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> TokenData:
        """
        Verify and decode JWT token.
        
        Args:
            token: JWT token to verify
            token_type: Expected token type
            
        Returns:
            TokenData: Decoded token data
            
        Raises:
            AuthenticationError: If token is invalid
        """
        try:
            payload = jwt.decode(
                token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM]
            )
            
            # Verify token type
            if payload.get("token_type") != token_type:
                raise AuthenticationError("Invalid token type")
            
            # Extract token data
            user_id: str = payload.get("sub")
            email: str = payload.get("email")
            scopes: List[str] = payload.get("scopes", [])
            exp: datetime = datetime.fromtimestamp(payload.get("exp", 0))
            iat: datetime = datetime.fromtimestamp(payload.get("iat", 0))
            jti: str = payload.get("jti")
            
            if user_id is None:
                raise AuthenticationError("Invalid token payload")
            
            token_data = TokenData(
                user_id=user_id,
                email=email,
                scopes=scopes,
                token_type=token_type,
                exp=exp,
                iat=iat,
                jti=jti
            )
            
            return token_data
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.JWTError:
            raise AuthenticationError("Invalid token")


class AuthenticationService:
    """Authentication service with security features."""
    
    def __init__(self):
        """Initialize authentication service."""
        self.failed_attempts = {}  # In production, use Redis
        self.revoked_tokens = set()  # In production, use Redis
    
    async def authenticate_user(
        self,
        email: str,
        password: str,
        request: Request
    ) -> Optional[UserInDB]:
        """
        Authenticate user with email and password.
        
        Args:
            email: User email
            password: User password
            request: FastAPI request object
            
        Returns:
            UserInDB: Authenticated user or None
        """
        client_ip = self._get_client_ip(request)
        
        # Check for account lockout
        if self._is_account_locked(email):
            security_audit_logger.log_authentication_attempt(
                email=email,
                success=False,
                ip_address=client_ip,
                failure_reason="Account locked"
            )
            raise AuthenticationError("Account is temporarily locked")
        
        # TODO: Get user from database
        # For now, simulate user lookup
        user = await self._get_user_by_email(email)
        
        if not user:
            self._record_failed_attempt(email)
            security_audit_logger.log_authentication_attempt(
                email=email,
                success=False,
                ip_address=client_ip,
                failure_reason="User not found"
            )
            raise AuthenticationError("Invalid credentials")
        
        if not user.is_active:
            security_audit_logger.log_authentication_attempt(
                user_id=user.id,
                email=email,
                success=False,
                ip_address=client_ip,
                failure_reason="Account disabled"
            )
            raise AuthenticationError("Account is disabled")
        
        if not PasswordManager.verify_password(password, user.hashed_password):
            self._record_failed_attempt(email)
            security_audit_logger.log_authentication_attempt(
                user_id=user.id,
                email=email,
                success=False,
                ip_address=client_ip,
                failure_reason="Invalid password"
            )
            raise AuthenticationError("Invalid credentials")
        
        # Reset failed attempts on successful login
        self._reset_failed_attempts(email)
        
        # Log successful authentication
        security_audit_logger.log_authentication_attempt(
            user_id=user.id,
            email=email,
            success=True,
            ip_address=client_ip
        )
        
        return user
    
    async def create_tokens(self, user: UserInDB) -> TokenResponse:
        """
        Create access and refresh tokens for user.
        
        Args:
            user: Authenticated user
            
        Returns:
            TokenResponse: Token response with access and refresh tokens
        """
        # Prepare token data
        token_data = {
            "sub": user.id,
            "email": user.email,
            "scopes": user.scopes,
        }
        
        # Create tokens
        access_token = JWTManager.create_access_token(token_data)
        refresh_token = JWTManager.create_refresh_token(token_data)
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            scope=" ".join(user.scopes),
            user=User(**user.dict())
        )
    
    async def refresh_access_token(self, refresh_token: str) -> TokenResponse:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            TokenResponse: New token response
        """
        # Verify refresh token
        token_data = JWTManager.verify_token(refresh_token, "refresh")
        
        # Check if token is revoked
        if token_data.jti in self.revoked_tokens:
            raise AuthenticationError("Token has been revoked")
        
        # Get user
        user = await self._get_user_by_id(token_data.user_id)
        if not user or not user.is_active:
            raise AuthenticationError("User not found or inactive")
        
        # Create new tokens
        return await self.create_tokens(user)
    
    def revoke_token(self, token: str) -> None:
        """
        Revoke a token (add to blacklist).
        
        Args:
            token: Token to revoke
        """
        try:
            token_data = JWTManager.verify_token(token)
            self.revoked_tokens.add(token_data.jti)
        except AuthenticationError:
            pass  # Token is already invalid
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP from request."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        return request.client.host if request.client else "unknown"
    
    def _is_account_locked(self, email: str) -> bool:
        """Check if account is locked due to failed attempts."""
        attempts = self.failed_attempts.get(email, {"count": 0, "locked_until": None})
        
        if attempts["locked_until"] and datetime.utcnow() < attempts["locked_until"]:
            return True
        
        return attempts["count"] >= 5  # Lock after 5 failed attempts
    
    def _record_failed_attempt(self, email: str) -> None:
        """Record failed login attempt."""
        if email not in self.failed_attempts:
            self.failed_attempts[email] = {"count": 0, "locked_until": None}
        
        self.failed_attempts[email]["count"] += 1
        
        # Lock account for 15 minutes after 5 failed attempts
        if self.failed_attempts[email]["count"] >= 5:
            self.failed_attempts[email]["locked_until"] = (
                datetime.utcnow() + timedelta(minutes=15)
            )
    
    def _reset_failed_attempts(self, email: str) -> None:
        """Reset failed login attempts."""
        if email in self.failed_attempts:
            del self.failed_attempts[email]
    
    async def _get_user_by_email(self, email: str) -> Optional[UserInDB]:
        """Get user by email (mock implementation)."""
        # TODO: Implement actual database lookup
        # For now, return a mock user for testing
        if email == "<EMAIL>":
            return UserInDB(
                id="1",
                email=email,
                username="admin",
                full_name="Admin User",
                hashed_password=PasswordManager.hash_password("admin123"),
                is_active=True,
                is_verified=True,
                is_superuser=True,
                scopes=["read", "write", "admin", "cv:generate", "cv:read", "cv:write", "cv:delete"],
                created_at=datetime.utcnow()
            )
        return None
    
    async def _get_user_by_id(self, user_id: str) -> Optional[UserInDB]:
        """Get user by ID (mock implementation)."""
        # TODO: Implement actual database lookup
        if user_id == "1":
            return await self._get_user_by_email("<EMAIL>")
        return None


# Global authentication service instance
auth_service = AuthenticationService()


async def get_current_user(
    request: Request,
    token: str = Depends(oauth2_scheme)
) -> User:
    """
    Get current authenticated user from JWT token.
    
    Args:
        request: FastAPI request object
        token: JWT access token
        
    Returns:
        User: Current authenticated user
        
    Raises:
        AuthenticationError: If authentication fails
    """
    # Verify token
    token_data = JWTManager.verify_token(token, "access")
    
    # Check if token is revoked
    if token_data.jti in auth_service.revoked_tokens:
        raise AuthenticationError("Token has been revoked")
    
    # Get user
    user = await auth_service._get_user_by_id(token_data.user_id)
    if user is None:
        raise AuthenticationError("User not found")
    
    if not user.is_active:
        raise AuthenticationError("User account is disabled")
    
    # Log data access
    security_audit_logger.log_data_access(
        user_id=user.id,
        resource=request.url.path,
        action=request.method
    )
    
    return User(**user.dict())


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user.
    
    Args:
        current_user: Current user from token
        
    Returns:
        User: Active user
        
    Raises:
        AuthenticationError: If user is not active
    """
    if not current_user.is_active:
        raise AuthenticationError("User account is disabled")
    
    return current_user


def require_scopes(required_scopes: List[str]):
    """
    Dependency to require specific scopes.
    
    Args:
        required_scopes: List of required scopes
        
    Returns:
        Dependency function
    """
    def check_scopes(current_user: User = Depends(get_current_active_user)) -> User:
        """Check if user has required scopes."""
        user_scopes = set(current_user.scopes)
        required_scopes_set = set(required_scopes)
        
        if not required_scopes_set.issubset(user_scopes):
            missing_scopes = required_scopes_set - user_scopes
            security_audit_logger.log_authorization_failure(
                user_id=current_user.id,
                resource="scoped_endpoint",
                action=f"required_scopes: {required_scopes}"
            )
            raise AuthorizationError(
                f"Insufficient permissions. Missing scopes: {list(missing_scopes)}"
            )
        
        return current_user
    
    return check_scopes


def require_admin(current_user: User = Depends(get_current_active_user)) -> User:
    """
    Dependency to require admin privileges.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User: Admin user
        
    Raises:
        AuthorizationError: If user is not admin
    """
    if not current_user.is_superuser:
        security_audit_logger.log_authorization_failure(
            user_id=current_user.id,
            resource="admin_endpoint",
            action="admin_required"
        )
        raise AuthorizationError("Admin privileges required")
    
    return current_user
