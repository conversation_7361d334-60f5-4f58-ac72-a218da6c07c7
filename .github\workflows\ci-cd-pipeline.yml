name: 🚀 ImpactCV CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: impactcv

jobs:
  # ============================================================================
  # SECURITY GATE 1: CODE QUALITY & SECRETS SCANNING
  # ============================================================================
  code-quality:
    name: 🔍 Code Quality & Security Scan
    runs-on: ubuntu-latest
    outputs:
      quality-gate: ${{ steps.quality-check.outputs.passed }}
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for SonarQube
      
      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
      
      - name: 📦 Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt
      
      - name: 🔐 Secrets Scanning
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: ${{ github.event.repository.default_branch }}
          head: HEAD
          extra_args: --debug --only-verified
      
      - name: 🧹 Code Linting
        run: |
          flake8 app/ tests/ --count --select=E9,F63,F7,F82 --show-source --statistics
          flake8 app/ tests/ --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
      
      - name: 🎨 Code Formatting Check
        run: |
          black --check app/ tests/
          isort --check-only app/ tests/
      
      - name: 🔒 Security Linting (Bandit)
        run: |
          bandit -r app/ -f json -o bandit-report.json
          bandit -r app/ -ll
      
      - name: 📊 Type Checking
        run: |
          mypy app/ --ignore-missing-imports
      
      - name: ✅ Quality Gate Check
        id: quality-check
        run: |
          echo "Quality checks completed"
          echo "passed=true" >> $GITHUB_OUTPUT
      
      - name: 📤 Upload Security Reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            bandit-report.json
            .trufflehog-report.json

  # ============================================================================
  # SECURITY GATE 2: DEPENDENCY VULNERABILITY SCANNING
  # ============================================================================
  dependency-security:
    name: 🛡️ Dependency Security Scan
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
      
      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
      
      - name: 📦 Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install safety pip-audit
      
      - name: 🔍 Safety Check
        run: |
          safety check --json --output safety-report.json || true
          safety check
      
      - name: 🔍 Pip Audit
        run: |
          pip-audit --format=json --output=pip-audit-report.json || true
          pip-audit
      
      - name: 🔍 Snyk Security Scan
        uses: snyk/actions/python@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --json-file-output=snyk-report.json
        continue-on-error: true
      
      - name: 📤 Upload Dependency Reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: dependency-reports
          path: |
            safety-report.json
            pip-audit-report.json
            snyk-report.json

  # ============================================================================
  # TESTING GATE: UNIT & INTEGRATION TESTS
  # ============================================================================
  testing:
    name: 🧪 Testing Suite
    runs-on: ubuntu-latest
    needs: code-quality
    
    strategy:
      matrix:
        python-version: ['3.11', '3.12']
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_impactcv
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
      
      - name: 🐍 Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'
      
      - name: 📦 Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
      
      - name: 🧪 Run Unit Tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_impactcv
          REDIS_URL: redis://localhost:6379/0
          ENVIRONMENT: testing
        run: |
          pytest tests/unit/ -v --cov=app --cov-report=xml --cov-report=html --cov-fail-under=90
      
      - name: 🔗 Run Integration Tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_impactcv
          REDIS_URL: redis://localhost:6379/0
          ENVIRONMENT: testing
        run: |
          pytest tests/integration/ -v --maxfail=5
      
      - name: 📊 Upload Coverage Reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella
      
      - name: 📤 Upload Test Reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-reports-${{ matrix.python-version }}
          path: |
            htmlcov/
            pytest-report.xml
            coverage.xml

  # ============================================================================
  # CONTAINER SECURITY GATE
  # ============================================================================
  container-security:
    name: 🐳 Container Security Scan
    runs-on: ubuntu-latest
    needs: [code-quality, dependency-security]
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
      
      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: 🏗️ Build Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: false
          tags: ${{ env.IMAGE_NAME }}:test
          cache-from: type=gha
          cache-to: type=gha,mode=max
      
      - name: 🔍 Trivy Container Scan
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: '${{ env.IMAGE_NAME }}:test'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: 📤 Upload Trivy Results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'
      
      - name: 🔍 Docker Scout Scan
        uses: docker/scout-action@v1
        with:
          command: cves
          image: ${{ env.IMAGE_NAME }}:test
          only-severities: critical,high
          exit-code: true

  # ============================================================================
  # BUILD & PACKAGE
  # ============================================================================
  build:
    name: 🏗️ Build & Package
    runs-on: ubuntu-latest
    needs: [testing, container-security]
    if: github.event_name == 'push' || github.event_name == 'release'
    
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
      
      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: 🏷️ Extract Metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-
      
      - name: 🏗️ Build and Push
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # ============================================================================
  # DEPLOYMENT (STAGING)
  # ============================================================================
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
      
      - name: 🚀 Deploy to Staging
        run: |
          echo "Deploying to staging environment..."
          echo "Image: ${{ needs.build.outputs.image-tag }}"
          echo "Digest: ${{ needs.build.outputs.image-digest }}"
          # Add actual deployment commands here
      
      - name: 🧪 Run Smoke Tests
        run: |
          echo "Running smoke tests against staging..."
          # Add smoke test commands here
      
      - name: 📊 Update Deployment Status
        run: |
          echo "Staging deployment completed successfully"

  # ============================================================================
  # DEPLOYMENT (PRODUCTION)
  # ============================================================================
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'release'
    environment: production
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
      
      - name: 🚀 Deploy to Production
        run: |
          echo "Deploying to production environment..."
          echo "Image: ${{ needs.build.outputs.image-tag }}"
          echo "Digest: ${{ needs.build.outputs.image-digest }}"
          # Add actual deployment commands here
      
      - name: 🧪 Run Health Checks
        run: |
          echo "Running production health checks..."
          # Add health check commands here
      
      - name: 📊 Update Deployment Status
        run: |
          echo "Production deployment completed successfully"
      
      - name: 📢 Notify Stakeholders
        run: |
          echo "Notifying stakeholders of successful deployment..."
          # Add notification commands here

  # ============================================================================
  # COMPLIANCE & REPORTING
  # ============================================================================
  compliance-report:
    name: 📋 Compliance Report
    runs-on: ubuntu-latest
    needs: [code-quality, dependency-security, testing, container-security]
    if: always()
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
      
      - name: 📥 Download All Artifacts
        uses: actions/download-artifact@v3
      
      - name: 📊 Generate Compliance Report
        run: |
          echo "Generating comprehensive compliance report..."
          python scripts/generate_compliance_report.py \
            --security-reports security-reports/ \
            --dependency-reports dependency-reports/ \
            --test-reports test-reports-*/ \
            --output compliance-report.json
      
      - name: 📤 Upload Compliance Report
        uses: actions/upload-artifact@v3
        with:
          name: compliance-report
          path: compliance-report.json
      
      - name: 📊 Update Compliance Dashboard
        run: |
          echo "Updating compliance dashboard..."
          # Add dashboard update commands here
