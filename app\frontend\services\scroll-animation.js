// Scroll animation service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ScrollAnimationService {
    constructor() {
        this.elements = new Map();
        this.subscribers = new Set();
        this.initialize();
    }

    /**
     * Initialize the scroll animation service
     */
    initialize() {
        // Create intersection observer
        this.observer = new IntersectionObserver(
            this.handleIntersection.bind(this),
            {
                root: null,
                rootMargin: '0px',
                threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1],
            }
        );

        // Add scroll event listener
        window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        window.addEventListener('resize', this.handleResize.bind(this), { passive: true });
    }

    /**
     * Subscribe to scroll animation events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of scroll animation events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in scroll animation subscriber:', error);
            }
        });
    }

    /**
     * Register a scroll animation element
     * @param {string} id - The element ID
     * @param {Object} options - The scroll animation options
     */
    registerElement(id, options = {}) {
        const element = document.getElementById(id);
        if (!element) {
            console.error(`Element ${id} not found`);
            return;
        }

        // Observe element
        this.observer.observe(element);

        this.elements.set(id, {
            ...options,
            element,
            originalTransform: element.style.transform,
            originalOpacity: element.style.opacity,
            originalFilter: element.style.filter,
            originalBackdropFilter: element.style.backdropFilter,
            originalClipPath: element.style.clipPath,
            originalMask: element.style.mask,
            originalTransition: element.style.transition,
            originalWillChange: element.style.willChange,
            originalMixBlendMode: element.style.mixBlendMode,
            originalIsolation: element.style.isolation,
            originalPerspective: element.style.perspective,
            originalTransformStyle: element.style.transformStyle,
            originalBackfaceVisibility: element.style.backfaceVisibility,
            originalTransformOrigin: element.style.transformOrigin,
            originalFilter: element.style.filter,
            originalBackdropFilter: element.style.backdropFilter,
            originalMixBlendMode: element.style.mixBlendMode,
            originalIsolation: element.style.isolation,
            originalClipPath: element.style.clipPath,
            originalMask: element.style.mask,
            originalMaskImage: element.style.maskImage,
            originalMaskSize: element.style.maskSize,
            originalMaskPosition: element.style.maskPosition,
            originalMaskRepeat: element.style.maskRepeat,
            originalMaskOrigin: element.style.maskOrigin,
            originalMaskClip: element.style.maskClip,
            originalMaskComposite: element.style.maskComposite,
            originalMaskMode: element.style.maskMode,
            originalMaskType: element.style.maskType,
            originalMaskBorder: element.style.maskBorder,
            originalMaskBorderSource: element.style.maskBorderSource,
            originalMaskBorderSlice: element.style.maskBorderSlice,
            originalMaskBorderWidth: element.style.maskBorderWidth,
            originalMaskBorderOutset: element.style.maskBorderOutset,
            originalMaskBorderRepeat: element.style.maskBorderRepeat,
            originalMaskBorderMode: element.style.maskBorderMode,
            originalMaskClipPath: element.style.maskClipPath,
        });

        // Initial update
        this.updateElement(id);
    }

    /**
     * Handle intersection events
     * @param {IntersectionObserverEntry[]} entries - The intersection entries
     */
    handleIntersection(entries) {
        return measurePerformance('scroll_animation_intersection', () => {
            entries.forEach(entry => {
                const element = entry.target;
                const id = element.id;
                const animation = this.elements.get(id);
                if (!animation) {
                    return;
                }

                // Update progress
                animation.progress = entry.intersectionRatio;

                // Update element
                this.updateElement(id);
            });
        });
    }

    /**
     * Handle scroll events
     */
    handleScroll() {
        return measurePerformance('scroll_animation_scroll', () => {
            this.elements.forEach((animation, id) => {
                this.updateElement(id);
            });
        });
    }

    /**
     * Handle resize events
     */
    handleResize() {
        return measurePerformance('scroll_animation_resize', () => {
            this.elements.forEach((animation, id) => {
                this.updateElement(id);
            });
        });
    }

    /**
     * Update a scroll animation element
     * @param {string} id - The element ID
     */
    updateElement(id) {
        return measurePerformance('scroll_animation_update', () => {
            const animation = this.elements.get(id);
            if (!animation) {
                return;
            }

            const { element, progress, options } = animation;
            const rect = element.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

            // Calculate progress
            const scrollProgress = {
                x: (rect.left + scrollLeft) / viewportWidth,
                y: (rect.top + scrollTop) / viewportHeight,
                scroll: scrollTop / (document.documentElement.scrollHeight - viewportHeight),
                viewport: {
                    x: rect.left / viewportWidth,
                    y: rect.top / viewportHeight,
                    width: rect.width / viewportWidth,
                    height: rect.height / viewportHeight,
                },
            };

            // Apply transforms
            const transforms = [];

            // Position
            if (options.position) {
                const { x, y, z } = options.position;
                if (x) {
                    transforms.push(`translateX(${x * progress}px)`);
                }
                if (y) {
                    transforms.push(`translateY(${y * progress}px)`);
                }
                if (z) {
                    transforms.push(`translateZ(${z * progress}px)`);
                }
            }

            // Scale
            if (options.scale) {
                const { x, y, z } = options.scale;
                if (x) {
                    transforms.push(`scaleX(${1 + x * progress})`);
                }
                if (y) {
                    transforms.push(`scaleY(${1 + y * progress})`);
                }
                if (z) {
                    transforms.push(`scaleZ(${1 + z * progress})`);
                }
            }

            // Rotate
            if (options.rotate) {
                const { x, y, z } = options.rotate;
                if (x) {
                    transforms.push(`rotateX(${x * progress}deg)`);
                }
                if (y) {
                    transforms.push(`rotateY(${y * progress}deg)`);
                }
                if (z) {
                    transforms.push(`rotateZ(${z * progress}deg)`);
                }
            }

            // Skew
            if (options.skew) {
                const { x, y } = options.skew;
                if (x) {
                    transforms.push(`skewX(${x * progress}deg)`);
                }
                if (y) {
                    transforms.push(`skewY(${y * progress}deg)`);
                }
            }

            // Apply transforms
            if (transforms.length > 0) {
                element.style.transform = transforms.join(' ');
            }

            // Apply opacity
            if (options.opacity) {
                const { start, end } = options.opacity;
                const opacity = start + (end - start) * progress;
                element.style.opacity = opacity;
            }

            // Apply filter
            if (options.filter) {
                const { blur, brightness, contrast, grayscale, hueRotate, invert, saturate, sepia } = options.filter;
                const filters = [];

                if (blur) {
                    filters.push(`blur(${blur * progress}px)`);
                }
                if (brightness) {
                    filters.push(`brightness(${brightness * progress})`);
                }
                if (contrast) {
                    filters.push(`contrast(${contrast * progress})`);
                }
                if (grayscale) {
                    filters.push(`grayscale(${grayscale * progress})`);
                }
                if (hueRotate) {
                    filters.push(`hue-rotate(${hueRotate * progress}deg)`);
                }
                if (invert) {
                    filters.push(`invert(${invert * progress})`);
                }
                if (saturate) {
                    filters.push(`saturate(${saturate * progress})`);
                }
                if (sepia) {
                    filters.push(`sepia(${sepia * progress})`);
                }

                if (filters.length > 0) {
                    element.style.filter = filters.join(' ');
                }
            }

            // Apply backdrop filter
            if (options.backdropFilter) {
                const { blur, brightness, contrast, grayscale, hueRotate, invert, saturate, sepia } = options.backdropFilter;
                const filters = [];

                if (blur) {
                    filters.push(`blur(${blur * progress}px)`);
                }
                if (brightness) {
                    filters.push(`brightness(${brightness * progress})`);
                }
                if (contrast) {
                    filters.push(`contrast(${contrast * progress})`);
                }
                if (grayscale) {
                    filters.push(`grayscale(${grayscale * progress})`);
                }
                if (hueRotate) {
                    filters.push(`hue-rotate(${hueRotate * progress}deg)`);
                }
                if (invert) {
                    filters.push(`invert(${invert * progress})`);
                }
                if (saturate) {
                    filters.push(`saturate(${saturate * progress})`);
                }
                if (sepia) {
                    filters.push(`sepia(${sepia * progress})`);
                }

                if (filters.length > 0) {
                    element.style.backdropFilter = filters.join(' ');
                }
            }

            // Apply clip path
            if (options.clipPath) {
                const { type, value } = options.clipPath;
                if (type === 'circle') {
                    const { radius, x, y } = value;
                    const r = radius * progress;
                    const cx = x * scrollProgress.x;
                    const cy = y * scrollProgress.y;
                    element.style.clipPath = `circle(${r}px at ${cx}px ${cy}px)`;
                } else if (type === 'ellipse') {
                    const { rx, ry, x, y } = value;
                    const rxx = rx * progress;
                    const ryy = ry * progress;
                    const cx = x * scrollProgress.x;
                    const cy = y * scrollProgress.y;
                    element.style.clipPath = `ellipse(${rxx}px ${ryy}px at ${cx}px ${cy}px)`;
                } else if (type === 'inset') {
                    const { top, right, bottom, left } = value;
                    const t = top * progress;
                    const r = right * progress;
                    const b = bottom * progress;
                    const l = left * progress;
                    element.style.clipPath = `inset(${t}px ${r}px ${b}px ${l}px)`;
                } else if (type === 'polygon') {
                    const { points } = value;
                    const p = points.map(point => {
                        const [x, y] = point;
                        return `${x * scrollProgress.x}px ${y * scrollProgress.y}px`;
                    });
                    element.style.clipPath = `polygon(${p.join(', ')})`;
                }
            }

            // Apply mask
            if (options.mask) {
                const { type, value } = options.mask;
                if (type === 'circle') {
                    const { radius, x, y } = value;
                    const r = radius * progress;
                    const cx = x * scrollProgress.x;
                    const cy = y * scrollProgress.y;
                    element.style.mask = `circle(${r}px at ${cx}px ${cy}px)`;
                } else if (type === 'ellipse') {
                    const { rx, ry, x, y } = value;
                    const rxx = rx * progress;
                    const ryy = ry * progress;
                    const cx = x * scrollProgress.x;
                    const cy = y * scrollProgress.y;
                    element.style.mask = `ellipse(${rxx}px ${ryy}px at ${cx}px ${cy}px)`;
                } else if (type === 'inset') {
                    const { top, right, bottom, left } = value;
                    const t = top * progress;
                    const r = right * progress;
                    const b = bottom * progress;
                    const l = left * progress;
                    element.style.mask = `inset(${t}px ${r}px ${b}px ${l}px)`;
                } else if (type === 'polygon') {
                    const { points } = value;
                    const p = points.map(point => {
                        const [x, y] = point;
                        return `${x * scrollProgress.x}px ${y * scrollProgress.y}px`;
                    });
                    element.style.mask = `polygon(${p.join(', ')})`;
                }
            }

            if (animation.onUpdate) {
                animation.onUpdate({
                    element,
                    progress,
                    scrollProgress,
                });
            }

            this.notifySubscribers('update', { id, progress, scrollProgress });
        });
    }

    /**
     * Reset a scroll animation element
     * @param {string} id - The element ID
     */
    resetElement(id) {
        return measurePerformance('scroll_animation_reset', () => {
            const animation = this.elements.get(id);
            if (!animation) {
                return;
            }

            const { element } = animation;

            // Reset styles
            element.style.transform = animation.originalTransform;
            element.style.opacity = animation.originalOpacity;
            element.style.filter = animation.originalFilter;
            element.style.backdropFilter = animation.originalBackdropFilter;
            element.style.clipPath = animation.originalClipPath;
            element.style.mask = animation.originalMask;
            element.style.transition = animation.originalTransition;
            element.style.willChange = animation.originalWillChange;
            element.style.mixBlendMode = animation.originalMixBlendMode;
            element.style.isolation = animation.originalIsolation;
            element.style.perspective = animation.originalPerspective;
            element.style.transformStyle = animation.originalTransformStyle;
            element.style.backfaceVisibility = animation.originalBackfaceVisibility;
            element.style.transformOrigin = animation.originalTransformOrigin;
            element.style.filter = animation.originalFilter;
            element.style.backdropFilter = animation.originalBackdropFilter;
            element.style.mixBlendMode = animation.originalMixBlendMode;
            element.style.isolation = animation.originalIsolation;
            element.style.clipPath = animation.originalClipPath;
            element.style.mask = animation.originalMask;
            element.style.maskImage = animation.originalMaskImage;
            element.style.maskSize = animation.originalMaskSize;
            element.style.maskPosition = animation.originalMaskPosition;
            element.style.maskRepeat = animation.originalMaskRepeat;
            element.style.maskOrigin = animation.originalMaskOrigin;
            element.style.maskClip = animation.originalMaskClip;
            element.style.maskComposite = animation.originalMaskComposite;
            element.style.maskMode = animation.originalMaskMode;
            element.style.maskType = animation.originalMaskType;
            element.style.maskBorder = animation.originalMaskBorder;
            element.style.maskBorderSource = animation.originalMaskBorderSource;
            element.style.maskBorderSlice = animation.originalMaskBorderSlice;
            element.style.maskBorderWidth = animation.originalMaskBorderWidth;
            element.style.maskBorderOutset = animation.originalMaskBorderOutset;
            element.style.maskBorderRepeat = animation.originalMaskBorderRepeat;
            element.style.maskBorderMode = animation.originalMaskBorderMode;
            element.style.maskClipPath = animation.originalMaskClipPath;

            if (animation.onReset) {
                animation.onReset({
                    element,
                });
            }

            this.notifySubscribers('reset', { id });
        });
    }

    /**
     * Get element data
     * @param {string} id - The element ID
     * @returns {Object} The element data
     */
    getElementData(id) {
        return this.elements.get(id);
    }

    /**
     * Update element data
     * @param {string} id - The element ID
     * @param {Object} data - The new element data
     */
    updateElementData(id, data) {
        const animation = this.elements.get(id);
        if (animation) {
            Object.assign(animation, data);
            this.updateElement(id);
        }
    }
}

// Create and export a singleton instance
const scrollAnimationService = new ScrollAnimationService();
export default scrollAnimationService; 