// Sort service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class SortService {
    constructor() {
        this.sortables = new Map();
        this.subscribers = new Set();
        this.draggedItem = null;
        this.placeholder = null;
        this.initialize();
    }

    /**
     * Initialize the sort service
     */
    initialize() {
        document.addEventListener('dragstart', this.handleDragStart.bind(this));
        document.addEventListener('dragend', this.handleDragEnd.bind(this));
        document.addEventListener('dragover', this.handleDragOver.bind(this));
        document.addEventListener('drop', this.handleDrop.bind(this));
    }

    /**
     * Subscribe to sort events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of sort events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in sort subscriber:', error);
            }
        });
    }

    /**
     * Register a sortable container
     * @param {string} id - The container ID
     * @param {Object} options - The sortable options
     */
    registerSortable(id, options = {}) {
        const container = document.getElementById(id);
        if (!container) {
            console.error(`Container ${id} not found`);
            return;
        }

        // Make items draggable
        const items = container.querySelectorAll(options.itemSelector || '.sortable-item');
        items.forEach(item => {
            item.draggable = true;
            item.classList.add('sortable-item');
        });

        this.sortables.set(id, {
            ...options,
            container,
            items: Array.from(items),
        });
    }

    /**
     * Create a placeholder element
     * @param {HTMLElement} item - The dragged item
     * @returns {HTMLElement} The placeholder element
     */
    createPlaceholder(item) {
        const placeholder = document.createElement('div');
        placeholder.className = 'sortable-placeholder';
        placeholder.style.height = `${item.offsetHeight}px`;
        placeholder.style.width = `${item.offsetWidth}px`;
        return placeholder;
    }

    /**
     * Handle drag start events
     * @param {DragEvent} event - The drag event
     */
    handleDragStart(event) {
        return measurePerformance('sort_dragstart', () => {
            const item = event.target.closest('.sortable-item');
            if (!item) {
                return;
            }

            const container = item.parentElement;
            const id = container.id;
            const sortable = this.sortables.get(id);
            if (!sortable) {
                return;
            }

            this.draggedItem = item;
            this.placeholder = this.createPlaceholder(item);

            // Set drag data
            event.dataTransfer.effectAllowed = 'move';
            event.dataTransfer.setData('text/plain', id);

            // Add placeholder
            container.insertBefore(this.placeholder, item);

            if (sortable.onDragStart) {
                sortable.onDragStart(event, {
                    item,
                    container,
                    placeholder: this.placeholder,
                });
            }

            this.notifySubscribers('sortstart', { id, event });
        });
    }

    /**
     * Handle drag end events
     * @param {DragEvent} event - The drag event
     */
    handleDragEnd(event) {
        return measurePerformance('sort_dragend', () => {
            if (!this.draggedItem) {
                return;
            }

            const container = this.draggedItem.parentElement;
            const id = container.id;
            const sortable = this.sortables.get(id);
            if (!sortable) {
                return;
            }

            // Remove placeholder
            if (this.placeholder && this.placeholder.parentElement) {
                this.placeholder.parentElement.removeChild(this.placeholder);
            }

            if (sortable.onDragEnd) {
                sortable.onDragEnd(event, {
                    item: this.draggedItem,
                    container,
                });
            }

            this.draggedItem = null;
            this.placeholder = null;

            this.notifySubscribers('sortend', { id, event });
        });
    }

    /**
     * Handle drag over events
     * @param {DragEvent} event - The drag event
     */
    handleDragOver(event) {
        return measurePerformance('sort_dragover', () => {
            event.preventDefault();

            if (!this.draggedItem || !this.placeholder) {
                return;
            }

            const container = this.draggedItem.parentElement;
            const id = container.id;
            const sortable = this.sortables.get(id);
            if (!sortable) {
                return;
            }

            const target = event.target.closest('.sortable-item');
            if (!target || target === this.draggedItem) {
                return;
            }

            // Calculate position
            const rect = target.getBoundingClientRect();
            const midpoint = rect.top + rect.height / 2;
            const position = event.clientY < midpoint ? 'before' : 'after';

            // Move placeholder
            if (position === 'before') {
                container.insertBefore(this.placeholder, target);
            } else {
                container.insertBefore(this.placeholder, target.nextSibling);
            }

            if (sortable.onDragOver) {
                sortable.onDragOver(event, {
                    item: this.draggedItem,
                    target,
                    container,
                    position,
                });
            }

            this.notifySubscribers('sortover', { id, event });
        });
    }

    /**
     * Handle drop events
     * @param {DragEvent} event - The drop event
     */
    handleDrop(event) {
        return measurePerformance('sort_drop', () => {
            event.preventDefault();

            if (!this.draggedItem || !this.placeholder) {
                return;
            }

            const container = this.draggedItem.parentElement;
            const id = container.id;
            const sortable = this.sortables.get(id);
            if (!sortable) {
                return;
            }

            // Move item to placeholder position
            container.insertBefore(this.draggedItem, this.placeholder);

            // Update items array
            const items = Array.from(container.querySelectorAll('.sortable-item'));
            sortable.items = items;

            if (sortable.onDrop) {
                sortable.onDrop(event, {
                    item: this.draggedItem,
                    container,
                    items,
                });
            }

            this.notifySubscribers('sortdrop', { id, event });
        });
    }

    /**
     * Get sortable data
     * @param {string} id - The sortable ID
     * @returns {Object} The sortable data
     */
    getSortableData(id) {
        return this.sortables.get(id);
    }

    /**
     * Update sortable data
     * @param {string} id - The sortable ID
     * @param {Object} data - The new sortable data
     */
    updateSortableData(id, data) {
        const sortable = this.sortables.get(id);
        if (sortable) {
            Object.assign(sortable, data);
        }
    }
}

// Create and export a singleton instance
const sortService = new SortService();
export default sortService; 