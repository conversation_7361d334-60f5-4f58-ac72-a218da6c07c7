"""
PII Detection Service
Automatic detection and handling of Personally Identifiable Information
"""

import hashlib
import logging
import re
from typing import Any, Dict, List, Optional, Set, Tuple

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class PIIMatch(BaseModel):
    """PII detection match model."""
    
    type: str = Field(..., description="Type of PII detected")
    value: str = Field(..., description="Detected PII value")
    start_pos: int = Field(..., description="Start position in text")
    end_pos: int = Field(..., description="End position in text")
    confidence: float = Field(..., description="Detection confidence (0-1)")
    context: str = Field(..., description="Surrounding context")


class PIIDetectionResult(BaseModel):
    """PII detection result model."""
    
    original_text: str = Field(..., description="Original input text")
    sanitized_text: str = Field(..., description="Text with PII removed/masked")
    pii_matches: List[PIIMatch] = Field(default_factory=list, description="Detected PII instances")
    pii_types: Set[str] = Field(default_factory=set, description="Types of PII found")
    risk_level: str = Field(..., description="Overall risk level")
    sanitization_method: str = Field(..., description="Method used for sanitization")


class PIIDetectionError(Exception):
    """Custom exception for PII detection errors."""
    
    def __init__(self, message: str, error_code: str = "PII_DETECTION_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class PIIDetector:
    """
    Enterprise PII detection and sanitization service.
    
    Features:
    - Pattern-based PII detection
    - Multiple sanitization methods
    - Risk level assessment
    - GDPR compliance support
    - Configurable sensitivity levels
    """
    
    def __init__(self):
        """Initialize PII detector."""
        
        # PII detection patterns
        self.patterns = {
            'email': {
                'pattern': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
                'confidence': 0.95,
                'risk': 'medium'
            },
            'phone': {
                'pattern': re.compile(r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'),
                'confidence': 0.90,
                'risk': 'medium'
            },
            'ssn': {
                'pattern': re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
                'confidence': 0.98,
                'risk': 'high'
            },
            'credit_card': {
                'pattern': re.compile(r'\b(?:\d{4}[-\s]?){3}\d{4}\b'),
                'confidence': 0.85,
                'risk': 'high'
            },
            'address': {
                'pattern': re.compile(r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Court|Ct|Place|Pl)', re.IGNORECASE),
                'confidence': 0.80,
                'risk': 'medium'
            },
            'date_of_birth': {
                'pattern': re.compile(r'\b(?:0[1-9]|1[0-2])[/-](?:0[1-9]|[12]\d|3[01])[/-](?:19|20)\d{2}\b'),
                'confidence': 0.75,
                'risk': 'high'
            },
            'ip_address': {
                'pattern': re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
                'confidence': 0.90,
                'risk': 'low'
            },
            'passport': {
                'pattern': re.compile(r'\b[A-Z]{1,2}\d{6,9}\b'),
                'confidence': 0.70,
                'risk': 'high'
            },
            'driver_license': {
                'pattern': re.compile(r'\b[A-Z]{1,2}\d{6,8}\b'),
                'confidence': 0.65,
                'risk': 'medium'
            },
            'bank_account': {
                'pattern': re.compile(r'\b\d{8,17}\b'),
                'confidence': 0.60,
                'risk': 'high'
            }
        }
        
        # Name patterns (more complex due to variations)
        self.name_patterns = [
            re.compile(r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b'),  # First Last
            re.compile(r'\b[A-Z][a-z]+\s+[A-Z]\.\s+[A-Z][a-z]+\b'),  # First M. Last
            re.compile(r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\s+[A-Z][a-z]+\b'),  # First Middle Last
        ]
        
        # Common first names (subset for validation)
        self.common_first_names = {
            'james', 'john', 'robert', 'michael', 'william', 'david', 'richard', 'joseph',
            'thomas', 'christopher', 'charles', 'daniel', 'matthew', 'anthony', 'mark',
            'donald', 'steven', 'paul', 'andrew', 'joshua', 'kenneth', 'kevin', 'brian',
            'mary', 'patricia', 'jennifer', 'linda', 'elizabeth', 'barbara', 'susan',
            'jessica', 'sarah', 'karen', 'nancy', 'lisa', 'betty', 'helen', 'sandra',
            'donna', 'carol', 'ruth', 'sharon', 'michelle', 'laura', 'sarah', 'kimberly'
        }
        
        # Sanitization methods
        self.sanitization_methods = {
            'mask': self._mask_pii,
            'remove': self._remove_pii,
            'hash': self._hash_pii,
            'replace': self._replace_pii,
        }
    
    def detect_pii(
        self,
        text: str,
        sensitivity_level: str = 'medium',
        include_names: bool = True
    ) -> PIIDetectionResult:
        """
        Detect PII in text.
        
        Args:
            text: Input text to analyze
            sensitivity_level: Detection sensitivity (low, medium, high)
            include_names: Whether to detect names
            
        Returns:
            PIIDetectionResult: Detection results with sanitized text
        """
        try:
            pii_matches = []
            pii_types = set()
            
            # Detect pattern-based PII
            for pii_type, config in self.patterns.items():
                matches = self._find_pattern_matches(text, pii_type, config, sensitivity_level)
                pii_matches.extend(matches)
                if matches:
                    pii_types.add(pii_type)
            
            # Detect names if requested
            if include_names:
                name_matches = self._detect_names(text, sensitivity_level)
                pii_matches.extend(name_matches)
                if name_matches:
                    pii_types.add('name')
            
            # Sort matches by position
            pii_matches.sort(key=lambda x: x.start_pos)
            
            # Determine risk level
            risk_level = self._assess_risk_level(pii_matches)
            
            # Sanitize text
            sanitized_text, sanitization_method = self._sanitize_text(text, pii_matches)
            
            return PIIDetectionResult(
                original_text=text,
                sanitized_text=sanitized_text,
                pii_matches=pii_matches,
                pii_types=pii_types,
                risk_level=risk_level,
                sanitization_method=sanitization_method
            )
            
        except Exception as e:
            logger.error(f"PII detection failed: {e}")
            raise PIIDetectionError(
                f"PII detection failed: {str(e)}",
                error_code="DETECTION_FAILED"
            )
    
    def _find_pattern_matches(
        self,
        text: str,
        pii_type: str,
        config: Dict[str, Any],
        sensitivity_level: str
    ) -> List[PIIMatch]:
        """Find matches for a specific PII pattern."""
        matches = []
        pattern = config['pattern']
        base_confidence = config['confidence']
        
        # Adjust confidence based on sensitivity level
        confidence_adjustment = {
            'low': -0.1,
            'medium': 0.0,
            'high': 0.1
        }
        adjusted_confidence = base_confidence + confidence_adjustment.get(sensitivity_level, 0.0)
        
        for match in pattern.finditer(text):
            start_pos = match.start()
            end_pos = match.end()
            value = match.group()
            
            # Get context around the match
            context_start = max(0, start_pos - 20)
            context_end = min(len(text), end_pos + 20)
            context = text[context_start:context_end]
            
            # Additional validation for some PII types
            if self._validate_pii_match(pii_type, value):
                matches.append(PIIMatch(
                    type=pii_type,
                    value=value,
                    start_pos=start_pos,
                    end_pos=end_pos,
                    confidence=adjusted_confidence,
                    context=context
                ))
        
        return matches
    
    def _detect_names(self, text: str, sensitivity_level: str) -> List[PIIMatch]:
        """Detect potential names in text."""
        matches = []
        
        for pattern in self.name_patterns:
            for match in pattern.finditer(text):
                name = match.group().strip()
                start_pos = match.start()
                end_pos = match.end()
                
                # Validate if it's likely a real name
                confidence = self._calculate_name_confidence(name)
                
                # Adjust confidence based on sensitivity
                if sensitivity_level == 'high':
                    confidence += 0.1
                elif sensitivity_level == 'low':
                    confidence -= 0.1
                
                if confidence > 0.5:  # Threshold for name detection
                    context_start = max(0, start_pos - 20)
                    context_end = min(len(text), end_pos + 20)
                    context = text[context_start:context_end]
                    
                    matches.append(PIIMatch(
                        type='name',
                        value=name,
                        start_pos=start_pos,
                        end_pos=end_pos,
                        confidence=confidence,
                        context=context
                    ))
        
        return matches
    
    def _validate_pii_match(self, pii_type: str, value: str) -> bool:
        """Additional validation for PII matches."""
        
        if pii_type == 'credit_card':
            # Luhn algorithm check for credit cards
            return self._luhn_check(re.sub(r'[-\s]', '', value))
        
        elif pii_type == 'ssn':
            # Basic SSN validation
            digits = re.sub(r'[-\s]', '', value)
            return len(digits) == 9 and not digits.startswith('000')
        
        elif pii_type == 'phone':
            # Basic phone validation
            digits = re.sub(r'[-\s\(\)]', '', value)
            return 10 <= len(digits) <= 15
        
        elif pii_type == 'email':
            # Additional email validation
            return '@' in value and '.' in value.split('@')[1]
        
        return True
    
    def _calculate_name_confidence(self, name: str) -> float:
        """Calculate confidence that a string is a real name."""
        confidence = 0.5  # Base confidence
        
        parts = name.split()
        
        # Check if first name is common
        if parts and parts[0].lower() in self.common_first_names:
            confidence += 0.3
        
        # Check capitalization pattern
        if all(part[0].isupper() and part[1:].islower() for part in parts if part):
            confidence += 0.2
        
        # Check length
        if 2 <= len(parts) <= 4:
            confidence += 0.1
        
        # Penalize if it looks like a title or organization
        title_words = {'mr', 'mrs', 'ms', 'dr', 'prof', 'inc', 'llc', 'corp', 'ltd'}
        if any(part.lower() in title_words for part in parts):
            confidence -= 0.3
        
        return min(1.0, max(0.0, confidence))
    
    def _luhn_check(self, card_number: str) -> bool:
        """Luhn algorithm for credit card validation."""
        def luhn_checksum(card_num):
            def digits_of(n):
                return [int(d) for d in str(n)]
            
            digits = digits_of(card_num)
            odd_digits = digits[-1::-2]
            even_digits = digits[-2::-2]
            checksum = sum(odd_digits)
            for d in even_digits:
                checksum += sum(digits_of(d * 2))
            return checksum % 10
        
        return luhn_checksum(card_number) == 0
    
    def _assess_risk_level(self, pii_matches: List[PIIMatch]) -> str:
        """Assess overall risk level based on detected PII."""
        if not pii_matches:
            return 'none'
        
        high_risk_types = {'ssn', 'credit_card', 'passport', 'bank_account', 'date_of_birth'}
        medium_risk_types = {'email', 'phone', 'address', 'driver_license'}
        
        detected_types = {match.type for match in pii_matches}
        
        if detected_types & high_risk_types:
            return 'high'
        elif detected_types & medium_risk_types:
            return 'medium'
        else:
            return 'low'
    
    def _sanitize_text(
        self,
        text: str,
        pii_matches: List[PIIMatch],
        method: str = 'mask'
    ) -> Tuple[str, str]:
        """Sanitize text by removing or masking PII."""
        if not pii_matches:
            return text, 'none'
        
        sanitizer = self.sanitization_methods.get(method, self._mask_pii)
        
        # Sort matches by position (reverse order to maintain positions)
        sorted_matches = sorted(pii_matches, key=lambda x: x.start_pos, reverse=True)
        
        sanitized_text = text
        for match in sorted_matches:
            replacement = sanitizer(match)
            sanitized_text = (
                sanitized_text[:match.start_pos] +
                replacement +
                sanitized_text[match.end_pos:]
            )
        
        return sanitized_text, method
    
    def _mask_pii(self, match: PIIMatch) -> str:
        """Mask PII with asterisks."""
        if match.type in ['email']:
            # Preserve domain for emails
            parts = match.value.split('@')
            if len(parts) == 2:
                return f"{'*' * len(parts[0])}@{parts[1]}"
        
        elif match.type in ['phone']:
            # Mask middle digits of phone
            digits = re.sub(r'[-\s\(\)]', '', match.value)
            if len(digits) >= 10:
                return f"{digits[:3]}-***-{digits[-4:]}"
        
        elif match.type in ['credit_card']:
            # Mask all but last 4 digits
            digits = re.sub(r'[-\s]', '', match.value)
            return f"****-****-****-{digits[-4:]}"
        
        # Default masking
        return '*' * len(match.value)
    
    def _remove_pii(self, match: PIIMatch) -> str:
        """Remove PII completely."""
        return f"[{match.type.upper()}_REMOVED]"
    
    def _hash_pii(self, match: PIIMatch) -> str:
        """Replace PII with hash."""
        hash_value = hashlib.sha256(match.value.encode()).hexdigest()[:8]
        return f"[{match.type.upper()}_{hash_value}]"
    
    def _replace_pii(self, match: PIIMatch) -> str:
        """Replace PII with generic placeholder."""
        replacements = {
            'name': '[NAME]',
            'email': '[EMAIL]',
            'phone': '[PHONE]',
            'address': '[ADDRESS]',
            'ssn': '[SSN]',
            'credit_card': '[CREDIT_CARD]',
            'date_of_birth': '[DOB]',
            'passport': '[PASSPORT]',
            'driver_license': '[LICENSE]',
            'bank_account': '[ACCOUNT]',
            'ip_address': '[IP]',
        }
        return replacements.get(match.type, f'[{match.type.upper()}]')
    
    def sanitize_cv_data(self, cv_data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize CV data structure."""
        sanitized_data = {}
        
        for key, value in cv_data.items():
            if isinstance(value, str):
                result = self.detect_pii(value)
                sanitized_data[key] = result.sanitized_text
            elif isinstance(value, list):
                sanitized_data[key] = [
                    self.detect_pii(item).sanitized_text if isinstance(item, str) else item
                    for item in value
                ]
            elif isinstance(value, dict):
                sanitized_data[key] = self.sanitize_cv_data(value)
            else:
                sanitized_data[key] = value
        
        return sanitized_data
    
    def get_pii_summary(self, detection_result: PIIDetectionResult) -> Dict[str, Any]:
        """Get summary of PII detection results."""
        return {
            'total_pii_found': len(detection_result.pii_matches),
            'pii_types': list(detection_result.pii_types),
            'risk_level': detection_result.risk_level,
            'sanitization_applied': detection_result.sanitization_method != 'none',
            'confidence_scores': [match.confidence for match in detection_result.pii_matches],
            'avg_confidence': (
                sum(match.confidence for match in detection_result.pii_matches) / 
                len(detection_result.pii_matches)
                if detection_result.pii_matches else 0
            )
        }


# Global PII detector instance
pii_detector = PIIDetector()
