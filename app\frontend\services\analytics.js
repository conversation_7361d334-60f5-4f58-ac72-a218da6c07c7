// Analytics service for tracking user interactions
import config from '../config.js';
import { measurePerformance } from '../utils.js';
import apiService from './api.js';

class AnalyticsService {
    constructor() {
        this.enabled = config.analytics?.enabled || false;
        this.queue = [];
        this.batchSize = config.analytics?.batchSize || 10;
        this.flushInterval = config.analytics?.flushInterval || 30000; // 30 seconds
        this.sessionId = this.generateSessionId();
        this.userId = null;
        this.startTime = Date.now();

        // Start periodic flush
        if (this.enabled) {
            setInterval(() => this.flush(), this.flushInterval);
        }
    }

    /**
     * Generate a unique session ID
     * @returns {string} The session ID
     */
    generateSessionId() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * Set the user ID
     * @param {string} userId - The user ID
     */
    setUserId(userId) {
        this.userId = userId;
    }

    /**
     * Track an event
     * @param {string} eventName - The event name
     * @param {Object} [properties] - The event properties
     */
    track(eventName, properties = {}) {
        if (!this.enabled) return;

        const event = {
            eventName,
            properties,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            userId: this.userId,
        };

        this.queue.push(event);

        // Flush if queue is full
        if (this.queue.length >= this.batchSize) {
            this.flush();
        }
    }

    /**
     * Track a page view
     * @param {string} page - The page path
     * @param {Object} [properties] - Additional properties
     */
    trackPageView(page, properties = {}) {
        this.track('page_view', {
            page,
            ...properties,
        });
    }

    /**
     * Track a user action
     * @param {string} action - The action name
     * @param {Object} [properties] - Additional properties
     */
    trackAction(action, properties = {}) {
        this.track('user_action', {
            action,
            ...properties,
        });
    }

    /**
     * Track an error
     * @param {Error} error - The error object
     * @param {Object} [properties] - Additional properties
     */
    trackError(error, properties = {}) {
        this.track('error', {
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack,
            },
            ...properties,
        });
    }

    /**
     * Track performance metrics
     * @param {string} metric - The metric name
     * @param {number} value - The metric value
     * @param {Object} [properties] - Additional properties
     */
    trackPerformance(metric, value, properties = {}) {
        this.track('performance', {
            metric,
            value,
            ...properties,
        });
    }

    /**
     * Flush the event queue
     */
    async flush() {
        if (this.queue.length === 0) return;

        const events = [...this.queue];
        this.queue = [];

        try {
            await apiService.request('/analytics/track', {
                method: 'POST',
                body: JSON.stringify({ events }),
            });
        } catch (error) {
            console.error('Error flushing analytics:', error);
            // Put events back in queue
            this.queue = [...events, ...this.queue];
        }
    }

    /**
     * Get session duration
     * @returns {number} The session duration in milliseconds
     */
    getSessionDuration() {
        return Date.now() - this.startTime;
    }

    /**
     * Enable analytics
     */
    enable() {
        this.enabled = true;
    }

    /**
     * Disable analytics
     */
    disable() {
        this.enabled = false;
        this.queue = [];
    }

    /**
     * Check if analytics is enabled
     * @returns {boolean} Whether analytics is enabled
     */
    isEnabled() {
        return this.enabled;
    }

    /**
     * Get the current queue size
     * @returns {number} The queue size
     */
    getQueueSize() {
        return this.queue.length;
    }

    /**
     * Clear the event queue
     */
    clearQueue() {
        this.queue = [];
    }
}

// Create and export a singleton instance
const analyticsService = new AnalyticsService();
export default analyticsService; 