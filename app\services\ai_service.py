"""
Unified AI Service
Servicio unificado que puede usar Mistral 7B local o OpenAI según configuración
"""

import json
import logging
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

from app.core.config import get_settings
from app.services.mistral_service import mistral_service

logger = logging.getLogger(__name__)

class AIProvider(ABC):
    """Interfaz abstracta para proveedores de IA"""
    
    @abstractmethod
    async def generate_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 500,
        **kwargs
    ) -> str:
        """Generar completion de texto"""
        pass
    
    @abstractmethod
    async def generate_json_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        schema_example: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generar completion en formato JSON"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Verificar estado del proveedor"""
        pass

class MistralProvider(AIProvider):
    """Proveedor de IA usando Mistral 7B local"""
    
    def __init__(self):
        self.service = mistral_service
    
    async def generate_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 500,
        **kwargs
    ) -> str:
        """Generar completion usando Mistral 7B"""
        return await self.service.generate_completion(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
    
    async def generate_json_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        schema_example: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generar completion JSON usando Mistral 7B"""
        return await self.service.generate_json_completion(
            prompt=prompt,
            system_prompt=system_prompt,
            schema_example=schema_example,
            **kwargs
        )
    
    async def health_check(self) -> Dict[str, Any]:
        """Verificar estado de Mistral"""
        result = await self.service.health_check()
        result["provider"] = "mistral"
        return result

class OpenAIProvider(AIProvider):
    """Proveedor de IA usando OpenAI API"""
    
    def __init__(self):
        self.settings = get_settings()
        self._client = None
    
    @property
    def client(self):
        """Lazy loading del cliente OpenAI"""
        if self._client is None:
            try:
                from openai import AsyncOpenAI
                self._client = AsyncOpenAI(api_key=self.settings.OPENAI_API_KEY)
            except ImportError:
                raise Exception("OpenAI library not installed. Install with: pip install openai")
        return self._client
    
    async def generate_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 500,
        **kwargs
    ) -> str:
        """Generar completion usando OpenAI"""
        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            response = await self.client.chat.completions.create(
                model=self.settings.OPENAI_MODEL,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI completion error: {e}")
            raise Exception(f"OpenAI generation failed: {str(e)}")
    
    async def generate_json_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        schema_example: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generar completion JSON usando OpenAI"""
        try:
            # Construir prompt para JSON
            json_instruction = "\n\nRespuesta requerida: JSON válido únicamente, sin texto adicional."
            if schema_example:
                json_instruction += f"\n\nEjemplo de formato esperado:\n{json.dumps(schema_example, indent=2, ensure_ascii=False)}"
            
            full_prompt = f"{prompt}{json_instruction}"
            
            # Generar respuesta
            response_text = await self.generate_completion(
                full_prompt,
                system_prompt,
                temperature=kwargs.get("temperature", 0.3),
                max_tokens=kwargs.get("max_tokens", 500),
                **kwargs
            )
            
            # Parsear JSON
            return self._extract_json(response_text)
            
        except Exception as e:
            logger.error(f"OpenAI JSON completion error: {e}")
            raise Exception(f"OpenAI JSON generation failed: {str(e)}")
    
    def _extract_json(self, response: str) -> Dict[str, Any]:
        """Extraer JSON de la respuesta de OpenAI"""
        try:
            response = response.strip()
            
            # Si la respuesta completa es JSON
            if response.startswith('{') and response.endswith('}'):
                return json.loads(response)
            
            # Buscar JSON entre marcadores
            start_idx = response.find('{')
            end_idx = response.rfind('}')
            
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx + 1]
                return json.loads(json_str)
            
            # Si no se encuentra JSON válido
            raise json.JSONDecodeError("No valid JSON found", response, 0)
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from OpenAI response: {response[:200]}...")
            raise Exception(f"Invalid JSON response: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Verificar estado de OpenAI"""
        try:
            test_response = await self.generate_completion(
                "Responde únicamente: 'OK'",
                temperature=0.1,
                max_tokens=10
            )
            
            return {
                "status": "healthy",
                "provider": "openai",
                "model": self.settings.OPENAI_MODEL,
                "test_response": test_response[:50]
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "provider": "openai",
                "model": self.settings.OPENAI_MODEL,
                "error": str(e)
            }

class AIService:
    """Servicio unificado de IA que selecciona el proveedor según configuración"""
    
    def __init__(self):
        self.settings = get_settings()
        self._provider = None
    
    @property
    def provider(self) -> AIProvider:
        """Obtener el proveedor de IA configurado"""
        if self._provider is None:
            if self.settings.AI_PROVIDER.lower() == "mistral":
                self._provider = MistralProvider()
                logger.info("Using Mistral 7B local provider")
            elif self.settings.AI_PROVIDER.lower() == "openai":
                self._provider = OpenAIProvider()
                logger.info("Using OpenAI provider")
            else:
                # Default to Mistral
                self._provider = MistralProvider()
                logger.warning(f"Unknown AI provider '{self.settings.AI_PROVIDER}', defaulting to Mistral")
        
        return self._provider
    
    async def generate_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 500,
        **kwargs
    ) -> str:
        """Generar completion usando el proveedor configurado"""
        return await self.provider.generate_completion(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
    
    async def generate_json_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        schema_example: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generar completion JSON usando el proveedor configurado"""
        return await self.provider.generate_json_completion(
            prompt=prompt,
            system_prompt=system_prompt,
            schema_example=schema_example,
            **kwargs
        )
    
    async def generate_tqr_achievement(
        self,
        profile_data: Dict[str, Any],
        achievement_description: str,
        **kwargs
    ) -> Dict[str, str]:
        """
        Generar logro en formato TQR (Tarea, Cuantificación, Resultado)
        
        Args:
            profile_data: Datos del perfil del usuario
            achievement_description: Descripción del logro
            
        Returns:
            Dict con keys: tarea, cuantificacion, resultado
        """
        system_prompt = """Eres un experto en Recursos Humanos especializado en optimización de CVs. 
Tu tarea es transformar experiencias laborales en logros impactantes usando la metodología TQR 
(Tarea, Cuantificación, Resultado).

IMPORTANTE: Debes responder ÚNICAMENTE con un objeto JSON válido, sin texto adicional antes o después.
El JSON debe tener exactamente las siguientes claves: "tarea", "cuantificacion", "resultado".
Cada valor debe ser una cadena de texto.

NO incluyas ningún texto adicional, explicaciones o comentarios.
NO uses markdown o código.
NO uses comillas dobles dentro de los valores.
NO uses caracteres especiales o emojis."""
        
        prompt = f"""
## Perfil del candidato:
Nombre: {profile_data.get('name', 'N/A')}
Posición: {profile_data.get('position', 'N/A')}
Experiencia: {profile_data.get('experience_years', 'N/A')} años

## Logro a transformar:
{achievement_description}

## Instrucciones:
Transforma esta experiencia en un logro con el modelo TQR. 

RESPONDE ÚNICAMENTE CON UN OBJETO JSON COMO ESTE:
{{
  "tarea": "Descripción clara de la tarea o responsabilidad",
  "cuantificacion": "Métricas específicas y medibles",
  "resultado": "Impacto positivo y beneficios obtenidos"
}}

## Ejemplo de formato TQR:
{{
  "tarea": "Implementé un sistema de automatización de procesos",
  "cuantificacion": "Reduciendo el tiempo de procesamiento en 40% y afectando a 150 usuarios diarios",
  "resultado": "Mejorando la eficiencia operativa y la satisfacción del cliente en un 25%"
}}

IMPORTANTE: 
- No incluyas ningún texto adicional antes o después del JSON
- No uses markdown o código
- No uses comillas dobles dentro de los valores
- No uses caracteres especiales o emojis
- Asegúrate de que el JSON sea válido y tenga exactamente las tres claves requeridas
"""
        
        schema_example = {
            "tarea": "string",
            "cuantificacion": "string", 
            "resultado": "string"
        }
        
        # Usar temperatura más baja para respuestas más consistentes
        temperature = kwargs.pop('temperature', 0.3)
        max_tokens = kwargs.pop('max_tokens', 400)

        return await self.generate_json_completion(
            prompt=prompt,
            system_prompt=system_prompt,
            schema_example=schema_example,
            temperature=temperature,
            max_tokens=max_tokens,
            **kwargs
        )
    
    async def health_check(self) -> Dict[str, Any]:
        """Verificar estado del servicio de IA"""
        try:
            result = await self.provider.health_check()
            result["configured_provider"] = self.settings.AI_PROVIDER
            return result
        except Exception as e:
            return {
                "status": "unhealthy",
                "configured_provider": self.settings.AI_PROVIDER,
                "error": str(e)
            }

# Instancia global del servicio
ai_service = AIService()
