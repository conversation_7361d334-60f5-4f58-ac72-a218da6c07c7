# 🔐 ImpactCV Threat Model

> **OWASP Top 10 2025 Threat Modeling for AI-Powered CV Generation System**  
> **Compliance:** OWASP Top 10 | NIST Cybersecurity Framework | Zero Trust Architecture

---

## 📋 **EXECUTIVE SUMMARY**

### **Threat Modeling Overview**
This document provides a comprehensive threat model for the ImpactCV AI-powered CV generation system, covering all OWASP Top 10 2025 categories with specific focus on AI/ML security risks. The analysis follows STRIDE methodology and includes mitigation strategies for each identified threat.

### **System Scope**
- **Web Application:** FastAPI-based microservices
- **AI Components:** OpenAI GPT-4o integration, RAG pipeline
- **Data Processing:** PDF/DOCX parsing, PII detection
- **Infrastructure:** Kubernetes deployment, cloud services
- **User Data:** Personal information, CV content, documents

---

## 🎯 **OWASP TOP 10 2025 THREAT ANALYSIS**

### **A01: Broken Access Control**

#### **Threat Description**
Unauthorized access to user data, CV generation functions, or administrative features.

#### **Attack Scenarios**
1. **Horizontal Privilege Escalation:** User accesses another user's CVs
2. **Vertical Privilege Escalation:** Regular user gains admin privileges
3. **Direct Object Reference:** Accessing CVs via predictable URLs
4. **API Endpoint Abuse:** Calling restricted API endpoints

#### **Potential Impact**
- **Confidentiality:** Exposure of personal CV data
- **Integrity:** Unauthorized modification of user profiles
- **Availability:** Service disruption through privilege abuse

#### **Mitigation Strategies**
```python
# JWT-based access control with role validation
@require_auth
@require_role("user")
async def get_cv(cv_id: str, current_user: User):
    # Verify ownership
    cv = await cv_service.get_cv(cv_id)
    if cv.user_id != current_user.id:
        raise HTTPException(403, "Access denied")
    return cv

# API rate limiting per user
@rate_limit("100/hour")
async def generate_cv(request: CVRequest, current_user: User):
    pass
```

#### **Security Controls**
- ✅ JWT token validation on all endpoints
- ✅ Role-based access control (RBAC)
- ✅ Resource ownership validation
- ✅ API rate limiting per user
- ✅ Session timeout enforcement

---

### **A02: Cryptographic Failures**

#### **Threat Description**
Exposure of sensitive data due to weak or missing encryption.

#### **Attack Scenarios**
1. **Data in Transit:** Man-in-the-middle attacks on API calls
2. **Data at Rest:** Database compromise exposing unencrypted PII
3. **Key Management:** Hardcoded API keys in source code
4. **Weak Algorithms:** Use of deprecated encryption methods

#### **Potential Impact**
- **Data Breach:** Exposure of personal information
- **Compliance Violation:** GDPR fines and penalties
- **Reputation Damage:** Loss of user trust

#### **Mitigation Strategies**
```python
# AES-256 encryption for sensitive data
from cryptography.fernet import Fernet

class DataEncryption:
    def __init__(self, key: bytes):
        self.cipher = Fernet(key)
    
    def encrypt_pii(self, data: str) -> str:
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_pii(self, encrypted_data: str) -> str:
        return self.cipher.decrypt(encrypted_data.encode()).decode()

# TLS 1.3 configuration
ssl_context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
```

#### **Security Controls**
- ✅ TLS 1.3 for all communications
- ✅ AES-256 encryption for data at rest
- ✅ HashiCorp Vault for key management
- ✅ Certificate pinning for API calls
- ✅ Encrypted database connections

---

### **A03: Injection**

#### **Threat Description**
Malicious code injection through user inputs, including SQL injection, NoSQL injection, and prompt injection.

#### **Attack Scenarios**
1. **SQL Injection:** Malicious SQL in form inputs
2. **NoSQL Injection:** MongoDB query manipulation
3. **Prompt Injection:** Malicious prompts to AI model
4. **Command Injection:** OS command execution

#### **Potential Impact**
- **Data Exfiltration:** Unauthorized database access
- **AI Model Manipulation:** Biased or harmful AI responses
- **System Compromise:** Remote code execution

#### **Mitigation Strategies**
```python
# Parameterized queries for SQL injection prevention
async def get_user_cvs(user_id: str) -> List[CV]:
    query = "SELECT * FROM cvs WHERE user_id = $1"
    return await database.fetch_all(query, user_id)

# Input validation and sanitization
from pydantic import BaseModel, validator

class CVRequest(BaseModel):
    content: str
    template_id: str
    
    @validator('content')
    def validate_content(cls, v):
        # Remove potential injection patterns
        sanitized = re.sub(r'[<>"\';]', '', v)
        if len(sanitized) > 10000:
            raise ValueError('Content too long')
        return sanitized

# Prompt injection prevention
def sanitize_prompt(user_input: str) -> str:
    # Remove potential prompt injection patterns
    dangerous_patterns = [
        r'ignore previous instructions',
        r'system:',
        r'assistant:',
        r'<\|.*?\|>',
    ]
    
    sanitized = user_input
    for pattern in dangerous_patterns:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
    
    return sanitized[:1000]  # Limit length
```

#### **Security Controls**
- ✅ Parameterized database queries
- ✅ Input validation and sanitization
- ✅ Prompt injection detection
- ✅ Content Security Policy (CSP)
- ✅ Web Application Firewall (WAF)

---

### **A04: Insecure Design**

#### **Threat Description**
Security flaws in the application's architecture and design.

#### **Attack Scenarios**
1. **Missing Security Controls:** No rate limiting on expensive operations
2. **Weak Authentication:** Single-factor authentication only
3. **Insufficient Logging:** No audit trail for sensitive operations
4. **Poor Error Handling:** Information disclosure in error messages

#### **Potential Impact**
- **Service Abuse:** Resource exhaustion attacks
- **Account Takeover:** Weak authentication bypass
- **Compliance Failure:** Audit trail requirements not met

#### **Mitigation Strategies**
```python
# Secure design patterns
class SecureDesignPatterns:
    
    @staticmethod
    def fail_securely(operation):
        """Fail to a secure state"""
        try:
            return operation()
        except Exception as e:
            logger.error(f"Operation failed: {type(e).__name__}")
            return {"error": "Operation failed", "details": None}
    
    @staticmethod
    def defense_in_depth(request):
        """Multiple security layers"""
        # Layer 1: Rate limiting
        if not rate_limiter.check(request.user_id):
            raise HTTPException(429, "Rate limit exceeded")
        
        # Layer 2: Input validation
        if not validate_input(request.data):
            raise HTTPException(400, "Invalid input")
        
        # Layer 3: Authorization
        if not authorize_user(request.user_id, request.resource):
            raise HTTPException(403, "Access denied")
        
        return True
```

#### **Security Controls**
- ✅ Multi-factor authentication (MFA)
- ✅ Comprehensive audit logging
- ✅ Rate limiting on all endpoints
- ✅ Secure error handling
- ✅ Defense in depth strategy

---

### **A05: Security Misconfiguration**

#### **Threat Description**
Insecure default configurations, incomplete configurations, or misconfigured security headers.

#### **Attack Scenarios**
1. **Default Credentials:** Using default admin passwords
2. **Exposed Debug Info:** Debug mode enabled in production
3. **Missing Security Headers:** No HSTS, CSP, or CSRF protection
4. **Unnecessary Services:** Unused services running with vulnerabilities

#### **Potential Impact**
- **Information Disclosure:** Sensitive configuration exposure
- **Unauthorized Access:** Default credential exploitation
- **Cross-Site Attacks:** Missing security headers

#### **Mitigation Strategies**
```python
# Security headers middleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*.impactcv.com"])
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://impactcv.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

# Security headers
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Content-Security-Policy"] = "default-src 'self'"
    return response
```

#### **Security Controls**
- ✅ Secure default configurations
- ✅ Security headers implementation
- ✅ Regular security scanning
- ✅ Configuration management
- ✅ Principle of least privilege

---

### **A06: Vulnerable and Outdated Components**

#### **Threat Description**
Use of components with known vulnerabilities or outdated versions.

#### **Attack Scenarios**
1. **Dependency Vulnerabilities:** Exploiting known CVEs in libraries
2. **Outdated Frameworks:** Using deprecated versions with security flaws
3. **Unpatched Systems:** Missing security updates
4. **Supply Chain Attacks:** Compromised dependencies

#### **Potential Impact**
- **Remote Code Execution:** Through vulnerable dependencies
- **Data Breach:** Via compromised libraries
- **Service Disruption:** Denial of service attacks

#### **Mitigation Strategies**
```yaml
# Automated dependency scanning (GitHub Actions)
name: Security Scan
on: [push, pull_request]
jobs:
  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Snyk to check for vulnerabilities
        uses: snyk/actions/python@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      - name: Run Safety check
        run: |
          pip install safety
          safety check --json
```

#### **Security Controls**
- ✅ Automated dependency scanning
- ✅ Regular security updates
- ✅ Vulnerability monitoring
- ✅ Supply chain security
- ✅ Component inventory management

---

### **A07: Identification and Authentication Failures**

#### **Threat Description**
Weaknesses in authentication and session management.

#### **Attack Scenarios**
1. **Brute Force Attacks:** Password guessing attacks
2. **Session Hijacking:** Stealing session tokens
3. **Credential Stuffing:** Using leaked credentials
4. **Weak Passwords:** Easily guessable passwords

#### **Potential Impact**
- **Account Takeover:** Unauthorized access to user accounts
- **Identity Theft:** Impersonation of legitimate users
- **Data Access:** Unauthorized access to personal data

#### **Mitigation Strategies**
```python
# Strong authentication implementation
from passlib.context import CryptContext
from jose import JWTError, jwt

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AuthService:
    def __init__(self):
        self.failed_attempts = {}
        self.lockout_duration = 900  # 15 minutes
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        return pwd_context.verify(plain_password, hashed_password)
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        # Check for account lockout
        if self.is_locked_out(email):
            raise HTTPException(423, "Account temporarily locked")
        
        user = get_user_by_email(email)
        if not user or not self.verify_password(password, user.hashed_password):
            self.record_failed_attempt(email)
            return None
        
        self.clear_failed_attempts(email)
        return user
    
    def is_locked_out(self, email: str) -> bool:
        attempts = self.failed_attempts.get(email, [])
        recent_attempts = [
            attempt for attempt in attempts 
            if time.time() - attempt < self.lockout_duration
        ]
        return len(recent_attempts) >= 5
```

#### **Security Controls**
- ✅ Multi-factor authentication
- ✅ Account lockout policies
- ✅ Strong password requirements
- ✅ Secure session management
- ✅ JWT token validation

---

### **A08: Software and Data Integrity Failures**

#### **Threat Description**
Failures related to code and infrastructure that do not protect against integrity violations.

#### **Attack Scenarios**
1. **Supply Chain Compromise:** Malicious code in dependencies
2. **Unsigned Updates:** Tampered software updates
3. **CI/CD Pipeline Attacks:** Compromised build processes
4. **Data Tampering:** Unauthorized data modifications

#### **Potential Impact**
- **Code Injection:** Malicious code execution
- **Data Corruption:** Integrity loss of critical data
- **System Compromise:** Full system takeover

#### **Mitigation Strategies**
```yaml
# Secure CI/CD pipeline
name: Secure Build
on: [push]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Verify commit signature
        run: git verify-commit HEAD
      - name: Build with integrity checks
        run: |
          # Verify checksums of dependencies
          pip-audit
          # Build with reproducible builds
          docker build --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') .
      - name: Sign artifacts
        run: |
          # Sign container images
          cosign sign --key cosign.key $IMAGE_URI
```

#### **Security Controls**
- ✅ Code signing and verification
- ✅ Secure CI/CD pipelines
- ✅ Dependency integrity checks
- ✅ Immutable infrastructure
- ✅ Audit logging for changes

---

### **A09: Security Logging and Monitoring Failures**

#### **Threat Description**
Insufficient logging and monitoring to detect and respond to security incidents.

#### **Attack Scenarios**
1. **Undetected Breaches:** Attacks go unnoticed for months
2. **Insufficient Forensics:** Cannot determine attack scope
3. **No Alerting:** Security events not triggering alerts
4. **Log Tampering:** Attackers modifying audit logs

#### **Potential Impact**
- **Extended Breach Duration:** Longer time to detection
- **Compliance Violations:** Audit requirements not met
- **Incident Response Delays:** Slow response to threats

#### **Mitigation Strategies**
```python
# Comprehensive security logging
import structlog
from datetime import datetime

logger = structlog.get_logger()

class SecurityLogger:
    @staticmethod
    def log_authentication(user_id: str, success: bool, ip_address: str):
        logger.info(
            "authentication_attempt",
            user_id=user_id,
            success=success,
            ip_address=ip_address,
            timestamp=datetime.utcnow().isoformat(),
            event_type="security.authentication"
        )
    
    @staticmethod
    def log_data_access(user_id: str, resource: str, action: str):
        logger.info(
            "data_access",
            user_id=user_id,
            resource=resource,
            action=action,
            timestamp=datetime.utcnow().isoformat(),
            event_type="security.data_access"
        )
    
    @staticmethod
    def log_security_event(event_type: str, severity: str, details: dict):
        logger.warning(
            "security_event",
            event_type=event_type,
            severity=severity,
            details=details,
            timestamp=datetime.utcnow().isoformat()
        )
```

#### **Security Controls**
- ✅ Comprehensive audit logging
- ✅ Real-time security monitoring
- ✅ Automated alerting
- ✅ Log integrity protection
- ✅ SIEM integration

---

### **A10: Server-Side Request Forgery (SSRF)**

#### **Threat Description**
Application fetching remote resources without validating user-supplied URLs.

#### **Attack Scenarios**
1. **Internal Network Scanning:** Probing internal services
2. **Cloud Metadata Access:** Accessing cloud instance metadata
3. **File System Access:** Reading local files via file:// URLs
4. **Service Abuse:** Using application as proxy for attacks

#### **Potential Impact**
- **Information Disclosure:** Internal network reconnaissance
- **Privilege Escalation:** Access to internal services
- **Data Exfiltration:** Unauthorized data access

#### **Mitigation Strategies**
```python
# SSRF prevention
import ipaddress
from urllib.parse import urlparse

class SSRFProtection:
    BLOCKED_NETWORKS = [
        ipaddress.ip_network('*********/8'),    # Loopback
        ipaddress.ip_network('10.0.0.0/8'),     # Private
        ipaddress.ip_network('**********/12'),  # Private
        ipaddress.ip_network('***********/16'), # Private
        ipaddress.ip_network('***********/16'), # Link-local
    ]
    
    @classmethod
    def validate_url(cls, url: str) -> bool:
        try:
            parsed = urlparse(url)
            
            # Only allow HTTP/HTTPS
            if parsed.scheme not in ['http', 'https']:
                return False
            
            # Resolve hostname to IP
            ip = ipaddress.ip_address(socket.gethostbyname(parsed.hostname))
            
            # Check against blocked networks
            for network in cls.BLOCKED_NETWORKS:
                if ip in network:
                    return False
            
            return True
        except Exception:
            return False
```

#### **Security Controls**
- ✅ URL validation and filtering
- ✅ Network segmentation
- ✅ Allowlist of external services
- ✅ Request timeout limits
- ✅ Response size limits

---

## 🔍 **RISK ASSESSMENT MATRIX**

| Threat Category | Likelihood | Impact | Risk Level | Priority |
|----------------|------------|--------|------------|----------|
| **A01: Broken Access Control** | High | High | Critical | P0 |
| **A02: Cryptographic Failures** | Medium | High | High | P1 |
| **A03: Injection** | Medium | High | High | P1 |
| **A04: Insecure Design** | Low | High | Medium | P2 |
| **A05: Security Misconfiguration** | Medium | Medium | Medium | P2 |
| **A06: Vulnerable Components** | High | Medium | High | P1 |
| **A07: Auth Failures** | Medium | High | High | P1 |
| **A08: Integrity Failures** | Low | High | Medium | P2 |
| **A09: Logging Failures** | Medium | Medium | Medium | P2 |
| **A10: SSRF** | Low | Medium | Low | P3 |

---

## 🛡️ **SECURITY IMPLEMENTATION ROADMAP**

### **Phase 1: Critical Security Controls (P0)**
- [ ] Implement comprehensive access control
- [ ] Deploy JWT authentication with RBAC
- [ ] Enable audit logging for all operations
- [ ] Configure security headers and HTTPS

### **Phase 2: High Priority Controls (P1)**
- [ ] Implement data encryption (AES-256)
- [ ] Deploy input validation and sanitization
- [ ] Set up automated vulnerability scanning
- [ ] Configure MFA for all users

### **Phase 3: Medium Priority Controls (P2)**
- [ ] Enhance monitoring and alerting
- [ ] Implement secure configuration management
- [ ] Deploy integrity checking mechanisms
- [ ] Complete security documentation

### **Phase 4: Additional Hardening (P3)**
- [ ] Implement SSRF protection
- [ ] Deploy advanced threat detection
- [ ] Conduct penetration testing
- [ ] Security awareness training

---

*This threat model provides comprehensive coverage of OWASP Top 10 2025 threats with specific mitigation strategies for the ImpactCV AI-powered CV generation system.*
