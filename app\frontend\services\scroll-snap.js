// Scroll snap service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ScrollSnapService {
    constructor() {
        this.containers = new Map();
        this.subscribers = new Set();
        this.initialize();
    }

    /**
     * Initialize the scroll snap service
     */
    initialize() {
        // Add scroll event listener
        window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        window.addEventListener('resize', this.handleResize.bind(this), { passive: true });
    }

    /**
     * Subscribe to scroll snap events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of scroll snap events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in scroll snap subscriber:', error);
            }
        });
    }

    /**
     * Register a scroll snap container
     * @param {string} id - The container ID
     * @param {Object} options - The scroll snap options
     */
    registerContainer(id, options = {}) {
        const container = document.getElementById(id);
        if (!container) {
            console.error(`Container ${id} not found`);
            return;
        }

        // Set scroll snap styles
        container.style.scrollSnapType = options.type || 'y mandatory';
        container.style.overflow = 'auto';
        container.style.height = '100%';

        // Get snap points
        const snapPoints = Array.from(container.children).map(child => {
            child.style.scrollSnapAlign = options.align || 'start';
            return child;
        });

        this.containers.set(id, {
            ...options,
            container,
            snapPoints,
            currentIndex: 0,
            scrolling: false,
            scrollTimeout: null,
        });

        // Initial update
        this.updateContainer(id);
    }

    /**
     * Handle scroll events
     */
    handleScroll() {
        return measurePerformance('scroll_snap_scroll', () => {
            this.containers.forEach((snap, id) => {
                this.updateContainer(id);
            });
        });
    }

    /**
     * Handle resize events
     */
    handleResize() {
        return measurePerformance('scroll_snap_resize', () => {
            this.containers.forEach((snap, id) => {
                this.updateContainer(id);
            });
        });
    }

    /**
     * Update a scroll snap container
     * @param {string} id - The container ID
     */
    updateContainer(id) {
        return measurePerformance('scroll_snap_update', () => {
            const snap = this.containers.get(id);
            if (!snap) {
                return;
            }

            const { container, snapPoints, options } = snap;
            const rect = container.getBoundingClientRect();
            const scrollTop = container.scrollTop;
            const scrollLeft = container.scrollLeft;

            // Find current snap point
            let currentIndex = 0;
            let minDistance = Infinity;

            snapPoints.forEach((point, index) => {
                const pointRect = point.getBoundingClientRect();
                const distance = Math.abs(pointRect.top - rect.top);

                if (distance < minDistance) {
                    minDistance = distance;
                    currentIndex = index;
                }
            });

            // Update current index
            if (currentIndex !== snap.currentIndex) {
                snap.currentIndex = currentIndex;

                if (snap.onSnap) {
                    snap.onSnap({
                        container,
                        index: currentIndex,
                        point: snapPoints[currentIndex],
                    });
                }

                this.notifySubscribers('snap', { id, index: currentIndex });
            }

            // Check if scrolling
            if (snap.scrollTimeout) {
                clearTimeout(snap.scrollTimeout);
            }

            snap.scrolling = true;
            snap.scrollTimeout = setTimeout(() => {
                snap.scrolling = false;

                if (snap.onScrollEnd) {
                    snap.onScrollEnd({
                        container,
                        index: currentIndex,
                        point: snapPoints[currentIndex],
                    });
                }

                this.notifySubscribers('scrollend', { id, index: currentIndex });
            }, 150);
        });
    }

    /**
     * Scroll to a snap point
     * @param {string} id - The container ID
     * @param {number} index - The snap point index
     */
    scrollTo(id, index) {
        return measurePerformance('scroll_snap_to', () => {
            const snap = this.containers.get(id);
            if (!snap) {
                return;
            }

            const { container, snapPoints } = snap;
            const point = snapPoints[index];

            if (!point) {
                return;
            }

            // Scroll to point
            container.scrollTo({
                top: point.offsetTop,
                left: point.offsetLeft,
                behavior: 'smooth',
            });

            if (snap.onScrollTo) {
                snap.onScrollTo({
                    container,
                    index,
                    point,
                });
            }

            this.notifySubscribers('scrollto', { id, index });
        });
    }

    /**
     * Scroll to the next snap point
     * @param {string} id - The container ID
     */
    scrollNext(id) {
        return measurePerformance('scroll_snap_next', () => {
            const snap = this.containers.get(id);
            if (!snap) {
                return;
            }

            const { currentIndex, snapPoints } = snap;
            const nextIndex = Math.min(currentIndex + 1, snapPoints.length - 1);

            this.scrollTo(id, nextIndex);
        });
    }

    /**
     * Scroll to the previous snap point
     * @param {string} id - The container ID
     */
    scrollPrevious(id) {
        return measurePerformance('scroll_snap_previous', () => {
            const snap = this.containers.get(id);
            if (!snap) {
                return;
            }

            const { currentIndex } = snap;
            const previousIndex = Math.max(currentIndex - 1, 0);

            this.scrollTo(id, previousIndex);
        });
    }

    /**
     * Reset a scroll snap container
     * @param {string} id - The container ID
     */
    resetContainer(id) {
        return measurePerformance('scroll_snap_reset', () => {
            const snap = this.containers.get(id);
            if (!snap) {
                return;
            }

            const { container } = snap;

            // Reset scroll
            container.scrollTo({
                top: 0,
                left: 0,
                behavior: 'instant',
            });

            // Reset state
            snap.currentIndex = 0;
            snap.scrolling = false;

            if (snap.scrollTimeout) {
                clearTimeout(snap.scrollTimeout);
                snap.scrollTimeout = null;
            }

            if (snap.onReset) {
                snap.onReset({
                    container,
                });
            }

            this.notifySubscribers('reset', { id });
        });
    }

    /**
     * Get container data
     * @param {string} id - The container ID
     * @returns {Object} The container data
     */
    getContainerData(id) {
        return this.containers.get(id);
    }

    /**
     * Update container data
     * @param {string} id - The container ID
     * @param {Object} data - The new container data
     */
    updateContainerData(id, data) {
        const snap = this.containers.get(id);
        if (snap) {
            Object.assign(snap, data);
            this.updateContainer(id);
        }
    }
}

// Create and export a singleton instance
const scrollSnapService = new ScrollSnapService();
export default scrollSnapService; 