"""
Pytest configuration and fixtures
"""

import pytest
import tempfile
import os
import asyncio
from unittest.mock import Mock, patch
from typing import Generator, Dict, Any

# Test data fixtures
@pytest.fixture
def sample_cv_data() -> Dict[str, Any]:
    """Sample CV data for testing."""
    return {
        'personal': {
            'full_name': '<PERSON>',
            'email': '<EMAIL>',
            'phone': '(*************',
            'address': '123 Main St, City, State 12345',
            'linkedin': 'https://linkedin.com/in/johndoe',
            'github': 'https://github.com/johndoe'
        },
        'summary': 'Experienced software engineer with 5+ years in web development.',
        'experience': [
            {
                'job_title': 'Senior Software Engineer',
                'company': 'Tech Corp',
                'location': 'San Francisco, CA',
                'start_date': '2020-01',
                'end_date': '2023-12',
                'is_current': False,
                'description': 'Led development of microservices architecture',
                'responsibilities': [
                    'Designed and implemented scalable APIs',
                    'Mentored junior developers',
                    'Improved system performance by 40%'
                ],
                'achievements': [
                    'Reduced deployment time by 75%',
                    'Led team of 5 developers'
                ]
            },
            {
                'job_title': 'Software Engineer',
                'company': 'StartupXYZ',
                'location': 'Austin, TX',
                'start_date': '2018-06',
                'end_date': '2020-01',
                'is_current': False,
                'description': 'Built REST APIs and web applications',
                'responsibilities': [
                    'Developed backend services',
                    'Implemented CI/CD pipelines'
                ]
            }
        ],
        'education': [
            {
                'degree': 'Bachelor of Science in Computer Science',
                'field_of_study': 'Computer Science',
                'institution': 'University of Technology',
                'start_date': '2014-09',
                'end_date': '2018-05',
                'gpa': '3.8',
                'honors': 'Magna Cum Laude'
            }
        ],
        'skills': [
            {'name': 'python', 'level': 'Expert', 'years_experience': '5', 'category': 'programming'},
            {'name': 'javascript', 'level': 'Advanced', 'years_experience': '4', 'category': 'programming'},
            {'name': 'react', 'level': 'Advanced', 'years_experience': '3', 'category': 'web_development'},
            {'name': 'django', 'level': 'Expert', 'years_experience': '4', 'category': 'web_development'},
            {'name': 'postgresql', 'level': 'Intermediate', 'years_experience': '3', 'category': 'databases'}
        ],
        'certifications': [
            {
                'name': 'AWS Certified Solutions Architect',
                'issuer': 'Amazon Web Services',
                'date_obtained': '2022-03',
                'expiry_date': '2025-03',
                'credential_id': 'AWS-SA-12345'
            },
            {
                'name': 'Certified Kubernetes Administrator',
                'issuer': 'Cloud Native Computing Foundation',
                'date_obtained': '2021-08',
                'expiry_date': '2024-08',
                'credential_id': 'CKA-67890'
            }
        ],
        'projects': [
            {
                'name': 'E-commerce Platform',
                'description': 'Built scalable e-commerce platform using microservices',
                'technologies': ['Python', 'Django', 'React', 'PostgreSQL', 'Redis'],
                'url': 'https://github.com/johndoe/ecommerce',
                'start_date': '2022-01',
                'end_date': '2022-12'
            },
            {
                'name': 'Real-time Analytics Dashboard',
                'description': 'Developed real-time analytics dashboard for business metrics',
                'technologies': ['JavaScript', 'React', 'Node.js', 'MongoDB'],
                'url': 'https://github.com/johndoe/analytics-dashboard',
                'start_date': '2021-06',
                'end_date': '2021-12'
            }
        ]
    }

@pytest.fixture
def sample_cv_text() -> str:
    """Sample CV text content for testing."""
    return """
    John Doe
    Senior Software Engineer
    <EMAIL> | (*************
    LinkedIn: linkedin.com/in/johndoe | GitHub: github.com/johndoe
    
    PROFESSIONAL SUMMARY
    Experienced software engineer with 5+ years in full-stack web development.
    Expertise in Python, JavaScript, and cloud technologies. Proven track record
    of leading teams and delivering high-impact projects.
    
    PROFESSIONAL EXPERIENCE
    
    Senior Software Engineer | Tech Corp | San Francisco, CA | Jan 2020 - Dec 2023
    • Led development of microservices architecture serving 1M+ users
    • Implemented CI/CD pipelines reducing deployment time by 75%
    • Mentored 5 junior developers and conducted code reviews
    • Technologies: Python, Django, React, PostgreSQL, AWS, Docker
    
    Software Engineer | StartupXYZ | Austin, TX | Jun 2018 - Jan 2020
    • Built REST APIs handling 10K+ requests per minute
    • Developed real-time analytics dashboard using React and WebSockets
    • Optimized database queries improving response time by 60%
    • Technologies: Python, Flask, JavaScript, MongoDB, Redis
    
    EDUCATION
    
    Bachelor of Science in Computer Science | University of Technology | 2014-2018
    GPA: 3.8/4.0 | Magna Cum Laude
    
    TECHNICAL SKILLS
    
    Programming Languages: Python, JavaScript, Java, Go, TypeScript
    Web Frameworks: Django, Flask, React, Vue.js, Node.js, Express
    Databases: PostgreSQL, MongoDB, Redis, MySQL, Elasticsearch
    Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins, GitLab CI
    
    CERTIFICATIONS
    
    • AWS Certified Solutions Architect - Associate (2022)
    • Certified Kubernetes Administrator (2021)
    • Python Institute PCAP Certification (2020)
    
    PROJECTS
    
    E-commerce Platform | github.com/johndoe/ecommerce
    Built scalable e-commerce platform using microservices architecture
    Technologies: Python, Django, React, PostgreSQL, Redis, AWS
    
    Real-time Analytics Dashboard | github.com/johndoe/analytics
    Developed real-time analytics dashboard for business metrics
    Technologies: JavaScript, React, Node.js, MongoDB
    """

@pytest.fixture
def temp_cv_file(sample_cv_text) -> Generator[str, None, None]:
    """Create temporary CV file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
        tmp.write(sample_cv_text)
        tmp_path = tmp.name
    
    yield tmp_path
    
    # Cleanup
    try:
        os.unlink(tmp_path)
    except OSError:
        pass

@pytest.fixture
def temp_pdf_file() -> Generator[str, None, None]:
    """Create temporary PDF file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
        # Write minimal PDF header
        tmp.write(b'%PDF-1.4\n%EOF\n')
        tmp_path = tmp.name
    
    yield tmp_path
    
    # Cleanup
    try:
        os.unlink(tmp_path)
    except OSError:
        pass

@pytest.fixture
def mock_llm_service():
    """Mock LLM service for testing."""
    with patch('app.services.llm_service.llm_service') as mock:
        mock.generate_completion.return_value = {
            'content': 'Generated CV content based on provided context',
            'usage': {
                'prompt_tokens': 100,
                'completion_tokens': 50,
                'total_tokens': 150
            },
            'model': 'gpt-4o',
            'finish_reason': 'stop'
        }
        
        mock.validate_prompt.return_value = {
            'is_safe': True,
            'risk_score': 0.1,
            'detected_issues': []
        }
        
        yield mock

@pytest.fixture
def mock_embedding_service():
    """Mock embedding service for testing."""
    with patch('app.services.embedding_service.embedding_service') as mock:
        # Mock embedding vector (384 dimensions for sentence-transformers)
        mock_embedding = [0.1] * 384
        
        mock.generate_embedding.return_value = mock_embedding
        mock.generate_embeddings.return_value = [mock_embedding] * 3
        mock.calculate_similarity.return_value = 0.85
        
        yield mock

@pytest.fixture
def mock_vector_store():
    """Mock vector store for testing."""
    with patch('app.services.vector_store.vector_store') as mock:
        mock.add_documents.return_value = ['doc1', 'doc2', 'doc3']
        mock.search_similar.return_value = [
            {
                'id': 'doc1',
                'content': 'Similar document 1',
                'metadata': {'type': 'cv_template'},
                'score': 0.95
            },
            {
                'id': 'doc2',
                'content': 'Similar document 2',
                'metadata': {'type': 'cv_example'},
                'score': 0.87
            }
        ]
        mock.get_document.return_value = {
            'id': 'doc1',
            'content': 'Document content',
            'metadata': {'type': 'cv_template'}
        }
        
        yield mock

@pytest.fixture
def mock_database():
    """Mock database for testing."""
    with patch('app.core.database.get_db') as mock:
        mock_session = Mock()
        mock.return_value = mock_session
        yield mock_session

# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Timer fixture for performance testing."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()

# Security testing fixtures
@pytest.fixture
def malicious_inputs():
    """Common malicious inputs for security testing."""
    return {
        'sql_injection': [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'/*",
            "' UNION SELECT * FROM users --"
        ],
        'xss_payloads': [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>"
        ],
        'command_injection': [
            "; cat /etc/passwd",
            "$(rm -rf /)",
            "`rm -rf /`",
            "| nc -l 4444"
        ],
        'path_traversal': [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc/passwd"
        ]
    }

@pytest.fixture
def pii_test_data():
    """PII test data for privacy testing."""
    return {
        'emails': [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ],
        'phones': [
            '(*************',
            '******-555-0123',
            '************'
        ],
        'ssns': [
            '***********',
            '***********'
        ],
        'credit_cards': [
            '4532-1234-5678-9012',
            '5555-5555-5555-4444'
        ],
        'addresses': [
            '123 Main Street, Anytown, ST 12345',
            '456 Oak Avenue, Suite 100, City, State 67890'
        ]
    }

# Async testing support
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# Test environment setup
@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Setup test environment variables."""
    # Set test environment variables
    monkeypatch.setenv("ENVIRONMENT", "test")
    monkeypatch.setenv("TESTING", "true")
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("LOG_LEVEL", "DEBUG")

    # Disable rate limiting for tests
    monkeypatch.setenv("RATE_LIMIT_ENABLED", "false")
    
    # Mock external services
    monkeypatch.setenv("OPENAI_API_KEY", "test-api-key")
    monkeypatch.setenv("DATABASE_URL", "sqlite:///:memory:")

# Cleanup fixtures
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Cleanup after each test."""
    yield
    
    # Clean up any temporary files
    import glob
    temp_files = glob.glob("/tmp/test_*")
    for file_path in temp_files:
        try:
            os.unlink(file_path)
        except OSError:
            pass

# Markers for test categorization
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "security: mark test as a security test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "compliance: mark test as a compliance test"
    )

# Test collection customization
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on file location."""
    for item in items:
        # Add markers based on test file location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "security" in str(item.fspath):
            item.add_marker(pytest.mark.security)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
        elif "compliance" in str(item.fspath):
            item.add_marker(pytest.mark.compliance)
        
        # Mark slow tests
        if "test_large" in item.name or "test_stress" in item.name or "performance" in str(item.fspath):
            item.add_marker(pytest.mark.slow)
        else:
            item.add_marker(pytest.mark.fast)
