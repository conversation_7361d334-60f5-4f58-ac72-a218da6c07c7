// Drag and drop service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class DragDropService {
    constructor() {
        this.draggables = new Map();
        this.dropzones = new Map();
        this.subscribers = new Set();
        this.draggedElement = null;
        this.dragImage = null;
        this.dragOffset = { x: 0, y: 0 };
        this.initialize();
    }

    /**
     * Initialize the drag and drop service
     */
    initialize() {
        document.addEventListener('dragstart', this.handleDragStart.bind(this));
        document.addEventListener('dragend', this.handleDragEnd.bind(this));
        document.addEventListener('dragover', this.handleDragOver.bind(this));
        document.addEventListener('drop', this.handleDrop.bind(this));
    }

    /**
     * Subscribe to drag and drop events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of drag and drop events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in drag and drop subscriber:', error);
            }
        });
    }

    /**
     * Register a draggable element
     * @param {string} id - The element ID
     * @param {Object} options - The draggable options
     */
    registerDraggable(id, options = {}) {
        const element = document.getElementById(id);
        if (!element) {
            console.error(`Element ${id} not found`);
            return;
        }

        element.draggable = true;
        this.draggables.set(id, {
            ...options,
            element,
        });
    }

    /**
     * Register a dropzone
     * @param {string} id - The element ID
     * @param {Object} options - The dropzone options
     */
    registerDropzone(id, options = {}) {
        const element = document.getElementById(id);
        if (!element) {
            console.error(`Element ${id} not found`);
            return;
        }

        this.dropzones.set(id, {
            ...options,
            element,
        });
    }

    /**
     * Handle drag start events
     * @param {DragEvent} event - The drag event
     */
    handleDragStart(event) {
        return measurePerformance('dragdrop_dragstart', () => {
            const id = event.target.id;
            const draggable = this.draggables.get(id);
            if (!draggable) {
                return;
            }

            this.draggedElement = draggable;
            this.dragOffset = {
                x: event.clientX - event.target.getBoundingClientRect().left,
                y: event.clientY - event.target.getBoundingClientRect().top,
            };

            // Set drag image
            if (draggable.dragImage) {
                this.dragImage = draggable.dragImage;
                event.dataTransfer.setDragImage(this.dragImage, this.dragOffset.x, this.dragOffset.y);
            }

            // Set drag data
            event.dataTransfer.setData('text/plain', id);
            event.dataTransfer.effectAllowed = draggable.effectAllowed || 'move';

            if (draggable.onDragStart) {
                draggable.onDragStart(event);
            }

            this.notifySubscribers('dragstart', { id, event });
        });
    }

    /**
     * Handle drag end events
     * @param {DragEvent} event - The drag event
     */
    handleDragEnd(event) {
        return measurePerformance('dragdrop_dragend', () => {
            const id = event.target.id;
            const draggable = this.draggables.get(id);
            if (!draggable) {
                return;
            }

            if (draggable.onDragEnd) {
                draggable.onDragEnd(event);
            }

            this.draggedElement = null;
            this.dragImage = null;
            this.dragOffset = { x: 0, y: 0 };

            this.notifySubscribers('dragend', { id, event });
        });
    }

    /**
     * Handle drag over events
     * @param {DragEvent} event - The drag event
     */
    handleDragOver(event) {
        return measurePerformance('dragdrop_dragover', () => {
            event.preventDefault();

            const dropzone = this.findDropzone(event.target);
            if (!dropzone) {
                return;
            }

            event.dataTransfer.dropEffect = dropzone.dropEffect || 'move';

            if (dropzone.onDragOver) {
                dropzone.onDragOver(event);
            }

            this.notifySubscribers('dragover', { id: dropzone.id, event });
        });
    }

    /**
     * Handle drop events
     * @param {DragEvent} event - The drop event
     */
    handleDrop(event) {
        return measurePerformance('dragdrop_drop', () => {
            event.preventDefault();

            const dropzone = this.findDropzone(event.target);
            if (!dropzone) {
                return;
            }

            const draggableId = event.dataTransfer.getData('text/plain');
            const draggable = this.draggables.get(draggableId);
            if (!draggable) {
                return;
            }

            if (dropzone.onDrop) {
                dropzone.onDrop(event, draggable);
            }

            this.notifySubscribers('drop', {
                draggableId,
                dropzoneId: dropzone.id,
                event,
            });
        });
    }

    /**
     * Find a dropzone for an element
     * @param {HTMLElement} element - The element
     * @returns {Object|null} The dropzone
     */
    findDropzone(element) {
        while (element) {
            const dropzone = this.dropzones.get(element.id);
            if (dropzone) {
                return { ...dropzone, id: element.id };
            }
            element = element.parentElement;
        }
        return null;
    }

    /**
     * Get draggable data
     * @param {string} id - The draggable ID
     * @returns {Object} The draggable data
     */
    getDraggableData(id) {
        return this.draggables.get(id);
    }

    /**
     * Get dropzone data
     * @param {string} id - The dropzone ID
     * @returns {Object} The dropzone data
     */
    getDropzoneData(id) {
        return this.dropzones.get(id);
    }

    /**
     * Update draggable data
     * @param {string} id - The draggable ID
     * @param {Object} data - The new draggable data
     */
    updateDraggableData(id, data) {
        const draggable = this.draggables.get(id);
        if (draggable) {
            Object.assign(draggable, data);
        }
    }

    /**
     * Update dropzone data
     * @param {string} id - The dropzone ID
     * @param {Object} data - The new dropzone data
     */
    updateDropzoneData(id, data) {
        const dropzone = this.dropzones.get(id);
        if (dropzone) {
            Object.assign(dropzone, data);
        }
    }
}

// Create and export a singleton instance
const dragDropService = new DragDropService();
export default dragDropService; 