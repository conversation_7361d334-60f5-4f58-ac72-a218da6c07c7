"""
Data Normalization Service
Consistent data format transformation and standardization
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class NormalizedData(BaseModel):
    """Normalized data model."""
    
    original_data: Dict[str, Any] = Field(..., description="Original input data")
    normalized_data: Dict[str, Any] = Field(..., description="Normalized output data")
    transformations: List[str] = Field(default_factory=list, description="Applied transformations")
    confidence_score: float = Field(default=1.0, description="Normalization confidence")
    warnings: List[str] = Field(default_factory=list, description="Normalization warnings")


class DataNormalizationError(Exception):
    """Custom exception for data normalization errors."""
    
    def __init__(self, message: str, error_code: str = "NORMALIZATION_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DataNormalizer:
    """
    Enterprise data normalization service.
    
    Features:
    - CV data standardization
    - Contact information normalization
    - Date format standardization
    - Text cleaning and formatting
    - Skill categorization
    - Experience level mapping
    """
    
    def __init__(self):
        """Initialize data normalizer."""
        self.phone_pattern = re.compile(r'[\+]?[1-9]?[\d\s\-\(\)\.]{7,15}')
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.url_pattern = re.compile(r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?')
        
        # Skill categories mapping
        self.skill_categories = {
            'programming': [
                'python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'go', 'rust',
                'php', 'ruby', 'swift', 'kotlin', 'scala', 'r', 'matlab', 'sql'
            ],
            'web_development': [
                'html', 'css', 'react', 'angular', 'vue', 'node.js', 'express',
                'django', 'flask', 'spring', 'laravel', 'wordpress'
            ],
            'databases': [
                'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch',
                'oracle', 'sql server', 'sqlite', 'cassandra', 'dynamodb'
            ],
            'cloud_platforms': [
                'aws', 'azure', 'gcp', 'google cloud', 'docker', 'kubernetes',
                'terraform', 'ansible', 'jenkins', 'gitlab'
            ],
            'data_science': [
                'machine learning', 'deep learning', 'tensorflow', 'pytorch',
                'pandas', 'numpy', 'scikit-learn', 'tableau', 'power bi'
            ],
            'soft_skills': [
                'leadership', 'communication', 'teamwork', 'problem solving',
                'project management', 'agile', 'scrum', 'mentoring'
            ]
        }
        
        # Experience level mapping
        self.experience_levels = {
            'entry': ['entry', 'junior', 'graduate', 'intern', 'trainee', '0-2 years'],
            'mid': ['mid', 'intermediate', 'experienced', '2-5 years', '3-7 years'],
            'senior': ['senior', 'lead', 'principal', '5+ years', '7+ years'],
            'executive': ['director', 'manager', 'head', 'vp', 'cto', 'ceo', '10+ years']
        }
    
    def normalize_cv_data(self, cv_data: Dict[str, Any]) -> NormalizedData:
        """
        Normalize CV data to standard format.
        
        Args:
            cv_data: Raw CV data dictionary
            
        Returns:
            NormalizedData: Normalized CV data with metadata
        """
        try:
            normalized = {}
            transformations = []
            warnings = []
            confidence_score = 1.0
            
            # Normalize personal information
            if 'personal' in cv_data or any(key in cv_data for key in ['name', 'email', 'phone']):
                personal_data = cv_data.get('personal', {})
                if 'name' in cv_data:
                    personal_data['name'] = cv_data['name']
                if 'email' in cv_data:
                    personal_data['email'] = cv_data['email']
                if 'phone' in cv_data:
                    personal_data['phone'] = cv_data['phone']
                
                normalized['personal'] = self._normalize_personal_info(personal_data)
                transformations.append('personal_info_normalized')
            
            # Normalize experience
            if 'experience' in cv_data or 'work_history' in cv_data:
                exp_data = cv_data.get('experience', cv_data.get('work_history', []))
                normalized['experience'] = self._normalize_experience(exp_data)
                transformations.append('experience_normalized')
            
            # Normalize education
            if 'education' in cv_data:
                normalized['education'] = self._normalize_education(cv_data['education'])
                transformations.append('education_normalized')
            
            # Normalize skills
            if 'skills' in cv_data:
                normalized['skills'] = self._normalize_skills(cv_data['skills'])
                transformations.append('skills_normalized')
            
            # Normalize summary/objective
            if 'summary' in cv_data or 'objective' in cv_data:
                summary_text = cv_data.get('summary', cv_data.get('objective', ''))
                normalized['summary'] = self._normalize_text(summary_text)
                transformations.append('summary_normalized')
            
            # Normalize certifications
            if 'certifications' in cv_data:
                normalized['certifications'] = self._normalize_certifications(cv_data['certifications'])
                transformations.append('certifications_normalized')
            
            # Normalize projects
            if 'projects' in cv_data:
                normalized['projects'] = self._normalize_projects(cv_data['projects'])
                transformations.append('projects_normalized')
            
            # Add metadata
            normalized['metadata'] = {
                'normalized_at': datetime.utcnow().isoformat(),
                'total_experience_years': self._calculate_total_experience(normalized.get('experience', [])),
                'skill_categories': self._categorize_all_skills(normalized.get('skills', [])),
                'experience_level': self._determine_experience_level(normalized.get('experience', [])),
            }
            
            return NormalizedData(
                original_data=cv_data,
                normalized_data=normalized,
                transformations=transformations,
                confidence_score=confidence_score,
                warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"CV data normalization failed: {e}")
            raise DataNormalizationError(
                f"CV data normalization failed: {str(e)}",
                error_code="CV_NORMALIZATION_FAILED"
            )
    
    def _normalize_personal_info(self, personal_data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize personal information."""
        normalized = {}
        
        # Normalize name
        if 'name' in personal_data:
            name = str(personal_data['name']).strip()
            normalized['full_name'] = self._normalize_text(name)
            
            # Split name into components
            name_parts = name.split()
            if len(name_parts) >= 2:
                normalized['first_name'] = name_parts[0]
                normalized['last_name'] = name_parts[-1]
                if len(name_parts) > 2:
                    normalized['middle_name'] = ' '.join(name_parts[1:-1])
        
        # Normalize email
        if 'email' in personal_data:
            email = str(personal_data['email']).strip().lower()
            if self.email_pattern.match(email):
                normalized['email'] = email
        
        # Normalize phone
        if 'phone' in personal_data:
            phone = self._normalize_phone(str(personal_data['phone']))
            if phone:
                normalized['phone'] = phone
        
        # Normalize address
        if 'address' in personal_data:
            normalized['address'] = self._normalize_text(str(personal_data['address']))
        
        # Normalize social links
        for field in ['linkedin', 'github', 'website', 'portfolio']:
            if field in personal_data:
                url = str(personal_data[field]).strip()
                if self.url_pattern.match(url) or url.startswith('www.'):
                    normalized[field] = url if url.startswith('http') else f"https://{url}"
        
        return normalized
    
    def _normalize_experience(self, experience_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Normalize work experience."""
        normalized_exp = []
        
        for exp in experience_data:
            if not isinstance(exp, dict):
                continue
            
            normalized_item = {}
            
            # Job title
            if 'title' in exp or 'position' in exp or 'job_title' in exp:
                title = exp.get('title', exp.get('position', exp.get('job_title', '')))
                normalized_item['job_title'] = self._normalize_text(str(title))
            
            # Company
            if 'company' in exp or 'employer' in exp:
                company = exp.get('company', exp.get('employer', ''))
                normalized_item['company'] = self._normalize_text(str(company))
            
            # Location
            if 'location' in exp:
                normalized_item['location'] = self._normalize_text(str(exp['location']))
            
            # Dates
            start_date = self._normalize_date(exp.get('start_date', exp.get('from', '')))
            end_date = self._normalize_date(exp.get('end_date', exp.get('to', '')))
            
            if start_date:
                normalized_item['start_date'] = start_date
            if end_date:
                normalized_item['end_date'] = end_date
            
            # Current job indicator
            normalized_item['is_current'] = exp.get('current', False) or end_date == 'present'
            
            # Description
            if 'description' in exp:
                normalized_item['description'] = self._normalize_text(str(exp['description']))
            
            # Responsibilities (if separate from description)
            if 'responsibilities' in exp:
                responsibilities = exp['responsibilities']
                if isinstance(responsibilities, list):
                    normalized_item['responsibilities'] = [
                        self._normalize_text(str(resp)) for resp in responsibilities
                    ]
                else:
                    normalized_item['responsibilities'] = [self._normalize_text(str(responsibilities))]
            
            # Achievements
            if 'achievements' in exp:
                achievements = exp['achievements']
                if isinstance(achievements, list):
                    normalized_item['achievements'] = [
                        self._normalize_text(str(ach)) for ach in achievements
                    ]
                else:
                    normalized_item['achievements'] = [self._normalize_text(str(achievements))]
            
            normalized_exp.append(normalized_item)
        
        # Sort by start date (most recent first)
        normalized_exp.sort(key=lambda x: x.get('start_date', ''), reverse=True)
        
        return normalized_exp
    
    def _normalize_education(self, education_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Normalize education information."""
        normalized_edu = []
        
        for edu in education_data:
            if not isinstance(edu, dict):
                continue
            
            normalized_item = {}
            
            # Degree
            if 'degree' in edu:
                normalized_item['degree'] = self._normalize_text(str(edu['degree']))
            
            # Field of study
            if 'field' in edu or 'major' in edu or 'field_of_study' in edu:
                field = edu.get('field', edu.get('major', edu.get('field_of_study', '')))
                normalized_item['field_of_study'] = self._normalize_text(str(field))
            
            # Institution
            if 'school' in edu or 'university' in edu or 'institution' in edu:
                institution = edu.get('school', edu.get('university', edu.get('institution', '')))
                normalized_item['institution'] = self._normalize_text(str(institution))
            
            # Dates
            start_date = self._normalize_date(edu.get('start_date', edu.get('from', '')))
            end_date = self._normalize_date(edu.get('end_date', edu.get('to', '')))
            
            if start_date:
                normalized_item['start_date'] = start_date
            if end_date:
                normalized_item['end_date'] = end_date
            
            # GPA
            if 'gpa' in edu:
                normalized_item['gpa'] = str(edu['gpa'])
            
            # Honors
            if 'honors' in edu:
                normalized_item['honors'] = self._normalize_text(str(edu['honors']))
            
            normalized_edu.append(normalized_item)
        
        # Sort by end date (most recent first)
        normalized_edu.sort(key=lambda x: x.get('end_date', ''), reverse=True)
        
        return normalized_edu
    
    def _normalize_skills(self, skills_data: Union[List[str], List[Dict], str]) -> List[Dict[str, Any]]:
        """Normalize skills information."""
        normalized_skills = []
        
        # Handle different skill formats
        if isinstance(skills_data, str):
            # Split comma-separated skills
            skills_list = [skill.strip() for skill in skills_data.split(',')]
        elif isinstance(skills_data, list):
            skills_list = skills_data
        else:
            return normalized_skills
        
        for skill in skills_list:
            if isinstance(skill, dict):
                # Skill with metadata
                normalized_skill = {
                    'name': self._normalize_text(str(skill.get('name', skill.get('skill', ''))).lower()),
                    'level': skill.get('level', ''),
                    'years_experience': skill.get('years', skill.get('experience', '')),
                }
            else:
                # Simple skill string
                normalized_skill = {
                    'name': self._normalize_text(str(skill)).lower(),
                    'level': '',
                    'years_experience': '',
                }
            
            # Categorize skill
            normalized_skill['category'] = self._categorize_skill(normalized_skill['name'])
            
            if normalized_skill['name']:
                normalized_skills.append(normalized_skill)
        
        return normalized_skills
    
    def _normalize_certifications(self, cert_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Normalize certifications."""
        normalized_certs = []
        
        for cert in cert_data:
            if not isinstance(cert, dict):
                continue
            
            normalized_cert = {
                'name': self._normalize_text(str(cert.get('name', cert.get('certification', '')))),
                'issuer': self._normalize_text(str(cert.get('issuer', cert.get('organization', '')))),
                'date_obtained': self._normalize_date(cert.get('date', cert.get('obtained', ''))),
                'expiry_date': self._normalize_date(cert.get('expiry', cert.get('expires', ''))),
                'credential_id': str(cert.get('credential_id', cert.get('id', ''))),
            }
            
            if normalized_cert['name']:
                normalized_certs.append(normalized_cert)
        
        return normalized_certs
    
    def _normalize_projects(self, project_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Normalize projects."""
        normalized_projects = []
        
        for project in project_data:
            if not isinstance(project, dict):
                continue
            
            normalized_project = {
                'name': self._normalize_text(str(project.get('name', project.get('title', '')))),
                'description': self._normalize_text(str(project.get('description', ''))),
                'technologies': project.get('technologies', project.get('tech_stack', [])),
                'url': project.get('url', project.get('link', '')),
                'start_date': self._normalize_date(project.get('start_date', '')),
                'end_date': self._normalize_date(project.get('end_date', '')),
            }
            
            if normalized_project['name']:
                normalized_projects.append(normalized_project)
        
        return normalized_projects
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text content."""
        if not text:
            return ''
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters that might cause issues
        text = re.sub(r'[^\w\s\-\.\,\(\)\&\+\#]', '', text)
        
        return text
    
    def _normalize_phone(self, phone: str) -> str:
        """Normalize phone number."""
        # Remove all non-digit characters except +
        phone = re.sub(r'[^\d\+]', '', phone)
        
        # Basic validation
        if len(phone) >= 7 and len(phone) <= 15:
            return phone
        
        return ''
    
    def _normalize_date(self, date_str: str) -> str:
        """Normalize date string."""
        if not date_str:
            return ''
        
        date_str = str(date_str).strip().lower()
        
        # Handle special cases
        if date_str in ['present', 'current', 'now']:
            return 'present'
        
        # Try to parse common date formats
        date_patterns = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
            r'(\d{1,2})/(\d{1,2})/(\d{4})',  # MM/DD/YYYY
            r'(\d{1,2})-(\d{1,2})-(\d{4})',  # MM-DD-YYYY
            r'(\w+)\s+(\d{4})',              # Month YYYY
            r'(\d{4})',                      # YYYY only
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                return match.group(0)
        
        return date_str
    
    def _categorize_skill(self, skill_name: str) -> str:
        """Categorize a skill."""
        skill_lower = skill_name.lower()
        
        for category, skills in self.skill_categories.items():
            if any(skill in skill_lower for skill in skills):
                return category
        
        return 'other'
    
    def _categorize_all_skills(self, skills: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Categorize all skills."""
        categorized = {}
        
        for skill in skills:
            category = skill.get('category', 'other')
            if category not in categorized:
                categorized[category] = []
            categorized[category].append(skill['name'])
        
        return categorized
    
    def _calculate_total_experience(self, experience: List[Dict[str, Any]]) -> float:
        """Calculate total years of experience."""
        total_years = 0.0
        
        for exp in experience:
            start_date = exp.get('start_date', '')
            end_date = exp.get('end_date', 'present')
            
            # Simple calculation (can be enhanced)
            if start_date and len(start_date) >= 4:
                try:
                    start_year = int(start_date[:4])
                    if end_date == 'present':
                        end_year = datetime.now().year
                    elif len(end_date) >= 4:
                        end_year = int(end_date[:4])
                    else:
                        continue
                    
                    years = max(0, end_year - start_year)
                    total_years += years
                except ValueError:
                    continue
        
        return total_years
    
    def _determine_experience_level(self, experience: List[Dict[str, Any]]) -> str:
        """Determine experience level."""
        total_years = self._calculate_total_experience(experience)
        
        if total_years < 2:
            return 'entry'
        elif total_years < 5:
            return 'mid'
        elif total_years < 10:
            return 'senior'
        else:
            return 'executive'


# Global normalizer instance
data_normalizer = DataNormalizer()
