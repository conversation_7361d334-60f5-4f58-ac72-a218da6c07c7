# 📊 Data Lineage Tracking

> **Comprehensive Data Flow Documentation for ImpactCV AI-Powered CV Generation System**  
> **Compliance:** DAMA-DMBOK v2 | GDPR Article 30 | SOX Section 404

---

## 📋 **EXECUTIVE SUMMARY**

### **Data Lineage Overview**
ImpactCV implements comprehensive data lineage tracking to ensure complete visibility of data flow from source to destination. This enables regulatory compliance, impact analysis, data quality monitoring, and root cause analysis across the entire AI-powered CV generation pipeline.

### **Key Benefits**
1. **Regulatory Compliance** - GDPR Article 30 record-keeping requirements
2. **Impact Analysis** - Understand downstream effects of data changes
3. **Data Quality** - Track quality degradation through the pipeline
4. **Audit Trail** - Complete data processing history for compliance

---

## 🔄 **END-TO-END DATA FLOW ARCHITECTURE**

### **Data Flow Overview**

```mermaid
graph TD
    subgraph "Data Sources"
        USER_UPLOAD[User Document Upload]
        USER_PROFILE[User Profile Data]
        TEMPLATE_DATA[CV Templates]
        EXTERNAL_API[OpenAI API Responses]
    end
    
    subgraph "Ingestion Layer"
        FILE_PARSER[Document Parser]
        VALIDATION[Data Validation]
        PII_DETECTION[PII Detection]
        SANITIZATION[Data Sanitization]
    end
    
    subgraph "Processing Layer"
        TEXT_EXTRACTION[Text Extraction]
        EMBEDDING_GEN[Embedding Generation]
        RAG_PROCESSING[RAG Processing]
        AI_ENHANCEMENT[AI Enhancement]
    end
    
    subgraph "Storage Layer"
        POSTGRES[(PostgreSQL)]
        REDIS[(Redis Cache)]
        FAISS[(Vector Database)]
        FILE_STORAGE[Encrypted File Storage]
    end
    
    subgraph "Output Layer"
        CV_GENERATION[CV Generation]
        PDF_CREATION[PDF Creation]
        DOWNLOAD[User Download]
        AUDIT_LOG[Audit Logging]
    end
    
    USER_UPLOAD --> FILE_PARSER
    USER_PROFILE --> VALIDATION
    TEMPLATE_DATA --> VALIDATION
    
    FILE_PARSER --> TEXT_EXTRACTION
    VALIDATION --> PII_DETECTION
    PII_DETECTION --> SANITIZATION
    
    TEXT_EXTRACTION --> EMBEDDING_GEN
    SANITIZATION --> RAG_PROCESSING
    EMBEDDING_GEN --> FAISS
    RAG_PROCESSING --> AI_ENHANCEMENT
    
    AI_ENHANCEMENT --> POSTGRES
    FAISS --> RAG_PROCESSING
    POSTGRES --> CV_GENERATION
    REDIS --> CV_GENERATION
    
    CV_GENERATION --> PDF_CREATION
    PDF_CREATION --> FILE_STORAGE
    FILE_STORAGE --> DOWNLOAD
    
    CV_GENERATION --> AUDIT_LOG
    AUDIT_LOG --> POSTGRES
    
    EXTERNAL_API --> AI_ENHANCEMENT
```

### **Data Lineage Tracking Points**

| Stage | Input Data | Processing | Output Data | Lineage ID |
|-------|------------|------------|-------------|------------|
| **Ingestion** | Raw documents (PDF/DOCX) | Document parsing, validation | Structured text data | `LIN_001` |
| **PII Detection** | Structured text | PII scanning, classification | Anonymized/flagged data | `LIN_002` |
| **Text Processing** | Clean text data | Chunking, normalization | Processed text chunks | `LIN_003` |
| **Embedding** | Text chunks | OpenAI embedding generation | Vector embeddings | `LIN_004` |
| **Vector Storage** | Vector embeddings | FAISS indexing | Searchable vectors | `LIN_005` |
| **RAG Processing** | User query + vectors | Similarity search, context retrieval | Enhanced context | `LIN_006` |
| **AI Enhancement** | Context + user data | GPT-4o processing | Enhanced content | `LIN_007` |
| **CV Generation** | Enhanced content + template | Template rendering | Generated CV | `LIN_008` |
| **Output** | Generated CV | PDF conversion, encryption | Final deliverable | `LIN_009` |

---

## 🏗️ **DATA LINEAGE IMPLEMENTATION**

### **Lineage Tracking Schema**

```python
# Data lineage tracking implementation
from sqlalchemy import Column, String, DateTime, Text, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class DataLineageRecord(Base):
    __tablename__ = 'data_lineage'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    lineage_id = Column(String, nullable=False, index=True)  # LIN_001, LIN_002, etc.
    
    # Source information
    source_system = Column(String, nullable=False)
    source_table = Column(String, nullable=True)
    source_record_id = Column(String, nullable=True)
    source_schema = Column(JSON, nullable=True)
    
    # Target information
    target_system = Column(String, nullable=False)
    target_table = Column(String, nullable=True)
    target_record_id = Column(String, nullable=True)
    target_schema = Column(JSON, nullable=True)
    
    # Processing information
    transformation_type = Column(String, nullable=False)
    transformation_logic = Column(Text, nullable=True)
    processing_timestamp = Column(DateTime, default=datetime.utcnow)
    processing_duration_ms = Column(Integer, nullable=True)
    
    # Data quality metrics
    input_record_count = Column(Integer, nullable=True)
    output_record_count = Column(Integer, nullable=True)
    quality_score = Column(Float, nullable=True)
    data_issues = Column(JSON, nullable=True)
    
    # Compliance tracking
    pii_detected = Column(Boolean, default=False)
    gdpr_lawful_basis = Column(String, nullable=True)
    retention_policy_id = Column(String, nullable=True)
    
    # User and session context
    user_id = Column(String, nullable=True)
    session_id = Column(String, nullable=True)
    request_id = Column(String, nullable=True)
    
    # Relationships
    parent_lineage_id = Column(String, ForeignKey('data_lineage.id'), nullable=True)
    children = relationship("DataLineageRecord", backref="parent", remote_side=[id])

class DataLineageTracker:
    def __init__(self, db_session):
        self.db_session = db_session
    
    def track_transformation(
        self,
        lineage_id: str,
        source_system: str,
        target_system: str,
        transformation_type: str,
        source_data: dict = None,
        target_data: dict = None,
        user_id: str = None,
        session_id: str = None,
        parent_lineage_id: str = None
    ) -> str:
        """Track a data transformation in the lineage"""
        
        record = DataLineageRecord(
            lineage_id=lineage_id,
            source_system=source_system,
            target_system=target_system,
            transformation_type=transformation_type,
            user_id=user_id,
            session_id=session_id,
            parent_lineage_id=parent_lineage_id
        )
        
        # Add source information
        if source_data:
            record.source_table = source_data.get('table')
            record.source_record_id = source_data.get('record_id')
            record.source_schema = source_data.get('schema')
            record.input_record_count = source_data.get('record_count', 1)
        
        # Add target information
        if target_data:
            record.target_table = target_data.get('table')
            record.target_record_id = target_data.get('record_id')
            record.target_schema = target_data.get('schema')
            record.output_record_count = target_data.get('record_count', 1)
        
        self.db_session.add(record)
        self.db_session.commit()
        
        return record.id
    
    def get_lineage_chain(self, record_id: str) -> list:
        """Get complete lineage chain for a record"""
        lineage_chain = []
        
        # Get all records in the lineage chain
        records = self.db_session.query(DataLineageRecord).filter(
            DataLineageRecord.target_record_id == record_id
        ).order_by(DataLineageRecord.processing_timestamp).all()
        
        for record in records:
            lineage_chain.append({
                'lineage_id': record.lineage_id,
                'transformation': record.transformation_type,
                'source': f"{record.source_system}.{record.source_table}",
                'target': f"{record.target_system}.{record.target_table}",
                'timestamp': record.processing_timestamp,
                'quality_score': record.quality_score,
                'pii_detected': record.pii_detected
            })
        
        return lineage_chain
    
    def get_impact_analysis(self, source_record_id: str) -> dict:
        """Analyze downstream impact of changes to a source record"""
        
        # Find all records that depend on this source
        dependent_records = self.db_session.query(DataLineageRecord).filter(
            DataLineageRecord.source_record_id == source_record_id
        ).all()
        
        impact_analysis = {
            'source_record_id': source_record_id,
            'total_dependent_records': len(dependent_records),
            'affected_systems': set(),
            'affected_users': set(),
            'processing_chain': []
        }
        
        for record in dependent_records:
            impact_analysis['affected_systems'].add(record.target_system)
            if record.user_id:
                impact_analysis['affected_users'].add(record.user_id)
            
            impact_analysis['processing_chain'].append({
                'lineage_id': record.lineage_id,
                'target_system': record.target_system,
                'transformation': record.transformation_type,
                'timestamp': record.processing_timestamp
            })
        
        impact_analysis['affected_systems'] = list(impact_analysis['affected_systems'])
        impact_analysis['affected_users'] = list(impact_analysis['affected_users'])
        
        return impact_analysis
```

### **Service Integration**

```python
# Integration with CV generation services
class CVGenerationService:
    def __init__(self, lineage_tracker: DataLineageTracker):
        self.lineage_tracker = lineage_tracker
    
    async def process_document(self, document: UploadFile, user_id: str, session_id: str) -> dict:
        """Process uploaded document with lineage tracking"""
        
        # Track document upload
        upload_lineage_id = self.lineage_tracker.track_transformation(
            lineage_id="LIN_001",
            source_system="user_upload",
            target_system="document_parser",
            transformation_type="document_ingestion",
            source_data={
                'table': 'user_uploads',
                'record_id': document.filename,
                'schema': {'filename': document.filename, 'content_type': document.content_type}
            },
            user_id=user_id,
            session_id=session_id
        )
        
        # Parse document
        parsed_content = await self.parse_document(document)
        
        # Track parsing transformation
        parsing_lineage_id = self.lineage_tracker.track_transformation(
            lineage_id="LIN_002",
            source_system="document_parser",
            target_system="text_processor",
            transformation_type="document_parsing",
            source_data={
                'table': 'raw_documents',
                'record_id': document.filename
            },
            target_data={
                'table': 'parsed_text',
                'record_id': parsed_content['id'],
                'schema': parsed_content['schema']
            },
            user_id=user_id,
            session_id=session_id,
            parent_lineage_id=upload_lineage_id
        )
        
        # Continue with PII detection
        pii_result = await self.detect_pii(parsed_content)
        
        # Track PII detection
        pii_lineage_id = self.lineage_tracker.track_transformation(
            lineage_id="LIN_003",
            source_system="text_processor",
            target_system="pii_detector",
            transformation_type="pii_detection",
            source_data={
                'table': 'parsed_text',
                'record_id': parsed_content['id']
            },
            target_data={
                'table': 'sanitized_text',
                'record_id': pii_result['id'],
                'schema': pii_result['schema']
            },
            user_id=user_id,
            session_id=session_id,
            parent_lineage_id=parsing_lineage_id
        )
        
        return {
            'document_id': pii_result['id'],
            'lineage_chain': [upload_lineage_id, parsing_lineage_id, pii_lineage_id],
            'pii_detected': pii_result['pii_detected']
        }
```

---

## 📊 **DATA QUALITY INTEGRATION**

### **Quality Metrics Tracking**

```python
# Data quality metrics in lineage
class DataQualityTracker:
    def __init__(self, lineage_tracker: DataLineageTracker):
        self.lineage_tracker = lineage_tracker
    
    def assess_data_quality(self, data: dict, stage: str) -> dict:
        """Assess data quality at each lineage stage"""
        
        quality_metrics = {
            'completeness': self.calculate_completeness(data),
            'accuracy': self.calculate_accuracy(data, stage),
            'consistency': self.calculate_consistency(data),
            'validity': self.calculate_validity(data, stage),
            'timeliness': self.calculate_timeliness(data)
        }
        
        # Calculate overall quality score
        overall_score = sum(quality_metrics.values()) / len(quality_metrics)
        
        return {
            'overall_score': overall_score,
            'metrics': quality_metrics,
            'issues': self.identify_quality_issues(quality_metrics),
            'recommendations': self.generate_recommendations(quality_metrics)
        }
    
    def calculate_completeness(self, data: dict) -> float:
        """Calculate data completeness score"""
        if not data:
            return 0.0
        
        total_fields = len(data)
        non_null_fields = sum(1 for value in data.values() if value is not None and value != "")
        
        return non_null_fields / total_fields if total_fields > 0 else 0.0
    
    def calculate_accuracy(self, data: dict, stage: str) -> float:
        """Calculate data accuracy based on validation rules"""
        accuracy_score = 1.0
        
        # Stage-specific accuracy checks
        if stage == "document_parsing":
            # Check if extracted text makes sense
            text_content = data.get('content', '')
            if len(text_content) < 100:  # Too short
                accuracy_score -= 0.3
            if not any(char.isalpha() for char in text_content):  # No letters
                accuracy_score -= 0.5
        
        elif stage == "pii_detection":
            # Check PII detection accuracy
            pii_patterns = data.get('pii_patterns', [])
            if 'email' in str(data) and 'email' not in pii_patterns:
                accuracy_score -= 0.2
        
        return max(0.0, accuracy_score)
    
    def identify_quality_issues(self, metrics: dict) -> list:
        """Identify specific data quality issues"""
        issues = []
        
        if metrics['completeness'] < 0.8:
            issues.append("Low data completeness - missing required fields")
        
        if metrics['accuracy'] < 0.9:
            issues.append("Data accuracy concerns - validation failures detected")
        
        if metrics['consistency'] < 0.85:
            issues.append("Data consistency issues - conflicting values found")
        
        return issues
```

---

## 🔍 **LINEAGE VISUALIZATION & REPORTING**

### **Lineage Dashboard**

```python
# Lineage visualization and reporting
class LineageDashboard:
    def __init__(self, lineage_tracker: DataLineageTracker):
        self.lineage_tracker = lineage_tracker
    
    def generate_lineage_report(self, cv_id: str) -> dict:
        """Generate comprehensive lineage report for a CV"""
        
        lineage_chain = self.lineage_tracker.get_lineage_chain(cv_id)
        
        report = {
            'cv_id': cv_id,
            'generation_timestamp': datetime.utcnow().isoformat(),
            'total_transformations': len(lineage_chain),
            'processing_stages': [],
            'data_quality_summary': {},
            'compliance_status': {},
            'performance_metrics': {}
        }
        
        # Process each stage in the lineage
        for stage in lineage_chain:
            stage_info = {
                'stage_name': stage['transformation'],
                'lineage_id': stage['lineage_id'],
                'source_system': stage['source'],
                'target_system': stage['target'],
                'processing_time': stage['timestamp'],
                'quality_score': stage['quality_score'],
                'pii_detected': stage['pii_detected']
            }
            report['processing_stages'].append(stage_info)
        
        # Calculate summary metrics
        quality_scores = [s['quality_score'] for s in lineage_chain if s['quality_score']]
        if quality_scores:
            report['data_quality_summary'] = {
                'average_quality': sum(quality_scores) / len(quality_scores),
                'min_quality': min(quality_scores),
                'max_quality': max(quality_scores),
                'quality_trend': self.calculate_quality_trend(quality_scores)
            }
        
        # Compliance status
        report['compliance_status'] = {
            'gdpr_compliant': not any(s['pii_detected'] for s in lineage_chain),
            'audit_trail_complete': len(lineage_chain) >= 5,  # Minimum expected stages
            'retention_policy_applied': True  # Check actual retention policies
        }
        
        return report
    
    def export_lineage_graph(self, cv_id: str, format: str = "mermaid") -> str:
        """Export lineage as a visual graph"""
        lineage_chain = self.lineage_tracker.get_lineage_chain(cv_id)
        
        if format == "mermaid":
            graph = "graph TD\n"
            
            for i, stage in enumerate(lineage_chain):
                source_node = f"S{i}[{stage['source']}]"
                target_node = f"T{i}[{stage['target']}]"
                transformation = stage['transformation']
                
                graph += f"    {source_node} -->|{transformation}| {target_node}\n"
            
            return graph
        
        return "Unsupported format"
```

---

## 📋 **COMPLIANCE & AUDIT SUPPORT**

### **GDPR Article 30 Compliance**

```python
# GDPR Article 30 record of processing activities
class GDPRLineageReporter:
    def __init__(self, lineage_tracker: DataLineageTracker):
        self.lineage_tracker = lineage_tracker
    
    def generate_article30_report(self, start_date: datetime, end_date: datetime) -> dict:
        """Generate GDPR Article 30 compliant processing record"""
        
        processing_activities = self.db_session.query(DataLineageRecord).filter(
            DataLineageRecord.processing_timestamp.between(start_date, end_date)
        ).all()
        
        report = {
            'controller_details': {
                'name': 'ImpactCV',
                'contact': '<EMAIL>',
                'dpo_contact': '<EMAIL>'
            },
            'processing_activities': [],
            'report_period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'total_processing_operations': len(processing_activities)
        }
        
        # Group activities by purpose
        activities_by_purpose = {}
        for activity in processing_activities:
            purpose = self.determine_processing_purpose(activity.transformation_type)
            if purpose not in activities_by_purpose:
                activities_by_purpose[purpose] = []
            activities_by_purpose[purpose].append(activity)
        
        # Generate activity records
        for purpose, activities in activities_by_purpose.items():
            activity_record = {
                'purpose': purpose,
                'lawful_basis': self.determine_lawful_basis(purpose),
                'categories_of_data_subjects': ['CV applicants', 'job seekers'],
                'categories_of_personal_data': self.extract_data_categories(activities),
                'recipients': self.identify_recipients(activities),
                'retention_period': self.get_retention_period(purpose),
                'security_measures': self.list_security_measures(),
                'processing_count': len(activities)
            }
            report['processing_activities'].append(activity_record)
        
        return report
    
    def determine_processing_purpose(self, transformation_type: str) -> str:
        """Determine GDPR processing purpose"""
        purpose_mapping = {
            'document_ingestion': 'CV document processing',
            'pii_detection': 'Personal data protection',
            'text_extraction': 'Content analysis',
            'ai_enhancement': 'CV improvement services',
            'cv_generation': 'CV creation and formatting'
        }
        return purpose_mapping.get(transformation_type, 'General data processing')
```

---

## ✅ **IMPLEMENTATION CHECKLIST**

### **Data Lineage Infrastructure**
- [ ] Database schema for lineage tracking
- [ ] Lineage tracking service implementation
- [ ] Integration with all data processing services
- [ ] Quality metrics calculation
- [ ] Performance monitoring

### **Visualization & Reporting**
- [ ] Lineage dashboard implementation
- [ ] Graph visualization (Mermaid/D3.js)
- [ ] Automated report generation
- [ ] Export capabilities (PDF, JSON, CSV)
- [ ] Real-time lineage monitoring

### **Compliance Support**
- [ ] GDPR Article 30 reporting
- [ ] Audit trail generation
- [ ] Impact analysis tools
- [ ] Data retention tracking
- [ ] Privacy impact assessments

### **Monitoring & Alerting**
- [ ] Data quality degradation alerts
- [ ] Lineage break detection
- [ ] Performance anomaly detection
- [ ] Compliance violation alerts
- [ ] Automated remediation workflows

---

*This comprehensive data lineage tracking system ensures complete visibility and traceability of data flow through the ImpactCV system, supporting regulatory compliance, data quality management, and operational excellence.*
