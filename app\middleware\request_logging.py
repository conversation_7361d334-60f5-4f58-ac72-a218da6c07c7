"""
Request Logging Middleware
Comprehensive request/response logging with security features
"""

import json
import time
import uuid
from typing import Any, Dict, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging import security_audit_logger
from app.core.config import settings
import logging


logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Request logging middleware with security and audit features.
    
    Features:
    - Request/response logging
    - Performance monitoring
    - Security event detection
    - PII sanitization
    - Request correlation IDs
    """
    
    def __init__(
        self,
        app,
        log_request_body: bool = False,
        log_response_body: bool = False,
        exclude_paths: Optional[list] = None,
        sensitive_headers: Optional[list] = None
    ):
        """
        Initialize request logging middleware.
        
        Args:
            app: FastAPI application
            log_request_body: Whether to log request bodies
            log_response_body: Whether to log response bodies
            exclude_paths: Paths to exclude from logging
            sensitive_headers: Headers to exclude from logs
        """
        super().__init__(app)
        self.log_request_body = log_request_body and settings.is_development()
        self.log_response_body = log_response_body and settings.is_development()
        self.exclude_paths = exclude_paths or ["/health", "/metrics"]
        self.sensitive_headers = sensitive_headers or [
            "authorization", "cookie", "x-api-key", "x-auth-token"
        ]
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """Log request and response details."""
        
        # Generate correlation ID
        correlation_id = str(uuid.uuid4())
        request.state.correlation_id = correlation_id
        
        # Skip logging for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            response = await call_next(request)
            response.headers["X-Correlation-ID"] = correlation_id
            return response
        
        # Start timing
        start_time = time.time()
        
        # Log request
        await self._log_request(request, correlation_id)
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response
            await self._log_response(request, response, process_time, correlation_id)
            
            # Add correlation ID to response
            response.headers["X-Correlation-ID"] = correlation_id
            
            # Log security events if needed
            await self._check_security_events(request, response, process_time)
            
            return response
            
        except Exception as e:
            # Log error
            process_time = time.time() - start_time
            await self._log_error(request, e, process_time, correlation_id)
            raise
    
    async def _log_request(self, request: Request, correlation_id: str) -> None:
        """
        Log incoming request details.
        
        Args:
            request: FastAPI request object
            correlation_id: Request correlation ID
        """
        # Get client information
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # Prepare request data
        request_data = {
            "correlation_id": correlation_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "client_ip": client_ip,
            "user_agent": user_agent,
            "headers": self._sanitize_headers(dict(request.headers)),
        }
        
        # Add request body if enabled
        if self.log_request_body and request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    # Try to parse as JSON
                    try:
                        request_data["body"] = json.loads(body.decode())
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        request_data["body"] = "[BINARY_DATA]"
            except Exception:
                request_data["body"] = "[ERROR_READING_BODY]"
        
        logger.info(
            f"Incoming request: {request.method} {request.url.path}",
            extra={
                "event_type": "request",
                **request_data
            }
        )
    
    async def _log_response(
        self,
        request: Request,
        response: Response,
        process_time: float,
        correlation_id: str
    ) -> None:
        """
        Log outgoing response details.
        
        Args:
            request: FastAPI request object
            response: FastAPI response object
            process_time: Request processing time
            correlation_id: Request correlation ID
        """
        # Prepare response data
        response_data = {
            "correlation_id": correlation_id,
            "status_code": response.status_code,
            "process_time": round(process_time, 4),
            "response_headers": self._sanitize_headers(dict(response.headers)),
        }
        
        # Add response body if enabled and not too large
        if self.log_response_body and hasattr(response, "body"):
            try:
                if len(response.body) < 10000:  # Limit to 10KB
                    try:
                        response_data["body"] = json.loads(response.body.decode())
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        response_data["body"] = "[BINARY_DATA]"
                else:
                    response_data["body"] = "[LARGE_RESPONSE]"
            except Exception:
                response_data["body"] = "[ERROR_READING_BODY]"
        
        # Determine log level based on status code
        if response.status_code >= 500:
            log_level = "error"
        elif response.status_code >= 400:
            log_level = "warning"
        else:
            log_level = "info"
        
        log_method = getattr(logger, log_level)
        log_method(
            f"Response: {response.status_code} for {request.method} {request.url.path} ({process_time:.4f}s)",
            extra={
                "event_type": "response",
                **response_data
            }
        )
    
    async def _log_error(
        self,
        request: Request,
        error: Exception,
        process_time: float,
        correlation_id: str
    ) -> None:
        """
        Log request processing error.
        
        Args:
            request: FastAPI request object
            error: Exception that occurred
            process_time: Request processing time
            correlation_id: Request correlation ID
        """
        error_data = {
            "correlation_id": correlation_id,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "process_time": round(process_time, 4),
        }
        
        logger.error(
            f"Request error: {type(error).__name__} for {request.method} {request.url.path}",
            extra={
                "event_type": "request_error",
                **error_data
            },
            exc_info=True
        )
    
    async def _check_security_events(
        self,
        request: Request,
        response: Response,
        process_time: float
    ) -> None:
        """
        Check for security events and log them.
        
        Args:
            request: FastAPI request object
            response: FastAPI response object
            process_time: Request processing time
        """
        client_ip = self._get_client_ip(request)
        
        # Log authentication failures
        if response.status_code == 401:
            security_audit_logger.log_authentication_attempt(
                success=False,
                ip_address=client_ip,
                user_agent=request.headers.get("user-agent"),
                failure_reason="Invalid credentials"
            )
        
        # Log authorization failures
        elif response.status_code == 403:
            security_audit_logger.log_authorization_failure(
                resource=request.url.path,
                action=request.method,
                ip_address=client_ip
            )
        
        # Log suspicious activity
        elif response.status_code == 400 and hasattr(response, 'body') and "injection" in str(response.body).lower():
            security_audit_logger.log_security_event(
                "suspicious_request",
                severity="WARNING",
                description="Potential injection attempt",
                ip_address=client_ip,
                path=request.url.path,
                method=request.method
            )
        
        # Log slow requests (potential DoS)
        elif process_time > 10.0:  # 10 seconds
            security_audit_logger.log_security_event(
                "slow_request",
                severity="WARNING",
                description="Unusually slow request detected",
                ip_address=client_ip,
                path=request.url.path,
                process_time=process_time
            )
        
        # Log large requests (potential DoS)
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB
            security_audit_logger.log_security_event(
                "large_request",
                severity="WARNING",
                description="Large request detected",
                ip_address=client_ip,
                path=request.url.path,
                content_length=content_length
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Get client IP address from request.
        
        Args:
            request: FastAPI request object
            
        Returns:
            str: Client IP address
        """
        # Check for forwarded headers (behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        return request.client.host if request.client else "unknown"
    
    def _sanitize_headers(self, headers: Dict[str, str]) -> Dict[str, str]:
        """
        Sanitize headers by removing sensitive information.
        
        Args:
            headers: Original headers dictionary
            
        Returns:
            Dict[str, str]: Sanitized headers
        """
        sanitized = {}
        
        for key, value in headers.items():
            key_lower = key.lower()
            
            if key_lower in self.sensitive_headers:
                sanitized[key] = "[REDACTED]"
            elif "token" in key_lower or "key" in key_lower or "secret" in key_lower:
                sanitized[key] = "[REDACTED]"
            else:
                sanitized[key] = value
        
        return sanitized
