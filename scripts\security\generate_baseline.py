#!/usr/bin/env python3
"""
ImpactCV Security Baseline Generator
Generate security baselines for Bandit and Semgrep to track known issues
"""

import argparse
import json
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List


class SecurityBaselineGenerator:
    """Generate and manage security baselines."""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.bandit_baseline = self.project_root / ".bandit_baseline.json"
        self.semgrep_baseline = self.project_root / ".semgrep_baseline.json"
    
    def generate_bandit_baseline(self) -> bool:
        """Generate Bandit security baseline."""
        print("🔍 Generating Bandit security baseline...")
        
        cmd = [
            "bandit",
            "-r", str(self.project_root / "app"),
            "-f", "json",
            "-o", str(self.bandit_baseline),
            "--exclude", "tests,test,migrations,alembic/versions"
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if self.bandit_baseline.exists():
                # Add metadata to baseline
                with open(self.bandit_baseline, 'r') as f:
                    baseline_data = json.load(f)
                
                baseline_data["baseline_metadata"] = {
                    "generated_at": datetime.now().isoformat(),
                    "tool": "bandit",
                    "version": self._get_tool_version("bandit"),
                    "command": " ".join(cmd),
                    "purpose": "Security baseline for known acceptable risks"
                }
                
                with open(self.bandit_baseline, 'w') as f:
                    json.dump(baseline_data, f, indent=2)
                
                print(f"✅ Bandit baseline generated: {self.bandit_baseline}")
                return True
            else:
                print("❌ Failed to generate Bandit baseline")
                return False
                
        except FileNotFoundError:
            print("❌ Bandit not found. Install with: pip install bandit")
            return False
        except Exception as e:
            print(f"❌ Bandit baseline generation failed: {e}")
            return False
    
    def generate_semgrep_baseline(self) -> bool:
        """Generate Semgrep security baseline."""
        print("🔍 Generating Semgrep security baseline...")
        
        cmd = [
            "semgrep",
            "--config=auto",
            "--json",
            "--output", str(self.semgrep_baseline),
            "--exclude", "tests",
            "--exclude", "test",
            "--exclude", "migrations",
            str(self.project_root)
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if self.semgrep_baseline.exists():
                # Add metadata to baseline
                with open(self.semgrep_baseline, 'r') as f:
                    baseline_data = json.load(f)
                
                baseline_data["baseline_metadata"] = {
                    "generated_at": datetime.now().isoformat(),
                    "tool": "semgrep",
                    "version": self._get_tool_version("semgrep"),
                    "command": " ".join(cmd),
                    "purpose": "Security baseline for known acceptable risks"
                }
                
                with open(self.semgrep_baseline, 'w') as f:
                    json.dump(baseline_data, f, indent=2)
                
                print(f"✅ Semgrep baseline generated: {self.semgrep_baseline}")
                return True
            else:
                print("❌ Failed to generate Semgrep baseline")
                return False
                
        except FileNotFoundError:
            print("❌ Semgrep not found. Install with: pip install semgrep")
            return False
        except Exception as e:
            print(f"❌ Semgrep baseline generation failed: {e}")
            return False
    
    def _get_tool_version(self, tool: str) -> str:
        """Get version of security tool."""
        try:
            result = subprocess.run(
                [tool, "--version"],
                capture_output=True,
                text=True
            )
            return result.stdout.strip()
        except:
            return "unknown"
    
    def validate_baseline(self, tool: str) -> bool:
        """Validate existing baseline file."""
        if tool == "bandit":
            baseline_file = self.bandit_baseline
        elif tool == "semgrep":
            baseline_file = self.semgrep_baseline
        else:
            print(f"❌ Unknown tool: {tool}")
            return False
        
        if not baseline_file.exists():
            print(f"❌ Baseline file not found: {baseline_file}")
            return False
        
        try:
            with open(baseline_file, 'r') as f:
                baseline_data = json.load(f)
            
            # Check if it's a valid baseline
            if tool == "bandit":
                required_keys = ["results", "metrics"]
            else:  # semgrep
                required_keys = ["results"]
            
            for key in required_keys:
                if key not in baseline_data:
                    print(f"❌ Invalid baseline: missing '{key}' key")
                    return False
            
            print(f"✅ Baseline validation passed: {baseline_file}")
            return True
            
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON in baseline file: {baseline_file}")
            return False
        except Exception as e:
            print(f"❌ Baseline validation failed: {e}")
            return False
    
    def update_baseline(self, tool: str) -> bool:
        """Update existing baseline with new scan results."""
        print(f"🔄 Updating {tool} baseline...")
        
        if tool == "bandit":
            return self.generate_bandit_baseline()
        elif tool == "semgrep":
            return self.generate_semgrep_baseline()
        else:
            print(f"❌ Unknown tool: {tool}")
            return False
    
    def compare_with_baseline(self, tool: str, current_results_file: str) -> Dict:
        """Compare current scan results with baseline."""
        if tool == "bandit":
            baseline_file = self.bandit_baseline
        elif tool == "semgrep":
            baseline_file = self.semgrep_baseline
        else:
            raise ValueError(f"Unknown tool: {tool}")
        
        if not baseline_file.exists():
            return {
                "status": "no_baseline",
                "message": f"No baseline found for {tool}",
                "new_issues": [],
                "resolved_issues": []
            }
        
        try:
            # Load baseline
            with open(baseline_file, 'r') as f:
                baseline_data = json.load(f)
            
            # Load current results
            current_file = Path(current_results_file)
            if not current_file.exists():
                return {
                    "status": "no_current_results",
                    "message": f"Current results file not found: {current_results_file}",
                    "new_issues": [],
                    "resolved_issues": []
                }
            
            with open(current_file, 'r') as f:
                current_data = json.load(f)
            
            # Extract issue identifiers
            baseline_issues = self._extract_issue_ids(baseline_data, tool)
            current_issues = self._extract_issue_ids(current_data, tool)
            
            # Find differences
            new_issues = current_issues - baseline_issues
            resolved_issues = baseline_issues - current_issues
            
            return {
                "status": "compared",
                "message": f"Comparison completed for {tool}",
                "new_issues": list(new_issues),
                "resolved_issues": list(resolved_issues),
                "total_baseline_issues": len(baseline_issues),
                "total_current_issues": len(current_issues)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Comparison failed: {e}",
                "new_issues": [],
                "resolved_issues": []
            }
    
    def _extract_issue_ids(self, data: Dict, tool: str) -> set:
        """Extract unique issue identifiers from scan results."""
        issue_ids = set()
        
        if tool == "bandit":
            for result in data.get("results", []):
                # Create unique ID from filename, line number, and test ID
                issue_id = f"{result.get('filename', '')}:{result.get('line_number', 0)}:{result.get('test_id', '')}"
                issue_ids.add(issue_id)
        
        elif tool == "semgrep":
            for result in data.get("results", []):
                # Create unique ID from path, line, and rule ID
                path = result.get("path", "")
                start_line = result.get("start", {}).get("line", 0)
                rule_id = result.get("check_id", "")
                issue_id = f"{path}:{start_line}:{rule_id}"
                issue_ids.add(issue_id)
        
        return issue_ids
    
    def generate_all_baselines(self) -> bool:
        """Generate baselines for all security tools."""
        print("🚀 Generating security baselines for all tools...")
        
        bandit_success = self.generate_bandit_baseline()
        semgrep_success = self.generate_semgrep_baseline()
        
        if bandit_success and semgrep_success:
            print("✅ All security baselines generated successfully")
            return True
        else:
            print("❌ Some baseline generations failed")
            return False
    
    def print_baseline_summary(self):
        """Print summary of existing baselines."""
        print("\n" + "="*50)
        print("🔒 SECURITY BASELINE SUMMARY")
        print("="*50)
        
        # Bandit baseline
        if self.bandit_baseline.exists():
            try:
                with open(self.bandit_baseline, 'r') as f:
                    bandit_data = json.load(f)
                
                issues_count = len(bandit_data.get("results", []))
                generated_at = bandit_data.get("baseline_metadata", {}).get("generated_at", "unknown")
                
                print(f"📊 Bandit Baseline: {issues_count} issues (generated: {generated_at})")
            except:
                print("📊 Bandit Baseline: Invalid or corrupted")
        else:
            print("📊 Bandit Baseline: Not found")
        
        # Semgrep baseline
        if self.semgrep_baseline.exists():
            try:
                with open(self.semgrep_baseline, 'r') as f:
                    semgrep_data = json.load(f)
                
                issues_count = len(semgrep_data.get("results", []))
                generated_at = semgrep_data.get("baseline_metadata", {}).get("generated_at", "unknown")
                
                print(f"📊 Semgrep Baseline: {issues_count} issues (generated: {generated_at})")
            except:
                print("📊 Semgrep Baseline: Invalid or corrupted")
        else:
            print("📊 Semgrep Baseline: Not found")
        
        print("="*50)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="ImpactCV Security Baseline Generator")
    parser.add_argument(
        "--project-root",
        default=".",
        help="Project root directory (default: current directory)"
    )
    parser.add_argument(
        "--tool",
        choices=["bandit", "semgrep", "all"],
        default="all",
        help="Security tool to generate baseline for (default: all)"
    )
    parser.add_argument(
        "--action",
        choices=["generate", "validate", "update", "summary"],
        default="generate",
        help="Action to perform (default: generate)"
    )
    parser.add_argument(
        "--compare",
        help="Compare current results file with baseline"
    )
    
    args = parser.parse_args()
    
    generator = SecurityBaselineGenerator(args.project_root)
    
    if args.action == "summary":
        generator.print_baseline_summary()
        return
    
    if args.compare:
        if args.tool == "all":
            print("❌ Cannot compare with --tool=all. Specify bandit or semgrep.")
            sys.exit(1)
        
        comparison = generator.compare_with_baseline(args.tool, args.compare)
        print(f"📊 Comparison result: {comparison['message']}")
        print(f"📊 New issues: {len(comparison['new_issues'])}")
        print(f"📊 Resolved issues: {len(comparison['resolved_issues'])}")
        return
    
    success = True
    
    if args.tool == "all":
        if args.action == "generate":
            success = generator.generate_all_baselines()
        elif args.action == "validate":
            success = (generator.validate_baseline("bandit") and 
                      generator.validate_baseline("semgrep"))
        elif args.action == "update":
            success = generator.generate_all_baselines()
    else:
        if args.action == "generate":
            if args.tool == "bandit":
                success = generator.generate_bandit_baseline()
            else:
                success = generator.generate_semgrep_baseline()
        elif args.action == "validate":
            success = generator.validate_baseline(args.tool)
        elif args.action == "update":
            success = generator.update_baseline(args.tool)
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
