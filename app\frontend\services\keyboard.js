// Keyboard navigation service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class KeyboardService {
    constructor() {
        this.focusableElements = new Set();
        this.focusableSelectors = [
            'a[href]',
            'button:not([disabled])',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
            '[tabindex]:not([tabindex="-1"])',
        ];
        this.subscribers = new Set();
        this.initialize();
    }

    /**
     * Initialize the keyboard service
     */
    initialize() {
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        this.updateFocusableElements();
    }

    /**
     * Subscribe to keyboard events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of keyboard events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in keyboard subscriber:', error);
            }
        });
    }

    /**
     * Handle key down events
     * @param {KeyboardEvent} event - The keyboard event
     */
    handleKeyDown(event) {
        return measurePerformance('keyboard_handle', () => {
            const key = event.key.toLowerCase();
            const isShift = event.shiftKey;
            const isCtrl = event.ctrlKey;
            const isAlt = event.altKey;

            // Handle tab navigation
            if (key === 'tab') {
                this.handleTabNavigation(event, isShift);
                return;
            }

            // Handle arrow key navigation
            if (['arrowup', 'arrowdown', 'arrowleft', 'arrowright'].includes(key)) {
                this.handleArrowNavigation(event, key);
                return;
            }

            // Handle enter key
            if (key === 'enter') {
                this.handleEnterKey(event);
                return;
            }

            // Handle escape key
            if (key === 'escape') {
                this.handleEscapeKey(event);
                return;
            }

            // Notify subscribers of other key events
            this.notifySubscribers('keydown', {
                key,
                isShift,
                isCtrl,
                isAlt,
                event,
            });
        });
    }

    /**
     * Handle tab navigation
     * @param {KeyboardEvent} event - The keyboard event
     * @param {boolean} isShift - Whether shift is pressed
     */
    handleTabNavigation(event, isShift) {
        const elements = Array.from(this.focusableElements);
        const currentIndex = elements.indexOf(document.activeElement);

        if (currentIndex === -1) {
            // If no element is focused, focus the first/last element
            const targetIndex = isShift ? elements.length - 1 : 0;
            elements[targetIndex]?.focus();
        } else {
            // Calculate the next index
            let nextIndex = currentIndex + (isShift ? -1 : 1);
            if (nextIndex < 0) {
                nextIndex = elements.length - 1;
            } else if (nextIndex >= elements.length) {
                nextIndex = 0;
            }

            // Focus the next element
            elements[nextIndex]?.focus();
        }

        event.preventDefault();
    }

    /**
     * Handle arrow key navigation
     * @param {KeyboardEvent} event - The keyboard event
     * @param {string} key - The arrow key
     */
    handleArrowNavigation(event, key) {
        const elements = Array.from(this.focusableElements);
        const currentIndex = elements.indexOf(document.activeElement);

        if (currentIndex === -1) {
            return;
        }

        let nextIndex = currentIndex;
        switch (key) {
            case 'arrowup':
                nextIndex = Math.max(0, currentIndex - 1);
                break;
            case 'arrowdown':
                nextIndex = Math.min(elements.length - 1, currentIndex + 1);
                break;
            case 'arrowleft':
                nextIndex = Math.max(0, currentIndex - 1);
                break;
            case 'arrowright':
                nextIndex = Math.min(elements.length - 1, currentIndex + 1);
                break;
        }

        if (nextIndex !== currentIndex) {
            elements[nextIndex]?.focus();
            event.preventDefault();
        }
    }

    /**
     * Handle enter key
     * @param {KeyboardEvent} event - The keyboard event
     */
    handleEnterKey(event) {
        const activeElement = document.activeElement;
        if (activeElement && this.focusableElements.has(activeElement)) {
            if (activeElement.tagName === 'BUTTON') {
                activeElement.click();
            } else if (activeElement.tagName === 'A') {
                activeElement.click();
            }
            event.preventDefault();
        }
    }

    /**
     * Handle escape key
     * @param {KeyboardEvent} event - The keyboard event
     */
    handleEscapeKey(event) {
        this.notifySubscribers('escape', { event });
    }

    /**
     * Update the list of focusable elements
     */
    updateFocusableElements() {
        this.focusableElements.clear();
        this.focusableSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                this.focusableElements.add(element);
            });
        });
    }

    /**
     * Add a focusable element
     * @param {HTMLElement} element - The element to add
     */
    addFocusableElement(element) {
        this.focusableElements.add(element);
    }

    /**
     * Remove a focusable element
     * @param {HTMLElement} element - The element to remove
     */
    removeFocusableElement(element) {
        this.focusableElements.delete(element);
    }

    /**
     * Get all focusable elements
     * @returns {Set<HTMLElement>} The focusable elements
     */
    getFocusableElements() {
        return this.focusableElements;
    }

    /**
     * Set focus to the first focusable element
     */
    focusFirst() {
        const elements = Array.from(this.focusableElements);
        elements[0]?.focus();
    }

    /**
     * Set focus to the last focusable element
     */
    focusLast() {
        const elements = Array.from(this.focusableElements);
        elements[elements.length - 1]?.focus();
    }
}

// Create and export a singleton instance
const keyboardService = new KeyboardService();
export default keyboardService; 