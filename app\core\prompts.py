"""
Prompt Management System
Versioned prompt templates with A/B testing and optimization
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from app.core.config import settings

logger = logging.getLogger(__name__)


class PromptTemplate(BaseModel):
    """Prompt template model."""
    
    id: str = Field(..., description="Unique prompt ID")
    name: str = Field(..., description="Human-readable name")
    description: str = Field(..., description="Prompt description")
    template: str = Field(..., description="Prompt template with placeholders")
    version: str = Field(..., description="Template version")
    category: str = Field(..., description="Prompt category")
    variables: List[str] = Field(default_factory=list, description="Required variables")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    is_active: bool = Field(default=True, description="Whether template is active")


class PromptVersion(BaseModel):
    """Prompt version for A/B testing."""
    
    template_id: str = Field(..., description="Parent template ID")
    version: str = Field(..., description="Version identifier")
    template: str = Field(..., description="Prompt template")
    weight: float = Field(default=1.0, description="Selection weight for A/B testing")
    performance_metrics: Dict[str, float] = Field(default_factory=dict, description="Performance metrics")
    is_active: bool = Field(default=True, description="Whether version is active")


class PromptError(Exception):
    """Custom exception for prompt management errors."""
    
    def __init__(self, message: str, error_code: str = "PROMPT_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class PromptManager:
    """
    Enterprise prompt management system.
    
    Features:
    - Versioned prompt templates
    - A/B testing capabilities
    - Performance tracking
    - Template validation
    - Hot reloading
    """
    
    def __init__(self, prompts_dir: Optional[str] = None):
        """Initialize prompt manager."""
        self.prompts_dir = prompts_dir or os.path.join(settings.BASE_DIR, "config", "prompts")
        self.templates = {}  # id -> PromptTemplate
        self.versions = {}   # template_id -> List[PromptVersion]
        self.initialized = False
        
        # Create prompts directory if it doesn't exist
        Path(self.prompts_dir).mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize prompt manager and load templates."""
        if self.initialized:
            return
        
        try:
            await self._load_templates()
            await self._create_default_templates()
            
            self.initialized = True
            logger.info(f"Prompt manager initialized with {len(self.templates)} templates")
            
        except Exception as e:
            logger.error(f"Failed to initialize prompt manager: {e}")
            raise PromptError(
                f"Prompt manager initialization failed: {str(e)}",
                error_code="INITIALIZATION_FAILED"
            )
    
    async def _load_templates(self) -> None:
        """Load prompt templates from files."""
        templates_file = os.path.join(self.prompts_dir, "templates.json")
        versions_file = os.path.join(self.prompts_dir, "versions.json")
        
        # Load templates
        if os.path.exists(templates_file):
            try:
                with open(templates_file, 'r', encoding='utf-8') as f:
                    templates_data = json.load(f)
                
                for template_data in templates_data:
                    template = PromptTemplate(**template_data)
                    self.templates[template.id] = template
                
                logger.info(f"Loaded {len(self.templates)} prompt templates")
                
            except Exception as e:
                logger.error(f"Failed to load templates: {e}")
        
        # Load versions
        if os.path.exists(versions_file):
            try:
                with open(versions_file, 'r', encoding='utf-8') as f:
                    versions_data = json.load(f)
                
                for version_data in versions_data:
                    version = PromptVersion(**version_data)
                    if version.template_id not in self.versions:
                        self.versions[version.template_id] = []
                    self.versions[version.template_id].append(version)
                
                logger.info(f"Loaded prompt versions for {len(self.versions)} templates")
                
            except Exception as e:
                logger.error(f"Failed to load versions: {e}")
    
    async def _create_default_templates(self) -> None:
        """Create default prompt templates if they don't exist."""
        
        default_templates = [
            {
                "id": "cv_summary_generation",
                "name": "CV Professional Summary Generation",
                "description": "Generate professional summary section for CV",
                "template": """Generate a compelling professional summary for a CV based on the following information:

Name: {name}
Target Role: {target_role}
Years of Experience: {years_experience}
Key Skills: {key_skills}
Industry: {industry}
Notable Achievements: {achievements}

Context from similar CVs:
{context}

Create a 3-4 sentence professional summary that:
- Highlights the candidate's experience and expertise
- Mentions key skills relevant to the target role
- Includes quantifiable achievements where possible
- Uses strong action words and industry terminology
- Is ATS-friendly and keyword-optimized

Professional Summary:""",
                "version": "1.0",
                "category": "cv_generation",
                "variables": ["name", "target_role", "years_experience", "key_skills", "industry", "achievements", "context"]
            },
            {
                "id": "cv_experience_generation",
                "name": "CV Work Experience Generation",
                "description": "Generate work experience section for CV",
                "template": """Generate professional work experience entries for a CV based on the following information:

Job Title: {job_title}
Company: {company}
Duration: {duration}
Key Responsibilities: {responsibilities}
Achievements: {achievements}
Skills Used: {skills_used}

Context from similar roles:
{context}

Create work experience entries that:
- Start with strong action verbs
- Include quantifiable achievements and metrics
- Highlight relevant skills and technologies
- Show progression and impact
- Are formatted consistently
- Use industry-appropriate terminology

Work Experience Entry:""",
                "version": "1.0",
                "category": "cv_generation",
                "variables": ["job_title", "company", "duration", "responsibilities", "achievements", "skills_used", "context"]
            },
            {
                "id": "cv_skills_generation",
                "name": "CV Skills Section Generation",
                "description": "Generate skills section for CV",
                "template": """Generate a comprehensive skills section for a CV based on the following information:

Target Role: {target_role}
Industry: {industry}
Technical Skills: {technical_skills}
Soft Skills: {soft_skills}
Certifications: {certifications}
Experience Level: {experience_level}

Context from similar profiles:
{context}

Create a skills section that:
- Categorizes skills appropriately (Technical, Leadership, etc.)
- Prioritizes skills most relevant to the target role
- Includes proficiency levels where appropriate
- Is ATS-friendly with proper keywords
- Balances technical and soft skills
- Mentions relevant certifications

Skills Section:""",
                "version": "1.0",
                "category": "cv_generation",
                "variables": ["target_role", "industry", "technical_skills", "soft_skills", "certifications", "experience_level", "context"]
            },
            {
                "id": "cv_improvement_suggestions",
                "name": "CV Improvement Suggestions",
                "description": "Provide suggestions to improve existing CV content",
                "template": """Analyze the following CV content and provide specific improvement suggestions:

Current CV Content:
{current_content}

Target Role: {target_role}
Industry: {industry}

Best practices from similar CVs:
{context}

Provide detailed improvement suggestions for:
1. Content clarity and impact
2. ATS optimization and keywords
3. Formatting and structure
4. Achievement quantification
5. Industry-specific improvements

Focus on actionable recommendations that will make the CV more competitive.

Improvement Suggestions:""",
                "version": "1.0",
                "category": "cv_improvement",
                "variables": ["current_content", "target_role", "industry", "context"]
            }
        ]
        
        # Add default templates if they don't exist
        for template_data in default_templates:
            if template_data["id"] not in self.templates:
                template = PromptTemplate(**template_data)
                self.templates[template.id] = template
                logger.info(f"Created default template: {template.name}")
        
        # Save templates
        await self._save_templates()
    
    async def get_prompt(
        self,
        template_id: str,
        variables: Dict[str, Any],
        version: Optional[str] = None
    ) -> str:
        """
        Get formatted prompt from template.
        
        Args:
            template_id: Template identifier
            variables: Variables to substitute in template
            version: Specific version to use (for A/B testing)
            
        Returns:
            str: Formatted prompt
        """
        if not self.initialized:
            await self.initialize()
        
        if template_id not in self.templates:
            raise PromptError(
                f"Template not found: {template_id}",
                error_code="TEMPLATE_NOT_FOUND"
            )
        
        template = self.templates[template_id]
        
        # Get specific version if requested
        if version:
            template_text = await self._get_version_template(template_id, version)
        else:
            template_text = template.template
        
        # Validate required variables
        missing_vars = [var for var in template.variables if var not in variables]
        if missing_vars:
            raise PromptError(
                f"Missing required variables: {missing_vars}",
                error_code="MISSING_VARIABLES",
                details={"missing_variables": missing_vars}
            )
        
        try:
            # Format template with variables
            formatted_prompt = template_text.format(**variables)
            return formatted_prompt
            
        except KeyError as e:
            raise PromptError(
                f"Template formatting error: {str(e)}",
                error_code="FORMATTING_ERROR"
            )
    
    async def _get_version_template(self, template_id: str, version: str) -> str:
        """Get template text for specific version."""
        if template_id not in self.versions:
            # Fall back to main template
            return self.templates[template_id].template
        
        for version_obj in self.versions[template_id]:
            if version_obj.version == version and version_obj.is_active:
                return version_obj.template
        
        # Fall back to main template if version not found
        return self.templates[template_id].template
    
    async def add_template(self, template: PromptTemplate) -> None:
        """Add new prompt template."""
        if not self.initialized:
            await self.initialize()
        
        self.templates[template.id] = template
        await self._save_templates()
        
        logger.info(f"Added new template: {template.name}")
    
    async def update_template(self, template_id: str, updates: Dict[str, Any]) -> None:
        """Update existing template."""
        if template_id not in self.templates:
            raise PromptError(
                f"Template not found: {template_id}",
                error_code="TEMPLATE_NOT_FOUND"
            )
        
        template = self.templates[template_id]
        
        # Update fields
        for key, value in updates.items():
            if hasattr(template, key):
                setattr(template, key, value)
        
        await self._save_templates()
        logger.info(f"Updated template: {template_id}")
    
    async def add_version(self, version: PromptVersion) -> None:
        """Add new template version for A/B testing."""
        if version.template_id not in self.templates:
            raise PromptError(
                f"Template not found: {version.template_id}",
                error_code="TEMPLATE_NOT_FOUND"
            )
        
        if version.template_id not in self.versions:
            self.versions[version.template_id] = []
        
        self.versions[version.template_id].append(version)
        await self._save_versions()
        
        logger.info(f"Added version {version.version} for template {version.template_id}")
    
    async def get_template_list(self, category: Optional[str] = None) -> List[PromptTemplate]:
        """Get list of available templates."""
        if not self.initialized:
            await self.initialize()
        
        templates = list(self.templates.values())
        
        if category:
            templates = [t for t in templates if t.category == category]
        
        return templates
    
    async def _save_templates(self) -> None:
        """Save templates to file."""
        templates_file = os.path.join(self.prompts_dir, "templates.json")
        
        templates_data = []
        for template in self.templates.values():
            template_dict = template.model_dump()
            # Convert datetime to string
            template_dict["created_at"] = template_dict["created_at"].isoformat()
            templates_data.append(template_dict)
        
        with open(templates_file, 'w', encoding='utf-8') as f:
            json.dump(templates_data, f, indent=2, ensure_ascii=False)
    
    async def _save_versions(self) -> None:
        """Save versions to file."""
        versions_file = os.path.join(self.prompts_dir, "versions.json")
        
        versions_data = []
        for version_list in self.versions.values():
            for version in version_list:
                versions_data.append(version.model_dump())
        
        with open(versions_file, 'w', encoding='utf-8') as f:
            json.dump(versions_data, f, indent=2, ensure_ascii=False)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get prompt manager statistics."""
        total_versions = sum(len(versions) for versions in self.versions.values())
        
        return {
            "initialized": self.initialized,
            "total_templates": len(self.templates),
            "total_versions": total_versions,
            "categories": list(set(t.category for t in self.templates.values())),
        }


# Global prompt manager instance
prompt_manager = PromptManager()
