# ImpactCV Bandit Security Configuration
# Static Application Security Testing (SAST) for Python

[bandit]
# Exclude test files and migrations from security scanning
exclude_dirs = [
    "tests",
    "test",
    "migrations",
    "alembic/versions",
    "venv",
    ".venv",
    "env",
    ".env",
    "node_modules",
    ".git",
    "__pycache__",
    ".pytest_cache",
    "htmlcov",
    "coverage-reports",
    "build",
    "dist",
    "*.egg-info"
]

# Skip specific test IDs that are false positives or acceptable risks
skips = [
    # B101: Test for use of assert - acceptable in test files
    # B601: Shell injection via Popen - we validate inputs
    # B603: Subprocess without shell equals true - we control the commands
]

# Test severity levels to include
# LOW, MEDIUM, HIGH
severity = "medium"

# Confidence levels to include  
# LOW, MEDIUM, HIGH
confidence = "medium"

# Output format options: csv, json, txt, xml, yaml
format = "json"

# Include line numbers in output
include_line_numbers = true

# Aggregate output by vulnerability type
aggregate = "vuln"

# Number of lines of code context to include
context_lines = 3

# ============================================================================
# SECURITY TEST CONFIGURATION
# ============================================================================

# Tests to include (if not specified, all tests are run)
tests = [
    # B101: assert_used
    "B102",  # exec_used
    "B103",  # set_bad_file_permissions
    "B104",  # hardcoded_bind_all_interfaces
    "B105",  # hardcoded_password_string
    "B106",  # hardcoded_password_funcarg
    "B107",  # hardcoded_password_default
    "B108",  # hardcoded_tmp_directory
    "B110",  # try_except_pass
    "B112",  # try_except_continue
    "B201",  # flask_debug_true
    "B301",  # pickle
    "B302",  # marshal
    "B303",  # md5
    "B304",  # des
    "B305",  # cipher
    "B306",  # mktemp_q
    "B307",  # eval
    "B308",  # mark_safe
    "B309",  # httpsconnection
    "B310",  # urllib_urlopen
    "B311",  # random
    "B312",  # telnetlib
    "B313",  # xml_bad_cElementTree
    "B314",  # xml_bad_ElementTree
    "B315",  # xml_bad_expatreader
    "B316",  # xml_bad_expatbuilder
    "B317",  # xml_bad_sax
    "B318",  # xml_bad_minidom
    "B319",  # xml_bad_pulldom
    "B320",  # xml_bad_etree
    "B321",  # ftplib
    "B322",  # input
    "B323",  # unverified_context
    "B324",  # hashlib_new_insecure_functions
    "B325",  # tempnam
    "B401",  # import_telnetlib
    "B402",  # import_ftplib
    "B403",  # import_pickle
    "B404",  # import_subprocess
    "B405",  # import_xml_etree
    "B406",  # import_xml_sax
    "B407",  # import_xml_expat
    "B408",  # import_xml_minidom
    "B409",  # import_xml_pulldom
    "B410",  # import_lxml
    "B411",  # import_xmlrpclib
    "B412",  # import_httpoxy
    "B413",  # import_pycrypto
    "B501",  # request_with_no_cert_validation
    "B502",  # ssl_with_bad_version
    "B503",  # ssl_with_bad_defaults
    "B504",  # ssl_with_no_version
    "B505",  # weak_cryptographic_key
    "B506",  # yaml_load
    "B507",  # ssh_no_host_key_verification
    "B601",  # paramiko_calls
    "B602",  # subprocess_popen_with_shell_equals_true
    "B603",  # subprocess_without_shell_equals_true
    "B604",  # any_other_function_with_shell_equals_true
    "B605",  # start_process_with_a_shell
    "B606",  # start_process_with_no_shell
    "B607",  # start_process_with_partial_path
    "B608",  # hardcoded_sql_expressions
    "B609",  # linux_commands_wildcard_injection
    "B610",  # django_extra_used
    "B611",  # django_rawsql_used
    "B701",  # jinja2_autoescape_false
    "B702",  # use_of_mako_templates
    "B703"   # django_mark_safe
]

# ============================================================================
# CUSTOM SECURITY RULES
# ============================================================================

# Custom patterns to look for
[bandit.patterns]
# Look for potential API key exposure
api_key_pattern = "(?i)(api[_-]?key|apikey|access[_-]?token|secret[_-]?key)"

# Look for potential database connection strings
db_connection_pattern = "(?i)(database[_-]?url|db[_-]?url|connection[_-]?string)"

# Look for potential password patterns
password_pattern = "(?i)(password|passwd|pwd|secret)"

# ============================================================================
# BASELINE CONFIGURATION
# ============================================================================

# Path to baseline file (for tracking known issues)
baseline = ".bandit_baseline.json"

# ============================================================================
# REPORTING CONFIGURATION
# ============================================================================

# Report format configuration
[bandit.formatters]
json = {
    "include_paths": true,
    "include_metrics": true,
    "include_skipped": false
}

txt = {
    "include_paths": true,
    "include_metrics": true,
    "include_skipped": true,
    "show_lineno": true,
    "show_code": true
}

# ============================================================================
# PLUGIN CONFIGURATION
# ============================================================================

# Custom plugins directory
plugins_dir = "security/bandit_plugins"

# ============================================================================
# ENVIRONMENT-SPECIFIC SETTINGS
# ============================================================================

[bandit.profiles.development]
# More lenient settings for development
severity = "medium"
confidence = "medium"
skips = ["B101", "B601", "B603"]

[bandit.profiles.staging]
# Stricter settings for staging
severity = "low"
confidence = "medium"
skips = ["B101"]

[bandit.profiles.production]
# Strictest settings for production
severity = "low"
confidence = "low"
skips = []

# ============================================================================
# INTEGRATION SETTINGS
# ============================================================================

# Exit codes
[bandit.exit_codes]
# Exit with code 1 if any issues found
exit_zero = false

# Return exit code based on severity
exit_on_severity = "medium"

# Return exit code based on confidence
exit_on_confidence = "medium"
