# 🤝 Contributing to ImpactCV

Thank you for your interest in contributing to ImpactCV! This document provides guidelines and information for contributors.

## 📋 Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Process](#development-process)
- [Code Standards](#code-standards)
- [Security Guidelines](#security-guidelines)
- [Testing Requirements](#testing-requirements)
- [Documentation](#documentation)
- [Pull Request Process](#pull-request-process)

## 📜 Code of Conduct

This project adheres to a code of conduct that we expect all contributors to follow:

- **Be respectful** and inclusive in all interactions
- **Be collaborative** and help others learn and grow
- **Be constructive** in feedback and discussions
- **Focus on the code** and technical aspects, not personal attributes

## 🚀 Getting Started

### Prerequisites

- Python 3.11+
- Docker & Docker Compose
- Git
- OpenAI API Key (for testing AI features)

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/yourusername/ImpactCV.git
   cd ImpactCV
   ```

2. **Set up Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate     # Windows
   
   pip install -r requirements-dev.txt
   ```

3. **Configure Environment**
   ```bash
   cp .env.template .env
   # Edit .env with your configuration
   ```

4. **Install Pre-commit Hooks**
   ```bash
   pre-commit install
   ```

## 🔄 Development Process

### Branching Strategy

We follow GitFlow branching model:

- **main** - Production-ready code
- **develop** - Integration branch for features
- **feature/*** - New features and enhancements
- **bugfix/*** - Bug fixes
- **hotfix/*** - Critical production fixes

### Feature Development

1. **Create Feature Branch**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/ISSUE-123-description
   ```

2. **Make Changes**
   - Write code following our standards
   - Add/update tests
   - Update documentation

3. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat(component): add new feature

   - Implement feature X
   - Add comprehensive tests
   - Update documentation
   
   Closes #123"
   ```

4. **Push and Create PR**
   ```bash
   git push origin feature/ISSUE-123-description
   ```

## 📏 Code Standards

### Python Code Style

- **Formatter:** Black (line length: 88)
- **Import Sorting:** isort
- **Linting:** flake8 with custom configuration
- **Type Checking:** mypy
- **Docstrings:** Google style

### Commit Message Format

Follow Conventional Commits specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat` - New feature
- `fix` - Bug fix
- `docs` - Documentation changes
- `style` - Code style changes
- `refactor` - Code refactoring
- `test` - Test additions/changes
- `chore` - Maintenance tasks
- `security` - Security improvements

**Examples:**
```bash
feat(auth): implement OAuth 2.0 PKCE flow
fix(api): resolve validation error in CV generation
docs(readme): update installation instructions
security(auth): add rate limiting to login endpoint
```

### Code Quality Requirements

- **Test Coverage:** Minimum 90%
- **Security Scans:** All security checks must pass
- **Type Hints:** All functions must have type annotations
- **Documentation:** All public APIs must be documented

## 🔒 Security Guidelines

### Security Requirements

1. **No Hardcoded Secrets**
   - Use environment variables
   - Never commit API keys or passwords
   - Use secure defaults for development

2. **Input Validation**
   - Validate all user inputs
   - Sanitize data before processing
   - Use parameterized queries

3. **Authentication & Authorization**
   - Implement proper access controls
   - Use secure session management
   - Follow OAuth 2.0 best practices

### Security Testing

Run security scans before submitting:

```bash
# Comprehensive security scan
python scripts/security/run_security_scan.py

# Check for secrets
python scripts/security/check_api_keys.py

# Validate configuration
python scripts/security/validate_config.py
```

## 🧪 Testing Requirements

### Test Categories

1. **Unit Tests** - Test individual components
2. **Integration Tests** - Test service interactions
3. **Security Tests** - Test security controls
4. **Performance Tests** - Test scalability

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html --cov-fail-under=90

# Run specific categories
pytest tests/unit/
pytest tests/integration/
pytest tests/security/
```

### Test Standards

- **Coverage:** Minimum 90% line coverage
- **Isolation:** Tests should not depend on each other
- **Mocking:** Mock external dependencies
- **Documentation:** Clear test descriptions

## 📚 Documentation

### Documentation Requirements

1. **Code Documentation**
   - Docstrings for all public functions/classes
   - Type hints for all parameters and returns
   - Clear variable and function names

2. **API Documentation**
   - OpenAPI/Swagger documentation
   - Request/response examples
   - Error code documentation

3. **Architecture Documentation**
   - System design documents
   - Security architecture
   - Deployment guides

### Documentation Format

Use Google-style docstrings:

```python
def generate_cv(user_data: Dict[str, Any], template_id: str) -> CVResponse:
    """Generate a CV using AI-powered content generation.
    
    Args:
        user_data: User information and experience data
        template_id: ID of the CV template to use
        
    Returns:
        CVResponse containing the generated CV and metadata
        
    Raises:
        ValidationError: If user_data is invalid
        APIError: If OpenAI API call fails
    """
```

## 🔄 Pull Request Process

### Before Submitting

1. **Run Quality Checks**
   ```bash
   # Format code
   black app/ tests/
   isort app/ tests/
   
   # Run linting
   flake8 app/ tests/
   
   # Type checking
   mypy app/
   
   # Run tests
   pytest --cov=app --cov-fail-under=90
   
   # Security scans
   python scripts/security/run_security_scan.py
   ```

2. **Update Documentation**
   - Update README if needed
   - Add/update API documentation
   - Update CHANGELOG.md

### PR Requirements

- [ ] **Tests:** All tests pass with >90% coverage
- [ ] **Security:** All security scans pass
- [ ] **Documentation:** Code is properly documented
- [ ] **Changelog:** CHANGELOG.md updated if needed
- [ ] **Conventional Commits:** Commit messages follow format
- [ ] **No Conflicts:** Branch is up to date with develop

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update
- [ ] Security improvement

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Security scans pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

### Review Process

1. **Automated Checks** - CI/CD pipeline runs automatically
2. **Code Review** - At least one maintainer review required
3. **Security Review** - Security team review for security changes
4. **Testing** - Manual testing if needed
5. **Merge** - Squash and merge to develop

## 🏷️ Release Process

### Version Management

We use Semantic Versioning (SemVer):
- **MAJOR** - Breaking changes
- **MINOR** - New features (backward compatible)
- **PATCH** - Bug fixes

### Release Steps

1. Create release branch from develop
2. Update version numbers
3. Update CHANGELOG.md
4. Create release PR to main
5. Tag release after merge
6. Deploy to production

## 🆘 Getting Help

- **Issues:** [GitHub Issues](https://github.com/dasotillop/ImpactCV/issues)
- **Discussions:** [GitHub Discussions](https://github.com/dasotillop/ImpactCV/discussions)
- **Email:** <EMAIL>

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

Thank you for contributing to ImpactCV! 🚀
