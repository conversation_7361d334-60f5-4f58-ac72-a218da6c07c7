# ImpactCV AI-Powered CV Generation System
# Development Dependencies

# Include production dependencies
-r requirements.txt

# ============================================================================
# TESTING FRAMEWORK
# ============================================================================
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-html==4.1.1
pytest-xdist==3.5.0
pytest-mock==3.12.0
pytest-benchmark==4.0.0
pytest-timeout==2.2.0

# ============================================================================
# TEST UTILITIES
# ============================================================================
factory-boy==3.3.0
faker==20.1.0
freezegun==1.2.2
responses==0.24.1
httpx-mock==0.10.1

# ============================================================================
# CODE QUALITY & LINTING
# ============================================================================
flake8==6.1.0
flake8-docstrings==1.7.0
flake8-import-order==0.18.2
flake8-bugbear==23.11.28
flake8-comprehensions==3.14.0
flake8-simplify==0.21.0

# ============================================================================
# CODE FORMATTING
# ============================================================================
black==23.11.0
isort==5.12.0
autoflake==2.2.1

# ============================================================================
# TYPE CHECKING
# ============================================================================
mypy==1.7.1
types-requests==*********
types-redis==********
types-python-dateutil==*********
types-PyYAML==*********

# ============================================================================
# SECURITY SCANNING
# ============================================================================
bandit==1.7.5
safety==2.3.5
pip-audit==2.6.1
semgrep==1.45.0

# ============================================================================
# DOCUMENTATION
# ============================================================================
mkdocs==1.5.3
mkdocs-material==9.4.8
mkdocs-mermaid2-plugin==1.1.1
mkdocstrings[python]==0.24.0

# ============================================================================
# API DOCUMENTATION
# ============================================================================
redoc-cli==0.13.2

# ============================================================================
# PERFORMANCE TESTING
# ============================================================================
locust==2.17.0
memory-profiler==0.61.0
py-spy==0.3.14

# ============================================================================
# DATABASE TESTING
# ============================================================================
pytest-postgresql==5.0.0
pytest-redis==3.0.2

# ============================================================================
# DEBUGGING
# ============================================================================
ipdb==0.13.13
pdbpp==0.10.3
icecream==2.1.3

# ============================================================================
# DEVELOPMENT UTILITIES
# ============================================================================
pre-commit==3.6.0
commitizen==3.12.0
bumpversion==0.6.0

# ============================================================================
# JUPYTER (for data analysis)
# ============================================================================
jupyter==1.0.0
jupyterlab==4.0.8
notebook==7.0.6

# ============================================================================
# DATA VISUALIZATION
# ============================================================================
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# ============================================================================
# LOAD TESTING
# ============================================================================
artillery==1.7.9

# ============================================================================
# MOCK SERVICES
# ============================================================================
responses==0.24.1
httpretty==1.1.4
wiremock==2.35.0

# ============================================================================
# CONTAINER TESTING
# ============================================================================
testcontainers==3.7.1

# ============================================================================
# API TESTING
# ============================================================================
tavern==2.4.1

# ============================================================================
# ACCESSIBILITY TESTING
# ============================================================================
axe-selenium-python==2.1.6

# ============================================================================
# COMPLIANCE TESTING
# ============================================================================
gdpr-checker==1.0.0

# ============================================================================
# ENVIRONMENT MANAGEMENT
# ============================================================================
python-dotenv==1.0.0
environs==10.0.0

# ============================================================================
# FILE WATCHING
# ============================================================================
watchdog==3.0.0

# ============================================================================
# TASK AUTOMATION
# ============================================================================
invoke==2.2.0
fabric==3.2.2

# ============================================================================
# DEPENDENCY ANALYSIS
# ============================================================================
pipdeptree==2.13.1
pip-tools==7.3.0

# ============================================================================
# COVERAGE REPORTING
# ============================================================================
coverage[toml]==7.3.2
coverage-badge==1.1.0

# ============================================================================
# STATIC ANALYSIS
# ============================================================================
vulture==2.10
radon==6.0.1
xenon==0.9.1

# ============================================================================
# IMPORT SORTING
# ============================================================================
isort==5.12.0

# ============================================================================
# DOCSTRING CHECKING
# ============================================================================
pydocstyle==6.3.0
darglint==1.8.1

# ============================================================================
# COMPLEXITY ANALYSIS
# ============================================================================
mccabe==0.7.0

# ============================================================================
# DEAD CODE DETECTION
# ============================================================================
vulture==2.10

# ============================================================================
# DEPENDENCY VULNERABILITY SCANNING
# ============================================================================
safety==2.3.5
pip-audit==2.6.1

# ============================================================================
# LICENSE CHECKING
# ============================================================================
pip-licenses==4.3.2

# ============================================================================
# CHANGELOG GENERATION
# ============================================================================
gitchangelog==3.0.4

# ============================================================================
# RELEASE AUTOMATION
# ============================================================================
twine==4.0.2
wheel==0.42.0
setuptools==69.0.2
