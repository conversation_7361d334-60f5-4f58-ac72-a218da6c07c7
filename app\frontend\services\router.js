// Routing service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class RouterService {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.params = new Map();
        this.query = new URLSearchParams();
        this.history = [];
        this.maxHistory = 50;
        this.subscribers = new Set();
    }

    /**
     * Subscribe to route changes
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of route changes
     * @param {string} route - The new route
     * @param {Object} params - The route parameters
     * @param {Object} query - The query parameters
     */
    notifySubscribers(route, params, query) {
        this.subscribers.forEach(callback => {
            try {
                callback(route, params, query);
            } catch (error) {
                console.error('Error in route subscriber:', error);
            }
        });
    }

    /**
     * Register a route
     * @param {string} path - The route path
     * @param {Object} options - Route options
     */
    registerRoute(path, options = {}) {
        this.routes.set(path, {
            path,
            component: options.component,
            title: options.title,
            auth: options.auth || false,
            ...options,
        });
    }

    /**
     * Navigate to a route
     * @param {string} path - The route path
     * @param {Object} [params] - Route parameters
     * @param {Object} [query] - Query parameters
     */
    async navigate(path, params = {}, query = {}) {
        return measurePerformance('navigation', async () => {
            const route = this.routes.get(path);
            if (!route) {
                throw new Error(`Route not found: ${path}`);
            }

            // Check authentication if required
            if (route.auth) {
                // Implement authentication check
            }

            // Update URL
            const url = new URL(path, window.location.origin);
            Object.entries(query).forEach(([key, value]) => {
                url.searchParams.set(key, value);
            });

            // Update history
            this.history.unshift({
                path,
                params,
                query,
                timestamp: Date.now(),
            });
            if (this.history.length > this.maxHistory) {
                this.history.pop();
            }

            // Update state
            this.currentRoute = route;
            this.params = new Map(Object.entries(params));
            this.query = new URLSearchParams(query);

            // Update browser history
            window.history.pushState(
                { path, params, query },
                route.title || '',
                url.toString()
            );

            // Update document title
            if (route.title) {
                document.title = route.title;
            }

            // Notify subscribers
            this.notifySubscribers(path, params, query);
        });
    }

    /**
     * Get the current route
     * @returns {Object} The current route
     */
    getCurrentRoute() {
        return this.currentRoute;
    }

    /**
     * Get route parameters
     * @returns {Map} The route parameters
     */
    getParams() {
        return this.params;
    }

    /**
     * Get query parameters
     * @returns {URLSearchParams} The query parameters
     */
    getQuery() {
        return this.query;
    }

    /**
     * Get navigation history
     * @returns {Array} The navigation history
     */
    getHistory() {
        return [...this.history];
    }

    /**
     * Go back in history
     */
    goBack() {
        if (this.history.length > 1) {
            const previous = this.history[1];
            this.navigate(previous.path, previous.params, previous.query);
        }
    }

    /**
     * Go forward in history
     */
    goForward() {
        // Implement forward navigation
    }

    /**
     * Initialize the router
     */
    initialize() {
        // Handle browser back/forward
        window.addEventListener('popstate', (event) => {
            if (event.state) {
                this.navigate(
                    event.state.path,
                    event.state.params,
                    event.state.query
                );
            }
        });

        // Handle initial route
        const path = window.location.pathname;
        const query = new URLSearchParams(window.location.search);
        const route = this.routes.get(path);

        if (route) {
            this.navigate(path, {}, Object.fromEntries(query));
        } else {
            // Handle 404
            this.navigate('/404');
        }
    }

    /**
     * Generate a URL for a route
     * @param {string} path - The route path
     * @param {Object} [params] - Route parameters
     * @param {Object} [query] - Query parameters
     * @returns {string} The generated URL
     */
    generateUrl(path, params = {}, query = {}) {
        const url = new URL(path, window.location.origin);
        
        // Replace path parameters
        Object.entries(params).forEach(([key, value]) => {
            url.pathname = url.pathname.replace(`:${key}`, value);
        });

        // Add query parameters
        Object.entries(query).forEach(([key, value]) => {
            url.searchParams.set(key, value);
        });

        return url.toString();
    }
}

// Create and export a singleton instance
const routerService = new RouterService();
export default routerService; 