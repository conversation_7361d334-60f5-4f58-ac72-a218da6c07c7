#!/usr/bin/env python3
"""
ImpactCV Security Configuration Validator
Validate security tool configurations and settings
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Tuple

import yaml


class SecurityConfigValidator:
    """Validate security configurations."""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.validation_results = []
    
    def validate_bandit_config(self) -> bool:
        """Validate Bandit configuration."""
        config_file = self.project_root / ".bandit"
        
        if not config_file.exists():
            self.validation_results.append((
                "ERROR", "Bandit", "Configuration file .bandit not found"
            ))
            return False
        
        try:
            # Bandit config is in INI format, basic validation
            with open(config_file, 'r') as f:
                content = f.read()
            
            # Check for required sections
            required_sections = ["bandit"]
            for section in required_sections:
                if f"[{section}]" not in content:
                    self.validation_results.append((
                        "WARNING", "Bandit", f"Missing section [{section}]"
                    ))
            
            # Check for important settings
            if "exclude_dirs" not in content:
                self.validation_results.append((
                    "WARNING", "Bandit", "No exclude_dirs specified"
                ))
            
            if "severity" not in content:
                self.validation_results.append((
                    "INFO", "Bandit", "No severity level specified, using default"
                ))
            
            self.validation_results.append((
                "SUCCESS", "Bandit", "Configuration file is valid"
            ))
            return True
            
        except Exception as e:
            self.validation_results.append((
                "ERROR", "Bandit", f"Failed to parse configuration: {e}"
            ))
            return False
    
    def validate_semgrep_config(self) -> bool:
        """Validate Semgrep configuration."""
        config_file = self.project_root / ".semgrep.yml"
        ignore_file = self.project_root / ".semgrepignore"
        
        success = True
        
        # Check main config file
        if not config_file.exists():
            self.validation_results.append((
                "WARNING", "Semgrep", "Configuration file .semgrep.yml not found"
            ))
            success = False
        else:
            try:
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                
                # Validate structure
                if not isinstance(config, dict):
                    self.validation_results.append((
                        "ERROR", "Semgrep", "Configuration must be a YAML object"
                    ))
                    success = False
                else:
                    # Check for rules section
                    if "rules" in config:
                        rules = config["rules"]
                        if isinstance(rules, list) and len(rules) > 0:
                            self.validation_results.append((
                                "SUCCESS", "Semgrep", f"Found {len(rules)} rule configurations"
                            ))
                        else:
                            self.validation_results.append((
                                "WARNING", "Semgrep", "No rules specified in configuration"
                            ))
                    
                    # Check for custom rules
                    if "custom_rules" in config:
                        custom_rules = config["custom_rules"]
                        if isinstance(custom_rules, list):
                            self.validation_results.append((
                                "SUCCESS", "Semgrep", f"Found {len(custom_rules)} custom rules"
                            ))
                    
                    self.validation_results.append((
                        "SUCCESS", "Semgrep", "Configuration file is valid YAML"
                    ))
                
            except yaml.YAMLError as e:
                self.validation_results.append((
                    "ERROR", "Semgrep", f"Invalid YAML in configuration: {e}"
                ))
                success = False
            except Exception as e:
                self.validation_results.append((
                    "ERROR", "Semgrep", f"Failed to parse configuration: {e}"
                ))
                success = False
        
        # Check ignore file
        if not ignore_file.exists():
            self.validation_results.append((
                "WARNING", "Semgrep", "Ignore file .semgrepignore not found"
            ))
        else:
            try:
                with open(ignore_file, 'r') as f:
                    ignore_content = f.read()
                
                # Check for common ignore patterns
                common_patterns = ["tests/", "node_modules/", "__pycache__/"]
                found_patterns = sum(1 for pattern in common_patterns if pattern in ignore_content)
                
                self.validation_results.append((
                    "SUCCESS", "Semgrep", f"Ignore file found with {found_patterns}/{len(common_patterns)} common patterns"
                ))
                
            except Exception as e:
                self.validation_results.append((
                    "WARNING", "Semgrep", f"Could not read ignore file: {e}"
                ))
        
        return success
    
    def validate_precommit_config(self) -> bool:
        """Validate pre-commit configuration."""
        config_file = self.project_root / ".pre-commit-config.yaml"
        
        if not config_file.exists():
            self.validation_results.append((
                "WARNING", "Pre-commit", "Configuration file .pre-commit-config.yaml not found"
            ))
            return False
        
        try:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            
            if not isinstance(config, dict) or "repos" not in config:
                self.validation_results.append((
                    "ERROR", "Pre-commit", "Invalid configuration structure"
                ))
                return False
            
            repos = config["repos"]
            if not isinstance(repos, list):
                self.validation_results.append((
                    "ERROR", "Pre-commit", "repos must be a list"
                ))
                return False
            
            # Check for security-related hooks
            security_hooks = []
            for repo in repos:
                if isinstance(repo, dict) and "hooks" in repo:
                    for hook in repo["hooks"]:
                        if isinstance(hook, dict) and "id" in hook:
                            hook_id = hook["id"]
                            if any(sec_term in hook_id.lower() for sec_term in 
                                  ["bandit", "safety", "secret", "security"]):
                                security_hooks.append(hook_id)
            
            if security_hooks:
                self.validation_results.append((
                    "SUCCESS", "Pre-commit", f"Found security hooks: {', '.join(security_hooks)}"
                ))
            else:
                self.validation_results.append((
                    "WARNING", "Pre-commit", "No security hooks found"
                ))
            
            self.validation_results.append((
                "SUCCESS", "Pre-commit", f"Configuration is valid with {len(repos)} repositories"
            ))
            return True
            
        except yaml.YAMLError as e:
            self.validation_results.append((
                "ERROR", "Pre-commit", f"Invalid YAML: {e}"
            ))
            return False
        except Exception as e:
            self.validation_results.append((
                "ERROR", "Pre-commit", f"Failed to parse configuration: {e}"
            ))
            return False
    
    def validate_github_workflows(self) -> bool:
        """Validate GitHub Actions workflows for security."""
        workflows_dir = self.project_root / ".github" / "workflows"
        
        if not workflows_dir.exists():
            self.validation_results.append((
                "WARNING", "GitHub Actions", "No .github/workflows directory found"
            ))
            return False
        
        workflow_files = list(workflows_dir.glob("*.yml")) + list(workflows_dir.glob("*.yaml"))
        
        if not workflow_files:
            self.validation_results.append((
                "WARNING", "GitHub Actions", "No workflow files found"
            ))
            return False
        
        security_workflows = 0
        for workflow_file in workflow_files:
            try:
                with open(workflow_file, 'r') as f:
                    workflow = yaml.safe_load(f)
                
                if isinstance(workflow, dict) and "jobs" in workflow:
                    jobs = workflow["jobs"]
                    for job_name, job_config in jobs.items():
                        if isinstance(job_config, dict) and "steps" in job_config:
                            steps = job_config["steps"]
                            for step in steps:
                                if isinstance(step, dict):
                                    # Check for security-related steps
                                    step_name = step.get("name", "").lower()
                                    step_uses = step.get("uses", "").lower()
                                    step_run = step.get("run", "").lower()
                                    
                                    if any(sec_term in f"{step_name} {step_uses} {step_run}" 
                                          for sec_term in ["security", "bandit", "semgrep", 
                                                          "safety", "trivy", "snyk"]):
                                        security_workflows += 1
                                        break
            
            except Exception as e:
                self.validation_results.append((
                    "WARNING", "GitHub Actions", f"Could not parse {workflow_file.name}: {e}"
                ))
        
        if security_workflows > 0:
            self.validation_results.append((
                "SUCCESS", "GitHub Actions", f"Found {security_workflows} workflows with security steps"
            ))
        else:
            self.validation_results.append((
                "WARNING", "GitHub Actions", "No security steps found in workflows"
            ))
        
        return True
    
    def validate_docker_security(self) -> bool:
        """Validate Docker security configuration."""
        dockerfile = self.project_root / "Dockerfile"
        
        if not dockerfile.exists():
            self.validation_results.append((
                "INFO", "Docker", "No Dockerfile found"
            ))
            return True
        
        try:
            with open(dockerfile, 'r') as f:
                content = f.read().upper()
            
            # Check for security best practices
            if "USER ROOT" in content:
                self.validation_results.append((
                    "WARNING", "Docker", "Running as root user detected"
                ))
            elif "USER " in content:
                self.validation_results.append((
                    "SUCCESS", "Docker", "Non-root user configuration found"
                ))
            else:
                self.validation_results.append((
                    "WARNING", "Docker", "No explicit user configuration found"
                ))
            
            # Check for HEALTHCHECK
            if "HEALTHCHECK" in content:
                self.validation_results.append((
                    "SUCCESS", "Docker", "Health check configuration found"
                ))
            else:
                self.validation_results.append((
                    "INFO", "Docker", "No health check configuration found"
                ))
            
            return True
            
        except Exception as e:
            self.validation_results.append((
                "WARNING", "Docker", f"Could not parse Dockerfile: {e}"
            ))
            return False
    
    def run_all_validations(self) -> bool:
        """Run all security configuration validations."""
        print("🔍 Validating security configurations...")
        
        validations = [
            ("Bandit Configuration", self.validate_bandit_config),
            ("Semgrep Configuration", self.validate_semgrep_config),
            ("Pre-commit Configuration", self.validate_precommit_config),
            ("GitHub Actions Workflows", self.validate_github_workflows),
            ("Docker Security", self.validate_docker_security)
        ]
        
        all_success = True
        for validation_name, validation_func in validations:
            try:
                success = validation_func()
                if not success:
                    all_success = False
            except Exception as e:
                self.validation_results.append((
                    "ERROR", validation_name, f"Validation failed: {e}"
                ))
                all_success = False
        
        return all_success
    
    def print_results(self):
        """Print validation results."""
        if not self.validation_results:
            print("✅ No validation results to display")
            return
        
        print("\n" + "="*70)
        print("🔒 SECURITY CONFIGURATION VALIDATION RESULTS")
        print("="*70)
        
        # Group by level
        errors = [r for r in self.validation_results if r[0] == "ERROR"]
        warnings = [r for r in self.validation_results if r[0] == "WARNING"]
        successes = [r for r in self.validation_results if r[0] == "SUCCESS"]
        infos = [r for r in self.validation_results if r[0] == "INFO"]
        
        if errors:
            print("\n❌ ERRORS:")
            for level, component, message in errors:
                print(f"  {component}: {message}")
        
        if warnings:
            print("\n⚠️  WARNINGS:")
            for level, component, message in warnings:
                print(f"  {component}: {message}")
        
        if successes:
            print("\n✅ SUCCESS:")
            for level, component, message in successes:
                print(f"  {component}: {message}")
        
        if infos:
            print("\n💡 INFO:")
            for level, component, message in infos:
                print(f"  {component}: {message}")
        
        print("\n📊 SUMMARY:")
        print(f"  Errors: {len(errors)}")
        print(f"  Warnings: {len(warnings)}")
        print(f"  Successes: {len(successes)}")
        print(f"  Info: {len(infos)}")
        print("="*70)


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate security configurations")
    parser.add_argument(
        "--project-root",
        default=".",
        help="Project root directory (default: current directory)"
    )
    parser.add_argument(
        "--fail-on-errors",
        action="store_true",
        help="Exit with non-zero code if errors are found"
    )
    
    args = parser.parse_args()
    
    validator = SecurityConfigValidator(args.project_root)
    success = validator.run_all_validations()
    validator.print_results()
    
    if args.fail_on_errors and not success:
        sys.exit(1)
    
    sys.exit(0)


if __name__ == "__main__":
    main()
