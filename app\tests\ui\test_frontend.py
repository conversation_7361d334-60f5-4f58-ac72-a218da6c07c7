import unittest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import time

class ImpactCVUITest(unittest.TestCase):
    def setUp(self):
        """Set up test environment"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in headless mode
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.implicitly_wait(10)
        self.base_url = "http://localhost:8000"
        
    def tearDown(self):
        """Clean up after test"""
        self.driver.quit()
    
    def test_page_load(self):
        """Test that the page loads correctly"""
        self.driver.get(self.base_url)
        
        # Check title
        self.assertIn("ImpactCV", self.driver.title)
        
        # Check main elements
        self.assertTrue(self.driver.find_element(By.ID, "cvForm").is_displayed())
        self.assertTrue(self.driver.find_element(By.ID, "results").is_displayed())
    
    def test_form_submission(self):
        """Test CV form submission"""
        self.driver.get(self.base_url)
        
        # Fill form
        self.driver.find_element(By.NAME, "name").send_keys("John Doe")
        self.driver.find_element(By.NAME, "position").send_keys("Software Engineer")
        self.driver.find_element(By.NAME, "experience_years").send_keys("5")
        self.driver.find_element(By.NAME, "achievement_description").send_keys(
            "Led a team of 5 developers to implement a new feature"
        )
        
        # Submit form
        submit_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_button.click()
        
        # Wait for results
        WebDriverWait(self.driver, 10).until(
            EC.visibility_of_element_located((By.ID, "taskResult"))
        )
        
        # Check results
        self.assertTrue(self.driver.find_element(By.ID, "taskResult").text)
        self.assertTrue(self.driver.find_element(By.ID, "quantificationResult").text)
        self.assertTrue(self.driver.find_element(By.ID, "resultResult").text)
    
    def test_error_handling(self):
        """Test error handling in form submission"""
        self.driver.get(self.base_url)
        
        # Submit empty form
        submit_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_button.click()
        
        # Check for error message
        error_message = WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "error-message"))
        )
        self.assertTrue(error_message.is_displayed())
    
    def test_responsive_design(self):
        """Test responsive design at different viewport sizes"""
        self.driver.get(self.base_url)
        
        # Test mobile viewport
        self.driver.set_window_size(375, 812)  # iPhone X
        time.sleep(1)  # Wait for resize
        self.assertTrue(self.driver.find_element(By.CLASS_NAME, "mobile-friendly").is_displayed())
        
        # Test tablet viewport
        self.driver.set_window_size(768, 1024)  # iPad
        time.sleep(1)
        self.assertTrue(self.driver.find_element(By.CLASS_NAME, "tablet-friendly").is_displayed())
        
        # Test desktop viewport
        self.driver.set_window_size(1920, 1080)
        time.sleep(1)
        self.assertTrue(self.driver.find_element(By.CLASS_NAME, "desktop-friendly").is_displayed())
    
    def test_performance(self):
        """Test page load performance"""
        self.driver.get(self.base_url)
        
        # Get performance metrics
        performance = self.driver.execute_script("return window.performance")
        timing = performance.get("timing")
        
        # Calculate load time
        load_time = timing.get("loadEventEnd") - timing.get("navigationStart")
        
        # Assert load time is reasonable (less than 3 seconds)
        self.assertLess(load_time, 3000)

if __name__ == "__main__":
    unittest.main() 