// Tooltip service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class TooltipService {
    constructor() {
        this.tooltips = new Map();
        this.activeTooltip = null;
        this.subscribers = new Set();
        this.tooltipElement = null;
        this.initializeTooltipElement();
    }

    /**
     * Initialize the tooltip element
     */
    initializeTooltipElement() {
        this.tooltipElement = document.createElement('div');
        this.tooltipElement.className = 'tooltip';
        this.tooltipElement.style.display = 'none';
        document.body.appendChild(this.tooltipElement);
    }

    /**
     * Subscribe to tooltip events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of tooltip events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in tooltip subscriber:', error);
            }
        });
    }

    /**
     * Register a tooltip
     * @param {string} id - The tooltip ID
     * @param {Object} options - The tooltip options
     */
    register(id, options = {}) {
        this.tooltips.set(id, {
            ...options,
            element: document.getElementById(id),
        });
    }

    /**
     * Show a tooltip
     * @param {string} id - The tooltip ID
     * @param {Object} [data] - The tooltip data
     */
    show(id, data = {}) {
        return measurePerformance('tooltip_show', () => {
            const tooltip = this.tooltips.get(id);
            if (!tooltip) {
                console.error(`Tooltip ${id} not found`);
                return;
            }

            if (this.activeTooltip) {
                this.hide(this.activeTooltip);
            }

            this.tooltipElement.textContent = data.content || tooltip.content;
            this.tooltipElement.style.display = 'block';
            this.positionTooltip(tooltip.element);
            this.activeTooltip = id;

            if (tooltip.onShow) {
                tooltip.onShow(data);
            }

            this.notifySubscribers('show', { id, data });
        });
    }

    /**
     * Hide a tooltip
     * @param {string} id - The tooltip ID
     */
    hide(id) {
        return measurePerformance('tooltip_hide', () => {
            const tooltip = this.tooltips.get(id);
            if (!tooltip) {
                console.error(`Tooltip ${id} not found`);
                return;
            }

            this.tooltipElement.style.display = 'none';
            this.activeTooltip = null;

            if (tooltip.onHide) {
                tooltip.onHide();
            }

            this.notifySubscribers('hide', { id });
        });
    }

    /**
     * Position the tooltip relative to the target element
     * @param {HTMLElement} targetElement - The target element
     */
    positionTooltip(targetElement) {
        const rect = targetElement.getBoundingClientRect();
        const tooltipRect = this.tooltipElement.getBoundingClientRect();

        let top = rect.bottom + window.scrollY + 5;
        let left = rect.left + window.scrollX + (rect.width - tooltipRect.width) / 2;

        // Adjust position if tooltip would go off screen
        if (left + tooltipRect.width > window.innerWidth) {
            left = window.innerWidth - tooltipRect.width - 10;
        }
        if (left < 0) {
            left = 10;
        }

        this.tooltipElement.style.top = `${top}px`;
        this.tooltipElement.style.left = `${left}px`;
    }

    /**
     * Get the active tooltip
     * @returns {string|null} The active tooltip ID
     */
    getActiveTooltip() {
        return this.activeTooltip;
    }

    /**
     * Check if a tooltip is active
     * @param {string} id - The tooltip ID
     * @returns {boolean} Whether the tooltip is active
     */
    isActive(id) {
        return this.activeTooltip === id;
    }

    /**
     * Get tooltip data
     * @param {string} id - The tooltip ID
     * @returns {Object} The tooltip data
     */
    getTooltipData(id) {
        return this.tooltips.get(id);
    }

    /**
     * Update tooltip data
     * @param {string} id - The tooltip ID
     * @param {Object} data - The new tooltip data
     */
    updateTooltipData(id, data) {
        const tooltip = this.tooltips.get(id);
        if (tooltip) {
            Object.assign(tooltip, data);
        }
    }

    /**
     * Initialize all tooltips
     */
    initialize() {
        this.tooltips.forEach((tooltip, id) => {
            if (tooltip.element) {
                tooltip.element.addEventListener('mouseenter', () => this.show(id));
                tooltip.element.addEventListener('mouseleave', () => this.hide(id));
            }
        });
    }
}

// Create and export a singleton instance
const tooltipService = new TooltipService();
export default tooltipService; 