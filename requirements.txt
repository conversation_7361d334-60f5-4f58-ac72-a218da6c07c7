# ImpactCV AI-Powered CV Generation System
# Core Production Dependencies

# ============================================================================
# WEB FRAMEWORK
# ============================================================================
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
starlette==0.27.0

# ============================================================================
# DATABASE & ORM
# ============================================================================
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# ============================================================================
# CACHING & SESSION
# ============================================================================
redis==5.0.1
hiredis==2.2.3

# ============================================================================
# AUTHENTICATION & SECURITY
# ============================================================================
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.7

# ============================================================================
# AI & ML DEPENDENCIES
# ============================================================================
openai>=1.6.1
langchain>=0.1.0
langchain-openai>=0.0.2
faiss-cpu==1.7.4
numpy==1.24.4
pandas==2.1.3
scikit-learn==1.3.2

# ============================================================================
# DOCUMENT PROCESSING
# ============================================================================
PyMuPDF==1.23.8
python-docx==1.1.0
python-pptx==0.6.23
openpyxl==3.1.2
Pillow==10.1.0

# ============================================================================
# DATA VALIDATION
# ============================================================================
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# ============================================================================
# HTTP CLIENT
# ============================================================================
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# ============================================================================
# CONFIGURATION & ENVIRONMENT
# ============================================================================
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# ============================================================================
# LOGGING & MONITORING
# ============================================================================
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# ============================================================================
# DATE & TIME
# ============================================================================
python-dateutil==2.8.2
pytz==2023.3

# ============================================================================
# UTILITIES
# ============================================================================
click==8.1.7
rich==13.7.0
typer==0.9.0

# ============================================================================
# ASYNC SUPPORT
# ============================================================================
asyncio==3.4.3
aiofiles==23.2.1

# ============================================================================
# JSON & SERIALIZATION
# ============================================================================
orjson==3.9.10
msgpack==1.0.7

# ============================================================================
# TEMPLATE ENGINE
# ============================================================================
jinja2==3.1.2

# ============================================================================
# CORS & MIDDLEWARE
# ============================================================================
# python-cors==1.7.0  # Not needed - FastAPI includes CORS middleware

# ============================================================================
# BACKGROUND TASKS
# ============================================================================
celery==5.3.4
kombu==5.3.4

# ============================================================================
# FILE HANDLING
# ============================================================================
python-magic==0.4.27
filetype==1.2.0

# ============================================================================
# NETWORKING
# ============================================================================
dnspython==2.4.2

# ============================================================================
# PERFORMANCE
# ============================================================================
cachetools==5.3.2
lru-dict==1.3.0
