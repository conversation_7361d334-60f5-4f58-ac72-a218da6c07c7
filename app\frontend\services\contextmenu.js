// Context menu service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ContextMenuService {
    constructor() {
        this.menus = new Map();
        this.activeMenu = null;
        this.subscribers = new Set();
        this.menuElement = null;
        this.initializeMenuElement();
    }

    /**
     * Initialize the menu element
     */
    initializeMenuElement() {
        this.menuElement = document.createElement('div');
        this.menuElement.className = 'context-menu';
        this.menuElement.style.display = 'none';
        document.body.appendChild(this.menuElement);

        // Close menu when clicking outside
        document.addEventListener('click', (event) => {
            if (!this.menuElement.contains(event.target)) {
                this.hide();
            }
        });
    }

    /**
     * Subscribe to context menu events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of context menu events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in context menu subscriber:', error);
            }
        });
    }

    /**
     * Register a context menu
     * @param {string} id - The menu ID
     * @param {Object} options - The menu options
     */
    register(id, options = {}) {
        this.menus.set(id, {
            ...options,
            items: options.items || [],
        });
    }

    /**
     * Show a context menu
     * @param {string} id - The menu ID
     * @param {Object} [data] - The menu data
     * @param {Event} [event] - The triggering event
     */
    show(id, data = {}, event = null) {
        return measurePerformance('contextmenu_show', () => {
            const menu = this.menus.get(id);
            if (!menu) {
                console.error(`Context menu ${id} not found`);
                return;
            }

            if (this.activeMenu) {
                this.hide();
            }

            this.menuElement.innerHTML = '';
            menu.items.forEach(item => {
                const menuItem = document.createElement('div');
                menuItem.className = 'context-menu-item';
                menuItem.textContent = item.label;
                menuItem.addEventListener('click', () => {
                    if (item.action) {
                        item.action(data);
                    }
                    this.hide();
                });
                this.menuElement.appendChild(menuItem);
            });

            this.menuElement.style.display = 'block';
            this.positionMenu(event);
            this.activeMenu = id;

            if (menu.onShow) {
                menu.onShow(data);
            }

            this.notifySubscribers('show', { id, data });
        });
    }

    /**
     * Hide the context menu
     */
    hide() {
        return measurePerformance('contextmenu_hide', () => {
            if (!this.activeMenu) {
                return;
            }

            const menu = this.menus.get(this.activeMenu);
            this.menuElement.style.display = 'none';
            this.activeMenu = null;

            if (menu && menu.onHide) {
                menu.onHide();
            }

            this.notifySubscribers('hide', { id: this.activeMenu });
        });
    }

    /**
     * Position the context menu relative to the event
     * @param {Event} event - The triggering event
     */
    positionMenu(event) {
        if (!event) {
            return;
        }

        const x = event.clientX;
        const y = event.clientY;
        const menuRect = this.menuElement.getBoundingClientRect();

        let left = x;
        let top = y;

        // Adjust position if menu would go off screen
        if (left + menuRect.width > window.innerWidth) {
            left = window.innerWidth - menuRect.width - 10;
        }
        if (top + menuRect.height > window.innerHeight) {
            top = window.innerHeight - menuRect.height - 10;
        }

        this.menuElement.style.left = `${left}px`;
        this.menuElement.style.top = `${top}px`;
    }

    /**
     * Get the active menu
     * @returns {string|null} The active menu ID
     */
    getActiveMenu() {
        return this.activeMenu;
    }

    /**
     * Check if a menu is active
     * @param {string} id - The menu ID
     * @returns {boolean} Whether the menu is active
     */
    isActive(id) {
        return this.activeMenu === id;
    }

    /**
     * Get menu data
     * @param {string} id - The menu ID
     * @returns {Object} The menu data
     */
    getMenuData(id) {
        return this.menus.get(id);
    }

    /**
     * Update menu data
     * @param {string} id - The menu ID
     * @param {Object} data - The new menu data
     */
    updateMenuData(id, data) {
        const menu = this.menus.get(id);
        if (menu) {
            Object.assign(menu, data);
        }
    }

    /**
     * Initialize all context menus
     */
    initialize() {
        this.menus.forEach((menu, id) => {
            if (menu.element) {
                menu.element.addEventListener('contextmenu', (event) => {
                    event.preventDefault();
                    this.show(id, {}, event);
                });
            }
        });
    }
}

// Create and export a singleton instance
const contextMenuService = new ContextMenuService();
export default contextMenuService; 