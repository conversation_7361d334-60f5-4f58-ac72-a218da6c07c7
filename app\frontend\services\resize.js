// Resize service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ResizeService {
    constructor() {
        this.resizables = new Map();
        this.subscribers = new Set();
        this.resizingElement = null;
        this.resizeHandle = null;
        this.startPosition = { x: 0, y: 0 };
        this.startSize = { width: 0, height: 0 };
        this.initialize();
    }

    /**
     * Initialize the resize service
     */
    initialize() {
        document.addEventListener('mousedown', this.handleMouseDown.bind(this));
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
        document.addEventListener('touchstart', this.handleTouchStart.bind(this));
        document.addEventListener('touchmove', this.handleTouchMove.bind(this));
        document.addEventListener('touchend', this.handleTouchEnd.bind(this));
    }

    /**
     * Subscribe to resize events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of resize events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in resize subscriber:', error);
            }
        });
    }

    /**
     * Register a resizable element
     * @param {string} id - The element ID
     * @param {Object} options - The resizable options
     */
    registerResizable(id, options = {}) {
        const element = document.getElementById(id);
        if (!element) {
            console.error(`Element ${id} not found`);
            return;
        }

        // Create resize handles
        const handles = this.createResizeHandles(element, options);
        this.resizables.set(id, {
            ...options,
            element,
            handles,
        });
    }

    /**
     * Create resize handles for an element
     * @param {HTMLElement} element - The element
     * @param {Object} options - The resizable options
     * @returns {Object} The resize handles
     */
    createResizeHandles(element, options) {
        const handles = {};
        const positions = options.handles || ['e', 's', 'se'];

        positions.forEach(position => {
            const handle = document.createElement('div');
            handle.className = `resize-handle resize-handle-${position}`;
            handle.dataset.position = position;
            element.appendChild(handle);
            handles[position] = handle;
        });

        return handles;
    }

    /**
     * Handle mouse down events
     * @param {MouseEvent} event - The mouse event
     */
    handleMouseDown(event) {
        return measurePerformance('resize_mousedown', () => {
            const handle = event.target.closest('.resize-handle');
            if (!handle) {
                return;
            }

            const element = handle.parentElement;
            const id = element.id;
            const resizable = this.resizables.get(id);
            if (!resizable) {
                return;
            }

            this.resizingElement = resizable;
            this.resizeHandle = handle;
            this.startPosition = { x: event.clientX, y: event.clientY };
            this.startSize = {
                width: element.offsetWidth,
                height: element.offsetHeight,
            };

            if (resizable.onResizeStart) {
                resizable.onResizeStart(event, {
                    element,
                    handle,
                    position: handle.dataset.position,
                });
            }

            this.notifySubscribers('resizestart', { id, event });
        });
    }

    /**
     * Handle mouse move events
     * @param {MouseEvent} event - The mouse event
     */
    handleMouseMove(event) {
        return measurePerformance('resize_mousemove', () => {
            if (!this.resizingElement) {
                return;
            }

            const deltaX = event.clientX - this.startPosition.x;
            const deltaY = event.clientY - this.startPosition.y;
            const position = this.resizeHandle.dataset.position;
            const element = this.resizingElement.element;

            let width = this.startSize.width;
            let height = this.startSize.height;

            // Update dimensions based on handle position
            if (position.includes('e')) {
                width = Math.max(this.resizingElement.minWidth || 0, width + deltaX);
            }
            if (position.includes('s')) {
                height = Math.max(this.resizingElement.minHeight || 0, height + deltaY);
            }

            // Apply new dimensions
            element.style.width = `${width}px`;
            element.style.height = `${height}px`;

            if (this.resizingElement.onResize) {
                this.resizingElement.onResize(event, {
                    element,
                    handle: this.resizeHandle,
                    position,
                    width,
                    height,
                });
            }

            this.notifySubscribers('resize', {
                id: element.id,
                event,
                width,
                height,
            });
        });
    }

    /**
     * Handle mouse up events
     * @param {MouseEvent} event - The mouse event
     */
    handleMouseUp(event) {
        return measurePerformance('resize_mouseup', () => {
            if (!this.resizingElement) {
                return;
            }

            const element = this.resizingElement.element;
            const handle = this.resizeHandle;

            if (this.resizingElement.onResizeEnd) {
                this.resizingElement.onResizeEnd(event, {
                    element,
                    handle,
                    position: handle.dataset.position,
                });
            }

            this.resizingElement = null;
            this.resizeHandle = null;
            this.startPosition = { x: 0, y: 0 };
            this.startSize = { width: 0, height: 0 };

            this.notifySubscribers('resizeend', { id: element.id, event });
        });
    }

    /**
     * Handle touch start events
     * @param {TouchEvent} event - The touch event
     */
    handleTouchStart(event) {
        return measurePerformance('resize_touchstart', () => {
            const handle = event.target.closest('.resize-handle');
            if (!handle) {
                return;
            }

            const element = handle.parentElement;
            const id = element.id;
            const resizable = this.resizables.get(id);
            if (!resizable) {
                return;
            }

            const touch = event.touches[0];
            this.resizingElement = resizable;
            this.resizeHandle = handle;
            this.startPosition = { x: touch.clientX, y: touch.clientY };
            this.startSize = {
                width: element.offsetWidth,
                height: element.offsetHeight,
            };

            if (resizable.onResizeStart) {
                resizable.onResizeStart(event, {
                    element,
                    handle,
                    position: handle.dataset.position,
                });
            }

            this.notifySubscribers('resizestart', { id, event });
        });
    }

    /**
     * Handle touch move events
     * @param {TouchEvent} event - The touch event
     */
    handleTouchMove(event) {
        return measurePerformance('resize_touchmove', () => {
            if (!this.resizingElement) {
                return;
            }

            const touch = event.touches[0];
            const deltaX = touch.clientX - this.startPosition.x;
            const deltaY = touch.clientY - this.startPosition.y;
            const position = this.resizeHandle.dataset.position;
            const element = this.resizingElement.element;

            let width = this.startSize.width;
            let height = this.startSize.height;

            // Update dimensions based on handle position
            if (position.includes('e')) {
                width = Math.max(this.resizingElement.minWidth || 0, width + deltaX);
            }
            if (position.includes('s')) {
                height = Math.max(this.resizingElement.minHeight || 0, height + deltaY);
            }

            // Apply new dimensions
            element.style.width = `${width}px`;
            element.style.height = `${height}px`;

            if (this.resizingElement.onResize) {
                this.resizingElement.onResize(event, {
                    element,
                    handle: this.resizeHandle,
                    position,
                    width,
                    height,
                });
            }

            this.notifySubscribers('resize', {
                id: element.id,
                event,
                width,
                height,
            });
        });
    }

    /**
     * Handle touch end events
     * @param {TouchEvent} event - The touch event
     */
    handleTouchEnd(event) {
        return measurePerformance('resize_touchend', () => {
            if (!this.resizingElement) {
                return;
            }

            const element = this.resizingElement.element;
            const handle = this.resizeHandle;

            if (this.resizingElement.onResizeEnd) {
                this.resizingElement.onResizeEnd(event, {
                    element,
                    handle,
                    position: handle.dataset.position,
                });
            }

            this.resizingElement = null;
            this.resizeHandle = null;
            this.startPosition = { x: 0, y: 0 };
            this.startSize = { width: 0, height: 0 };

            this.notifySubscribers('resizeend', { id: element.id, event });
        });
    }

    /**
     * Get resizable data
     * @param {string} id - The resizable ID
     * @returns {Object} The resizable data
     */
    getResizableData(id) {
        return this.resizables.get(id);
    }

    /**
     * Update resizable data
     * @param {string} id - The resizable ID
     * @param {Object} data - The new resizable data
     */
    updateResizableData(id, data) {
        const resizable = this.resizables.get(id);
        if (resizable) {
            Object.assign(resizable, data);
        }
    }
}

// Create and export a singleton instance
const resizeService = new ResizeService();
export default resizeService; 