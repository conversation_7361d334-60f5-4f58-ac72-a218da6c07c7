[tool:pytest]
# Pytest configuration for ImpactCV testing

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --durations=10
    --maxfail=5

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    security: Security tests
    performance: Performance tests
    compliance: Compliance tests
    gdpr: GDPR compliance tests
    owasp: OWASP security tests
    slow: Slow running tests
    fast: Fast running tests
    critical: Critical functionality tests
    smoke: Smoke tests for basic functionality
    regression: Regression tests
    api: API endpoint tests
    database: Database related tests
    ai: AI/ML related tests
    rag: RAG pipeline tests
    pii: PII detection tests
    validation: Data validation tests
    quality: Data quality tests
    lineage: Data lineage tests
    catalog: Data catalog tests

# Test timeout (in seconds)
timeout = 300

# Warnings
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Coverage configuration
[coverage:run]
source = app
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */migrations/*
    */alembic/*
    */conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov

[coverage:xml]
output = coverage.xml
