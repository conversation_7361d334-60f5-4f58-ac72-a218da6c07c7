"""
Data Catalog Service
Searchable metadata catalog with DAMA-DMBOK governance
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class DataAsset(BaseModel):
    """Data asset model for catalog."""
    
    id: str = Field(..., description="Unique asset ID")
    name: str = Field(..., description="Asset name")
    type: str = Field(..., description="Asset type")
    description: str = Field(..., description="Asset description")
    owner: str = Field(..., description="Data owner")
    steward: Optional[str] = Field(None, description="Data steward")
    domain: str = Field(..., description="Business domain")
    classification: str = Field(..., description="Data classification")
    tags: List[str] = Field(default_factory=list, description="Asset tags")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    schema: Optional[Dict[str, Any]] = Field(None, description="Data schema")
    quality_score: Optional[float] = Field(None, description="Quality score")
    usage_stats: Dict[str, Any] = Field(default_factory=dict, description="Usage statistics")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")


class CatalogSearchResult(BaseModel):
    """Catalog search result model."""
    
    assets: List[DataAsset] = Field(..., description="Found assets")
    total_count: int = Field(..., description="Total matching assets")
    facets: Dict[str, Dict[str, int]] = Field(..., description="Search facets")
    search_time_ms: float = Field(..., description="Search time in milliseconds")


class DataCatalogError(Exception):
    """Custom exception for data catalog errors."""
    
    def __init__(self, message: str, error_code: str = "CATALOG_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DataCatalog:
    """
    Enterprise data catalog service.
    
    Features:
    - Metadata management
    - Asset discovery
    - Data governance
    - Usage tracking
    - Quality monitoring
    """
    
    def __init__(self):
        """Initialize data catalog."""
        self.assets = {}  # asset_id -> DataAsset
        self.indexes = {
            'name': {},      # name -> asset_ids
            'type': {},      # type -> asset_ids
            'domain': {},    # domain -> asset_ids
            'tags': {},      # tag -> asset_ids
            'owner': {},     # owner -> asset_ids
        }
    
    def register_asset(self, asset: DataAsset) -> DataAsset:
        """
        Register a new data asset in the catalog.
        
        Args:
            asset: Data asset to register
            
        Returns:
            DataAsset: Registered asset
        """
        try:
            # Store asset
            self.assets[asset.id] = asset
            
            # Update indexes
            self._update_indexes(asset)
            
            logger.info(f"Registered data asset: {asset.name} ({asset.id})")
            return asset
            
        except Exception as e:
            logger.error(f"Failed to register asset: {e}")
            raise DataCatalogError(
                f"Failed to register asset: {str(e)}",
                error_code="REGISTRATION_FAILED"
            )
    
    def update_asset(self, asset_id: str, updates: Dict[str, Any]) -> DataAsset:
        """
        Update an existing data asset.
        
        Args:
            asset_id: Asset ID to update
            updates: Fields to update
            
        Returns:
            DataAsset: Updated asset
        """
        if asset_id not in self.assets:
            raise DataCatalogError(f"Asset not found: {asset_id}")
        
        asset = self.assets[asset_id]
        
        # Remove from old indexes
        self._remove_from_indexes(asset)
        
        # Update asset
        for key, value in updates.items():
            if hasattr(asset, key):
                setattr(asset, key, value)
        
        asset.updated_at = datetime.utcnow()
        
        # Update indexes
        self._update_indexes(asset)
        
        logger.info(f"Updated data asset: {asset.name} ({asset_id})")
        return asset
    
    def get_asset(self, asset_id: str) -> Optional[DataAsset]:
        """Get asset by ID."""
        return self.assets.get(asset_id)
    
    def search_assets(
        self,
        query: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 50,
        offset: int = 0
    ) -> CatalogSearchResult:
        """
        Search data assets in the catalog.
        
        Args:
            query: Search query
            filters: Search filters
            limit: Maximum results to return
            offset: Results offset
            
        Returns:
            CatalogSearchResult: Search results
        """
        import time
        start_time = time.time()
        
        try:
            # Get candidate assets
            candidate_assets = self._get_candidate_assets(query, filters)
            
            # Apply text search if query provided
            if query:
                candidate_assets = self._apply_text_search(candidate_assets, query)
            
            # Sort results by relevance
            sorted_assets = self._sort_by_relevance(candidate_assets, query)
            
            # Apply pagination
            total_count = len(sorted_assets)
            paginated_assets = sorted_assets[offset:offset + limit]
            
            # Generate facets
            facets = self._generate_facets(candidate_assets)
            
            search_time_ms = (time.time() - start_time) * 1000
            
            return CatalogSearchResult(
                assets=paginated_assets,
                total_count=total_count,
                facets=facets,
                search_time_ms=search_time_ms
            )
            
        except Exception as e:
            logger.error(f"Asset search failed: {e}")
            raise DataCatalogError(
                f"Asset search failed: {str(e)}",
                error_code="SEARCH_FAILED"
            )
    
    def _get_candidate_assets(
        self,
        query: Optional[str],
        filters: Optional[Dict[str, Any]]
    ) -> List[DataAsset]:
        """Get candidate assets based on filters."""
        if not filters:
            return list(self.assets.values())
        
        candidate_ids = None
        
        for filter_key, filter_value in filters.items():
            if filter_key in self.indexes:
                index = self.indexes[filter_key]
                
                if isinstance(filter_value, list):
                    # Multiple values (OR)
                    filter_ids = set()
                    for value in filter_value:
                        filter_ids.update(index.get(value, set()))
                else:
                    # Single value
                    filter_ids = index.get(filter_value, set())
                
                if candidate_ids is None:
                    candidate_ids = filter_ids
                else:
                    candidate_ids = candidate_ids.intersection(filter_ids)
        
        if candidate_ids is None:
            return list(self.assets.values())
        
        return [self.assets[asset_id] for asset_id in candidate_ids if asset_id in self.assets]
    
    def _apply_text_search(self, assets: List[DataAsset], query: str) -> List[DataAsset]:
        """Apply text search to assets."""
        query_lower = query.lower()
        matching_assets = []
        
        for asset in assets:
            # Search in name, description, and tags
            searchable_text = f"{asset.name} {asset.description} {' '.join(asset.tags)}".lower()
            
            if query_lower in searchable_text:
                matching_assets.append(asset)
        
        return matching_assets
    
    def _sort_by_relevance(self, assets: List[DataAsset], query: Optional[str]) -> List[DataAsset]:
        """Sort assets by relevance."""
        if not query:
            # Sort by quality score and update time
            return sorted(
                assets,
                key=lambda a: (a.quality_score or 0, a.updated_at),
                reverse=True
            )
        
        query_lower = query.lower()
        
        def calculate_relevance(asset: DataAsset) -> float:
            score = 0.0
            
            # Name match (highest weight)
            if query_lower in asset.name.lower():
                score += 10.0
                if asset.name.lower() == query_lower:
                    score += 20.0
            
            # Description match
            if query_lower in asset.description.lower():
                score += 5.0
            
            # Tag match
            for tag in asset.tags:
                if query_lower in tag.lower():
                    score += 3.0
            
            # Quality bonus
            if asset.quality_score:
                score += asset.quality_score * 2.0
            
            return score
        
        return sorted(assets, key=calculate_relevance, reverse=True)
    
    def _generate_facets(self, assets: List[DataAsset]) -> Dict[str, Dict[str, int]]:
        """Generate search facets."""
        facets = {
            'type': {},
            'domain': {},
            'classification': {},
            'owner': {},
            'tags': {}
        }
        
        for asset in assets:
            # Type facet
            asset_type = asset.type
            facets['type'][asset_type] = facets['type'].get(asset_type, 0) + 1
            
            # Domain facet
            domain = asset.domain
            facets['domain'][domain] = facets['domain'].get(domain, 0) + 1
            
            # Classification facet
            classification = asset.classification
            facets['classification'][classification] = facets['classification'].get(classification, 0) + 1
            
            # Owner facet
            owner = asset.owner
            facets['owner'][owner] = facets['owner'].get(owner, 0) + 1
            
            # Tags facet
            for tag in asset.tags:
                facets['tags'][tag] = facets['tags'].get(tag, 0) + 1
        
        return facets
    
    def _update_indexes(self, asset: DataAsset) -> None:
        """Update search indexes for an asset."""
        # Name index
        name_key = asset.name.lower()
        if name_key not in self.indexes['name']:
            self.indexes['name'][name_key] = set()
        self.indexes['name'][name_key].add(asset.id)
        
        # Type index
        if asset.type not in self.indexes['type']:
            self.indexes['type'][asset.type] = set()
        self.indexes['type'][asset.type].add(asset.id)
        
        # Domain index
        if asset.domain not in self.indexes['domain']:
            self.indexes['domain'][asset.domain] = set()
        self.indexes['domain'][asset.domain].add(asset.id)
        
        # Owner index
        if asset.owner not in self.indexes['owner']:
            self.indexes['owner'][asset.owner] = set()
        self.indexes['owner'][asset.owner].add(asset.id)
        
        # Tags index
        for tag in asset.tags:
            if tag not in self.indexes['tags']:
                self.indexes['tags'][tag] = set()
            self.indexes['tags'][tag].add(asset.id)
    
    def _remove_from_indexes(self, asset: DataAsset) -> None:
        """Remove asset from search indexes."""
        # Name index
        name_key = asset.name.lower()
        if name_key in self.indexes['name']:
            self.indexes['name'][name_key].discard(asset.id)
            if not self.indexes['name'][name_key]:
                del self.indexes['name'][name_key]
        
        # Type index
        if asset.type in self.indexes['type']:
            self.indexes['type'][asset.type].discard(asset.id)
            if not self.indexes['type'][asset.type]:
                del self.indexes['type'][asset.type]
        
        # Domain index
        if asset.domain in self.indexes['domain']:
            self.indexes['domain'][asset.domain].discard(asset.id)
            if not self.indexes['domain'][asset.domain]:
                del self.indexes['domain'][asset.domain]
        
        # Owner index
        if asset.owner in self.indexes['owner']:
            self.indexes['owner'][asset.owner].discard(asset.id)
            if not self.indexes['owner'][asset.owner]:
                del self.indexes['owner'][asset.owner]
        
        # Tags index
        for tag in asset.tags:
            if tag in self.indexes['tags']:
                self.indexes['tags'][tag].discard(asset.id)
                if not self.indexes['tags'][tag]:
                    del self.indexes['tags'][tag]
    
    def get_catalog_stats(self) -> Dict[str, Any]:
        """Get catalog statistics."""
        total_assets = len(self.assets)
        
        # Count by type
        type_counts = {}
        domain_counts = {}
        classification_counts = {}
        
        for asset in self.assets.values():
            type_counts[asset.type] = type_counts.get(asset.type, 0) + 1
            domain_counts[asset.domain] = domain_counts.get(asset.domain, 0) + 1
            classification_counts[asset.classification] = classification_counts.get(asset.classification, 0) + 1
        
        # Quality statistics
        quality_scores = [asset.quality_score for asset in self.assets.values() if asset.quality_score]
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        
        return {
            'total_assets': total_assets,
            'type_distribution': type_counts,
            'domain_distribution': domain_counts,
            'classification_distribution': classification_counts,
            'average_quality_score': avg_quality,
            'index_sizes': {
                index_name: len(index_data)
                for index_name, index_data in self.indexes.items()
            }
        }
    
    def register_cv_assets(self, cv_id: str, user_id: str, cv_data: Dict[str, Any]) -> List[DataAsset]:
        """Register CV-related data assets."""
        assets = []
        
        # Register CV document asset
        cv_asset = DataAsset(
            id=f"cv_{cv_id}",
            name=f"CV Document {cv_id}",
            type="cv_document",
            description="Generated CV document",
            owner=user_id,
            domain="hr_documents",
            classification="confidential",
            tags=["cv", "generated", "document"],
            metadata={
                "cv_id": cv_id,
                "user_id": user_id,
                "generated_at": datetime.utcnow().isoformat(),
                "sections": list(cv_data.keys()) if cv_data else []
            }
        )
        
        assets.append(self.register_asset(cv_asset))
        
        # Register individual sections as assets
        if cv_data:
            for section_name, section_data in cv_data.items():
                if section_data:  # Only register non-empty sections
                    section_asset = DataAsset(
                        id=f"cv_{cv_id}_section_{section_name}",
                        name=f"CV {section_name.title()} Section",
                        type="cv_section",
                        description=f"CV {section_name} section data",
                        owner=user_id,
                        domain="hr_documents",
                        classification="confidential",
                        tags=["cv", "section", section_name],
                        metadata={
                            "cv_id": cv_id,
                            "section_name": section_name,
                            "parent_asset": cv_asset.id,
                            "data_type": type(section_data).__name__
                        }
                    )
                    
                    assets.append(self.register_asset(section_asset))
        
        logger.info(f"Registered {len(assets)} CV assets for CV {cv_id}")
        return assets


# Global data catalog instance
data_catalog = DataCatalog()
