// Utility functions for the frontend

/**
 * Format a date according to the configured format
 * @param {Date} date - The date to format
 * @returns {Promise<string>} Formatted date string
 */
export const formatDate = async (date) => {
    const config = await import('./config.js').then(m => m.default);
    const { dateFormat } = config.ui;
    
    // Implement date formatting logic based on dateFormat
    return date.toISOString().split('T')[0]; // Basic implementation
};

/**
 * Format a time according to the configured format
 * @param {Date} date - The date containing the time to format
 * @returns {Promise<string>} Formatted time string
 */
export const formatTime = async (date) => {
    const config = await import('./config.js').then(m => m.default);
    const { timeFormat } = config.ui;
    
    // Implement time formatting logic based on timeFormat
    return date.toTimeString().split(' ')[0]; // Basic implementation
};

/**
 * Validate form input against configured limits
 * @param {string} field - The field name
 * @param {string} value - The value to validate
 * @returns {Promise<boolean>} Whether the value is valid
 */
export const validateInput = async (field, value) => {
    const config = await import('./config.js').then(m => m.default);
    const { maxInputLength } = config.ui;
    
    return value.length <= maxInputLength[field];
};

/**
 * Track an analytics event
 * @param {string} eventName - The name of the event
 * @param {Object} data - Additional event data
 */
export const trackEvent = async (eventName, data = {}) => {
    const config = await import('./config.js').then(m => m.default);
    
    if (!config.analytics.enabled) return;
    
    try {
        // Implement analytics tracking logic
        console.log(`Tracking event: ${eventName}`, data);
    } catch (error) {
        console.error('Error tracking event:', error);
    }
};

/**
 * Measure performance of an operation
 * @param {string} metricName - The name of the metric
 * @param {Function} operation - The operation to measure
 * @returns {Promise<any>} The result of the operation
 */
export const measurePerformance = async (metricName, operation) => {
    const config = await import('./config.js').then(m => m.default);
    
    if (!config.performance.enabled) return operation();
    
    const startTime = performance.now();
    try {
        const result = await operation();
        const duration = performance.now() - startTime;
        
        // Log performance metric
        console.log(`Performance metric - ${metricName}: ${duration}ms`);
        
        // Check against threshold
        const threshold = config.performance.thresholds[metricName];
        if (threshold && duration > threshold) {
            console.warn(`Performance warning - ${metricName} exceeded threshold of ${threshold}ms`);
        }
        
        return result;
    } catch (error) {
        const duration = performance.now() - startTime;
        console.error(`Performance error - ${metricName} failed after ${duration}ms:`, error);
        throw error;
    }
};

/**
 * Handle API errors consistently
 * @param {Error} error - The error to handle
 * @returns {Promise<Object>} Formatted error object
 */
export const handleApiError = async (error) => {
    const config = await import('./config.js').then(m => m.default);
    
    const errorDetails = {
        message: error.message,
        timestamp: new Date().toISOString(),
    };
    
    if (config.errors.showDetailedErrors) {
        errorDetails.stack = error.stack;
        errorDetails.details = error.response?.data;
    }
    
    // Report error if endpoint is configured
    if (config.errors.errorReportingEndpoint) {
        fetch(config.errors.errorReportingEndpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(errorDetails),
        }).catch(console.error);
    }
    
    return errorDetails;
};

/**
 * Cache data with the configured settings
 * @param {string} key - The cache key
 * @param {any} data - The data to cache
 */
export const cacheData = async (key, data) => {
    const config = await import('./config.js').then(m => m.default);
    
    if (!config.cache.enabled) return;
    
    const cacheItem = {
        data,
        timestamp: Date.now(),
        expires: Date.now() + (config.cache.maxAge * 1000),
    };
    
    try {
        if (config.cache.storage === 'localStorage') {
            localStorage.setItem(key, JSON.stringify(cacheItem));
        }
    } catch (error) {
        console.error('Error caching data:', error);
    }
};

/**
 * Retrieve cached data
 * @param {string} key - The cache key
 * @returns {any} The cached data or null if not found/expired
 */
export const getCachedData = async (key) => {
    const config = await import('./config.js').then(m => m.default);
    
    if (!config.cache.enabled) return null;
    
    try {
        if (config.cache.storage === 'localStorage') {
            const cached = localStorage.getItem(key);
            if (!cached) return null;
            
            const { data, expires } = JSON.parse(cached);
            if (Date.now() > expires) {
                localStorage.removeItem(key);
                return null;
            }
            
            return data;
        }
    } catch (error) {
        console.error('Error retrieving cached data:', error);
        return null;
    }
};

/**
 * Sanitize user input
 * @param {string} input - The input to sanitize
 * @returns {string} Sanitized input
 */
export const sanitizeInput = (input) => {
    return input
        .replace(/[<>]/g, '') // Remove angle brackets
        .trim(); // Remove leading/trailing whitespace
};

/**
 * Debounce a function
 * @param {Function} func - The function to debounce
 * @param {number} wait - The wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}; 