# 📊 Data Quality Framework

> **Comprehensive Data Quality Management for ImpactCV AI-Powered CV Generation System**  
> **Standards:** DAMA-DMBOK v2 | ISO 8000 | Six Dimensions of Data Quality

---

## 📋 **EXECUTIVE SUMMARY**

### **Data Quality Overview**
ImpactCV implements a comprehensive data quality framework based on the six dimensions of data quality: Accuracy, Completeness, Consistency, Timeliness, Validity, and Uniqueness. The framework ensures high-quality data throughout the AI-powered CV generation pipeline, from document ingestion to final output.

### **Quality Objectives**
1. **Accuracy** - 95%+ data accuracy across all processing stages
2. **Completeness** - 90%+ data completeness for essential fields
3. **Consistency** - 98%+ consistency across data sources
4. **Timeliness** - Real-time quality monitoring and alerting
5. **Validity** - 100% compliance with business rules and formats
6. **Uniqueness** - Duplicate detection and resolution

---

## 🎯 **SIX DIMENSIONS OF DATA QUALITY**

### **Quality Dimensions Framework**

```mermaid
graph TB
    subgraph "Data Quality Dimensions"
        ACC[Accuracy<br/>Correctness of Data]
        COM[Completeness<br/>No Missing Values]
        CON[Consistency<br/>Uniform Format]
        TIM[Timeliness<br/>Up-to-date Data]
        VAL[Validity<br/>Business Rules]
        UNI[Uniqueness<br/>No Duplicates]
    end
    
    subgraph "Quality Monitoring"
        RULES[Quality Rules Engine]
        METRICS[Quality Metrics]
        ALERTS[Quality Alerts]
        REPORTS[Quality Reports]
    end
    
    subgraph "Quality Improvement"
        CLEANSING[Data Cleansing]
        ENRICHMENT[Data Enrichment]
        STANDARDIZATION[Standardization]
        VALIDATION[Validation]
    end
    
    ACC --> RULES
    COM --> RULES
    CON --> RULES
    TIM --> RULES
    VAL --> RULES
    UNI --> RULES
    
    RULES --> METRICS
    METRICS --> ALERTS
    ALERTS --> REPORTS
    
    REPORTS --> CLEANSING
    CLEANSING --> ENRICHMENT
    ENRICHMENT --> STANDARDIZATION
    STANDARDIZATION --> VALIDATION
```

---

## 🏗️ **DATA QUALITY ARCHITECTURE**

### **Quality Assessment Engine**

```python
# Data Quality Assessment Framework
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Any, Optional
import re
import pandas as pd
from datetime import datetime, timedelta

class QualityDimension(Enum):
    ACCURACY = "accuracy"
    COMPLETENESS = "completeness"
    CONSISTENCY = "consistency"
    TIMELINESS = "timeliness"
    VALIDITY = "validity"
    UNIQUENESS = "uniqueness"

class QualityLevel(Enum):
    EXCELLENT = "excellent"  # 95-100%
    GOOD = "good"           # 85-94%
    FAIR = "fair"           # 70-84%
    POOR = "poor"           # <70%

class DataQualityRule(ABC):
    def __init__(self, rule_id: str, dimension: QualityDimension, weight: float = 1.0):
        self.rule_id = rule_id
        self.dimension = dimension
        self.weight = weight
        self.threshold = 0.95  # Default quality threshold
    
    @abstractmethod
    def evaluate(self, data: Any) -> Dict[str, Any]:
        """Evaluate data quality rule and return results"""
        pass

class AccuracyRule(DataQualityRule):
    def __init__(self, rule_id: str, field_name: str, validation_function):
        super().__init__(rule_id, QualityDimension.ACCURACY)
        self.field_name = field_name
        self.validation_function = validation_function
    
    def evaluate(self, data: Dict) -> Dict[str, Any]:
        """Evaluate accuracy of a specific field"""
        field_value = data.get(self.field_name)
        
        if field_value is None:
            return {
                'rule_id': self.rule_id,
                'dimension': self.dimension.value,
                'score': 0.0,
                'passed': False,
                'issues': [f"Field {self.field_name} is missing"]
            }
        
        is_accurate = self.validation_function(field_value)
        
        return {
            'rule_id': self.rule_id,
            'dimension': self.dimension.value,
            'score': 1.0 if is_accurate else 0.0,
            'passed': is_accurate,
            'issues': [] if is_accurate else [f"Field {self.field_name} failed accuracy validation"]
        }

class CompletenessRule(DataQualityRule):
    def __init__(self, rule_id: str, required_fields: List[str]):
        super().__init__(rule_id, QualityDimension.COMPLETENESS)
        self.required_fields = required_fields
    
    def evaluate(self, data: Dict) -> Dict[str, Any]:
        """Evaluate completeness of required fields"""
        missing_fields = []
        present_fields = 0
        
        for field in self.required_fields:
            value = data.get(field)
            if value is None or value == "" or (isinstance(value, str) and value.strip() == ""):
                missing_fields.append(field)
            else:
                present_fields += 1
        
        completeness_score = present_fields / len(self.required_fields) if self.required_fields else 1.0
        
        return {
            'rule_id': self.rule_id,
            'dimension': self.dimension.value,
            'score': completeness_score,
            'passed': completeness_score >= self.threshold,
            'issues': [f"Missing required fields: {', '.join(missing_fields)}"] if missing_fields else [],
            'details': {
                'total_fields': len(self.required_fields),
                'present_fields': present_fields,
                'missing_fields': missing_fields
            }
        }

class ConsistencyRule(DataQualityRule):
    def __init__(self, rule_id: str, field_name: str, expected_format: str):
        super().__init__(rule_id, QualityDimension.CONSISTENCY)
        self.field_name = field_name
        self.expected_format = expected_format
    
    def evaluate(self, data: Dict) -> Dict[str, Any]:
        """Evaluate consistency of field format"""
        field_value = data.get(self.field_name)
        
        if field_value is None:
            return {
                'rule_id': self.rule_id,
                'dimension': self.dimension.value,
                'score': 0.0,
                'passed': False,
                'issues': [f"Field {self.field_name} is missing"]
            }
        
        # Check format consistency based on expected format
        is_consistent = self._check_format_consistency(field_value, self.expected_format)
        
        return {
            'rule_id': self.rule_id,
            'dimension': self.dimension.value,
            'score': 1.0 if is_consistent else 0.0,
            'passed': is_consistent,
            'issues': [] if is_consistent else [f"Field {self.field_name} format inconsistent with {self.expected_format}"]
        }
    
    def _check_format_consistency(self, value: str, expected_format: str) -> bool:
        """Check if value matches expected format"""
        format_patterns = {
            'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            'phone': r'^\+?[\d\s\-\(\)]{10,}$',
            'date': r'^\d{4}-\d{2}-\d{2}$',
            'url': r'^https?://[^\s/$.?#].[^\s]*$'
        }
        
        pattern = format_patterns.get(expected_format)
        if pattern:
            return bool(re.match(pattern, str(value)))
        
        return True  # If no pattern defined, assume consistent

class DataQualityEngine:
    def __init__(self):
        self.rules: List[DataQualityRule] = []
        self.quality_history: List[Dict] = []
        
    def add_rule(self, rule: DataQualityRule):
        """Add a data quality rule to the engine"""
        self.rules.append(rule)
    
    def evaluate_quality(self, data: Dict, context: str = "general") -> Dict[str, Any]:
        """Evaluate data quality against all rules"""
        
        evaluation_results = {
            'evaluation_id': str(uuid.uuid4()),
            'timestamp': datetime.utcnow().isoformat(),
            'context': context,
            'data_sample': self._create_data_sample(data),
            'rule_results': [],
            'dimension_scores': {},
            'overall_score': 0.0,
            'quality_level': QualityLevel.POOR.value,
            'issues': [],
            'recommendations': []
        }
        
        # Evaluate each rule
        dimension_scores = {}
        total_weight = 0
        weighted_score = 0
        
        for rule in self.rules:
            rule_result = rule.evaluate(data)
            evaluation_results['rule_results'].append(rule_result)
            
            # Aggregate by dimension
            dimension = rule_result['dimension']
            if dimension not in dimension_scores:
                dimension_scores[dimension] = {'scores': [], 'weights': []}
            
            dimension_scores[dimension]['scores'].append(rule_result['score'])
            dimension_scores[dimension]['weights'].append(rule.weight)
            
            # Calculate weighted overall score
            weighted_score += rule_result['score'] * rule.weight
            total_weight += rule.weight
            
            # Collect issues
            if rule_result['issues']:
                evaluation_results['issues'].extend(rule_result['issues'])
        
        # Calculate dimension scores
        for dimension, scores_data in dimension_scores.items():
            scores = scores_data['scores']
            weights = scores_data['weights']
            
            if scores:
                weighted_avg = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
                evaluation_results['dimension_scores'][dimension] = {
                    'score': weighted_avg,
                    'level': self._get_quality_level(weighted_avg).value
                }
        
        # Calculate overall score
        if total_weight > 0:
            evaluation_results['overall_score'] = weighted_score / total_weight
            evaluation_results['quality_level'] = self._get_quality_level(evaluation_results['overall_score']).value
        
        # Generate recommendations
        evaluation_results['recommendations'] = self._generate_recommendations(evaluation_results)
        
        # Store in history
        self.quality_history.append(evaluation_results)
        
        return evaluation_results
    
    def _get_quality_level(self, score: float) -> QualityLevel:
        """Determine quality level based on score"""
        if score >= 0.95:
            return QualityLevel.EXCELLENT
        elif score >= 0.85:
            return QualityLevel.GOOD
        elif score >= 0.70:
            return QualityLevel.FAIR
        else:
            return QualityLevel.POOR
    
    def _create_data_sample(self, data: Dict) -> Dict:
        """Create a sample of data for logging (without sensitive info)"""
        sample = {}
        for key, value in data.items():
            if key.lower() in ['password', 'ssn', 'credit_card']:
                sample[key] = '[REDACTED]'
            elif isinstance(value, str) and len(value) > 100:
                sample[key] = value[:100] + '...'
            else:
                sample[key] = value
        return sample
    
    def _generate_recommendations(self, evaluation_results: Dict) -> List[str]:
        """Generate quality improvement recommendations"""
        recommendations = []
        
        for dimension, scores in evaluation_results['dimension_scores'].items():
            score = scores['score']
            
            if score < 0.70:
                if dimension == 'completeness':
                    recommendations.append("Implement data validation at input to ensure required fields are provided")
                elif dimension == 'accuracy':
                    recommendations.append("Add data validation rules and cross-reference checks")
                elif dimension == 'consistency':
                    recommendations.append("Standardize data formats and implement format validation")
                elif dimension == 'timeliness':
                    recommendations.append("Implement real-time data processing and reduce latency")
        
        return recommendations

# CV-specific quality rules
class CVDataQualityRules:
    @staticmethod
    def create_cv_quality_rules() -> List[DataQualityRule]:
        """Create quality rules specific to CV data"""
        
        rules = []
        
        # Completeness rules
        rules.append(CompletenessRule(
            "CV_COMPLETENESS_001",
            required_fields=['name', 'email', 'experience', 'education']
        ))
        
        # Accuracy rules
        rules.append(AccuracyRule(
            "CV_ACCURACY_001",
            "email",
            lambda x: re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', str(x)) is not None
        ))
        
        rules.append(AccuracyRule(
            "CV_ACCURACY_002",
            "phone",
            lambda x: re.match(r'^\+?[\d\s\-\(\)]{10,}$', str(x)) is not None if x else True
        ))
        
        # Consistency rules
        rules.append(ConsistencyRule(
            "CV_CONSISTENCY_001",
            "email",
            "email"
        ))
        
        rules.append(ConsistencyRule(
            "CV_CONSISTENCY_002",
            "phone",
            "phone"
        ))
        
        return rules
```

---

## 📊 **QUALITY MONITORING & METRICS**

### **Real-time Quality Dashboard**

```python
# Quality monitoring and alerting system
from prometheus_client import Histogram, Counter, Gauge
import structlog

class DataQualityMonitor:
    def __init__(self):
        self.logger = structlog.get_logger("data_quality")
        
        # Prometheus metrics
        self.quality_score_histogram = Histogram(
            "data_quality_score",
            "Data quality scores by dimension",
            ["dimension", "context"]
        )
        
        self.quality_rule_violations = Counter(
            "data_quality_violations_total",
            "Total quality rule violations",
            ["rule_id", "dimension", "severity"]
        )
        
        self.quality_level_gauge = Gauge(
            "data_quality_level",
            "Current data quality level",
            ["dimension", "context"]
        )
        
        # Quality thresholds for alerting
        self.alert_thresholds = {
            QualityLevel.POOR: 0.70,
            QualityLevel.FAIR: 0.85,
            QualityLevel.GOOD: 0.95
        }
    
    def record_quality_evaluation(self, evaluation_results: Dict):
        """Record quality evaluation results for monitoring"""
        
        context = evaluation_results['context']
        overall_score = evaluation_results['overall_score']
        
        # Record overall quality score
        self.quality_score_histogram.labels(
            dimension="overall",
            context=context
        ).observe(overall_score)
        
        # Record dimension-specific scores
        for dimension, scores in evaluation_results['dimension_scores'].items():
            score = scores['score']
            
            self.quality_score_histogram.labels(
                dimension=dimension,
                context=context
            ).observe(score)
            
            self.quality_level_gauge.labels(
                dimension=dimension,
                context=context
            ).set(score)
        
        # Record rule violations
        for rule_result in evaluation_results['rule_results']:
            if not rule_result['passed']:
                severity = self._determine_severity(rule_result['score'])
                
                self.quality_rule_violations.labels(
                    rule_id=rule_result['rule_id'],
                    dimension=rule_result['dimension'],
                    severity=severity
                ).inc()
        
        # Check for alerts
        self._check_quality_alerts(evaluation_results)
        
        # Log quality evaluation
        self.logger.info(
            "data_quality_evaluation",
            evaluation_id=evaluation_results['evaluation_id'],
            context=context,
            overall_score=overall_score,
            quality_level=evaluation_results['quality_level'],
            issues_count=len(evaluation_results['issues'])
        )
    
    def _determine_severity(self, score: float) -> str:
        """Determine severity level based on quality score"""
        if score < 0.50:
            return "critical"
        elif score < 0.70:
            return "high"
        elif score < 0.85:
            return "medium"
        else:
            return "low"
    
    def _check_quality_alerts(self, evaluation_results: Dict):
        """Check if quality alerts should be triggered"""
        
        overall_score = evaluation_results['overall_score']
        context = evaluation_results['context']
        
        # Critical quality alert
        if overall_score < self.alert_thresholds[QualityLevel.POOR]:
            self._send_quality_alert(
                severity="critical",
                message=f"Critical data quality issue in {context}",
                score=overall_score,
                details=evaluation_results
            )
        
        # Dimension-specific alerts
        for dimension, scores in evaluation_results['dimension_scores'].items():
            score = scores['score']
            
            if score < self.alert_thresholds[QualityLevel.POOR]:
                self._send_quality_alert(
                    severity="high",
                    message=f"Poor {dimension} quality in {context}",
                    score=score,
                    details={'dimension': dimension, 'context': context}
                )
    
    def _send_quality_alert(self, severity: str, message: str, score: float, details: Dict):
        """Send quality alert to monitoring systems"""
        
        alert = {
            'timestamp': datetime.utcnow().isoformat(),
            'severity': severity,
            'message': message,
            'quality_score': score,
            'details': details,
            'alert_type': 'data_quality'
        }
        
        # Log alert
        self.logger.warning(
            "data_quality_alert",
            **alert
        )
        
        # Send to external alerting systems (Slack, PagerDuty, etc.)
        # Implementation depends on your alerting infrastructure

class QualityReportGenerator:
    def __init__(self, quality_engine: DataQualityEngine):
        self.quality_engine = quality_engine
    
    def generate_quality_report(self, start_date: datetime, end_date: datetime) -> Dict:
        """Generate comprehensive quality report for a time period"""
        
        # Filter quality history by date range
        relevant_evaluations = [
            eval_result for eval_result in self.quality_engine.quality_history
            if start_date <= datetime.fromisoformat(eval_result['timestamp'].replace('Z', '+00:00')) <= end_date
        ]
        
        if not relevant_evaluations:
            return {'error': 'No quality evaluations found for the specified period'}
        
        report = {
            'report_period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'summary': self._generate_summary(relevant_evaluations),
            'trends': self._analyze_trends(relevant_evaluations),
            'dimension_analysis': self._analyze_dimensions(relevant_evaluations),
            'top_issues': self._identify_top_issues(relevant_evaluations),
            'recommendations': self._generate_report_recommendations(relevant_evaluations)
        }
        
        return report
    
    def _generate_summary(self, evaluations: List[Dict]) -> Dict:
        """Generate summary statistics"""
        
        total_evaluations = len(evaluations)
        overall_scores = [eval_result['overall_score'] for eval_result in evaluations]
        
        return {
            'total_evaluations': total_evaluations,
            'average_quality_score': sum(overall_scores) / len(overall_scores),
            'min_quality_score': min(overall_scores),
            'max_quality_score': max(overall_scores),
            'quality_level_distribution': self._calculate_level_distribution(overall_scores)
        }
    
    def _analyze_trends(self, evaluations: List[Dict]) -> Dict:
        """Analyze quality trends over time"""
        
        # Sort evaluations by timestamp
        sorted_evaluations = sorted(evaluations, key=lambda x: x['timestamp'])
        
        # Calculate trend
        scores = [eval_result['overall_score'] for eval_result in sorted_evaluations]
        
        if len(scores) < 2:
            return {'trend': 'insufficient_data'}
        
        # Simple linear trend calculation
        x = list(range(len(scores)))
        n = len(scores)
        
        sum_x = sum(x)
        sum_y = sum(scores)
        sum_xy = sum(x[i] * scores[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        
        trend_direction = "improving" if slope > 0.01 else "declining" if slope < -0.01 else "stable"
        
        return {
            'trend': trend_direction,
            'slope': slope,
            'first_score': scores[0],
            'last_score': scores[-1],
            'change': scores[-1] - scores[0]
        }
```

---

## 🔧 **DATA CLEANSING & IMPROVEMENT**

### **Automated Data Cleansing**

```python
# Data cleansing and improvement engine
class DataCleansingEngine:
    def __init__(self):
        self.cleansing_rules = []
        self.standardization_rules = {}
    
    def add_cleansing_rule(self, rule_id: str, field_name: str, cleansing_function):
        """Add a data cleansing rule"""
        self.cleansing_rules.append({
            'rule_id': rule_id,
            'field_name': field_name,
            'function': cleansing_function
        })
    
    def cleanse_data(self, data: Dict) -> Dict:
        """Apply all cleansing rules to data"""
        
        cleansed_data = data.copy()
        cleansing_log = []
        
        for rule in self.cleansing_rules:
            field_name = rule['field_name']
            original_value = cleansed_data.get(field_name)
            
            if original_value is not None:
                try:
                    cleansed_value = rule['function'](original_value)
                    
                    if cleansed_value != original_value:
                        cleansed_data[field_name] = cleansed_value
                        cleansing_log.append({
                            'rule_id': rule['rule_id'],
                            'field': field_name,
                            'original': original_value,
                            'cleansed': cleansed_value
                        })
                
                except Exception as e:
                    cleansing_log.append({
                        'rule_id': rule['rule_id'],
                        'field': field_name,
                        'error': str(e)
                    })
        
        return {
            'data': cleansed_data,
            'cleansing_log': cleansing_log
        }

# CV-specific cleansing rules
def setup_cv_cleansing_rules(engine: DataCleansingEngine):
    """Setup cleansing rules for CV data"""
    
    # Email normalization
    engine.add_cleansing_rule(
        "EMAIL_NORMALIZE",
        "email",
        lambda x: x.lower().strip() if isinstance(x, str) else x
    )
    
    # Phone number standardization
    engine.add_cleansing_rule(
        "PHONE_STANDARDIZE",
        "phone",
        lambda x: re.sub(r'[^\d+]', '', str(x)) if x else x
    )
    
    # Name capitalization
    engine.add_cleansing_rule(
        "NAME_CAPITALIZE",
        "name",
        lambda x: x.title().strip() if isinstance(x, str) else x
    )
    
    # Remove extra whitespace
    for field in ['name', 'email', 'address', 'company']:
        engine.add_cleansing_rule(
            f"{field.upper()}_TRIM",
            field,
            lambda x: ' '.join(x.split()) if isinstance(x, str) else x
        )
```

---

## ✅ **DATA QUALITY IMPLEMENTATION CHECKLIST**

### **Quality Rules Engine**
- [ ] Six dimensions quality rules implemented
- [ ] CV-specific quality rules defined
- [ ] Rule evaluation engine deployed
- [ ] Quality scoring algorithm implemented
- [ ] Threshold-based quality levels

### **Monitoring & Alerting**
- [ ] Real-time quality monitoring
- [ ] Prometheus metrics integration
- [ ] Quality alert system
- [ ] Dashboard visualization
- [ ] Historical trend analysis

### **Data Cleansing**
- [ ] Automated cleansing rules
- [ ] Data standardization
- [ ] Quality improvement workflows
- [ ] Cleansing audit trails
- [ ] Performance optimization

### **Reporting & Analytics**
- [ ] Quality report generation
- [ ] Trend analysis
- [ ] Dimension-specific insights
- [ ] Recommendation engine
- [ ] Executive dashboards

---

*This comprehensive data quality framework ensures that ImpactCV maintains high-quality data throughout the AI-powered CV generation process, with continuous monitoring, automated improvement, and detailed reporting capabilities.*
