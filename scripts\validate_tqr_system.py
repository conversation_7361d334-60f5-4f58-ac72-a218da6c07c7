#!/usr/bin/env python3
"""
Script de validación completa del sistema TQR con Mistral 7B
Valida funcionalidad, rendimiento y calidad de respuestas
"""

import asyncio
import json
import time
import statistics
from typing import List, Dict, Any
import aiohttp
import logging

# Configuración
BASE_URL = "http://localhost:8000"
TQR_ENDPOINT = f"{BASE_URL}/api/v1/ai-test/generate-tqr"
HEALTH_ENDPOINT = f"{BASE_URL}/api/v1/ai-test/health"

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Dataset de prueba
TEST_CASES = [
    {
        "name": "<PERSON>",
        "position": "Desarrollador Senior",
        "experience_years": 5,
        "achievement_description": "Optimicé el sistema de base de datos reduciendo los tiempos de consulta"
    },
    {
        "name": "<PERSON>",
        "position": "Product Manager",
        "experience_years": 3,
        "achievement_description": "Lideré el lanzamiento de una nueva funcionalidad que aumentó la retención de usuarios"
    },
    {
        "name": "Carlos López",
        "position": "DevOps Engineer",
        "experience_years": 7,
        "achievement_description": "Implementé un pipeline CI/CD que redujo el tiempo de despliegue"
    },
    {
        "name": "Ana Martín",
        "position": "Data Scientist",
        "experience_years": 4,
        "achievement_description": "Desarrollé un modelo de machine learning que mejoró la precisión de predicciones"
    },
    {
        "name": "Roberto Silva",
        "position": "Frontend Developer",
        "experience_years": 2,
        "achievement_description": "Creé una interfaz de usuario que mejoró la experiencia del cliente"
    }
]

class TQRValidator:
    """Validador completo del sistema TQR"""
    
    def __init__(self):
        self.results = {
            "health_check": False,
            "functionality_tests": [],
            "performance_tests": [],
            "quality_tests": [],
            "error_handling_tests": [],
            "summary": {}
        }
    
    async def run_all_tests(self):
        """Ejecutar todas las pruebas de validación"""
        logger.info("🚀 Iniciando validación completa del sistema TQR")
        
        # 1. Health Check
        await self.test_health_check()
        
        # 2. Functionality Tests
        await self.test_functionality()
        
        # 3. Performance Tests
        await self.test_performance()
        
        # 4. Quality Tests
        await self.test_response_quality()
        
        # 5. Error Handling Tests
        await self.test_error_handling()
        
        # 6. Generate Summary
        self.generate_summary()
        
        return self.results
    
    async def test_health_check(self):
        """Probar health check del servicio AI"""
        logger.info("🔍 Testing AI service health check...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(HEALTH_ENDPOINT) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.results["health_check"] = True
                        logger.info(f"✅ Health check passed: {data.get('status', 'unknown')}")
                    else:
                        logger.error(f"❌ Health check failed: {response.status}")
        except Exception as e:
            logger.error(f"❌ Health check error: {e}")
    
    async def test_functionality(self):
        """Probar funcionalidad básica del endpoint TQR"""
        logger.info("🧪 Testing TQR endpoint functionality...")
        
        async with aiohttp.ClientSession() as session:
            for i, test_case in enumerate(TEST_CASES):
                try:
                    start_time = time.time()
                    
                    async with session.post(
                        TQR_ENDPOINT,
                        json=test_case,
                        headers={"Content-Type": "application/json"}
                    ) as response:
                        
                        end_time = time.time()
                        response_time = (end_time - start_time) * 1000  # ms
                        
                        if response.status == 200:
                            data = await response.json()
                            
                            # Validar estructura de respuesta
                            required_fields = ["tarea", "cuantificacion", "resultado", "original_description"]
                            has_all_fields = all(field in data for field in required_fields)
                            
                            test_result = {
                                "test_case": i + 1,
                                "name": test_case["name"],
                                "status": "PASS",
                                "response_time_ms": response_time,
                                "has_all_fields": has_all_fields,
                                "response_data": data
                            }
                            
                            logger.info(f"✅ Test case {i+1} passed ({response_time:.0f}ms)")
                        else:
                            test_result = {
                                "test_case": i + 1,
                                "name": test_case["name"],
                                "status": "FAIL",
                                "error": f"HTTP {response.status}",
                                "response_time_ms": response_time
                            }
                            logger.error(f"❌ Test case {i+1} failed: HTTP {response.status}")
                        
                        self.results["functionality_tests"].append(test_result)
                        
                except Exception as e:
                    logger.error(f"❌ Test case {i+1} error: {e}")
                    self.results["functionality_tests"].append({
                        "test_case": i + 1,
                        "name": test_case["name"],
                        "status": "ERROR",
                        "error": str(e)
                    })
    
    async def test_performance(self):
        """Probar rendimiento con múltiples requests"""
        logger.info("⚡ Testing performance with concurrent requests...")
        
        # Test con 5 requests concurrentes
        test_case = TEST_CASES[0]  # Usar el primer caso de prueba
        
        async def single_request(session, request_id):
            start_time = time.time()
            try:
                async with session.post(
                    TQR_ENDPOINT,
                    json=test_case,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000
                    
                    return {
                        "request_id": request_id,
                        "status": response.status,
                        "response_time_ms": response_time,
                        "success": response.status == 200
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    "request_id": request_id,
                    "status": "ERROR",
                    "response_time_ms": (end_time - start_time) * 1000,
                    "error": str(e),
                    "success": False
                }
        
        async with aiohttp.ClientSession() as session:
            # Ejecutar 5 requests concurrentes
            tasks = [single_request(session, i) for i in range(5)]
            results = await asyncio.gather(*tasks)
            
            # Analizar resultados
            response_times = [r["response_time_ms"] for r in results if r["success"]]
            success_rate = sum(1 for r in results if r["success"]) / len(results) * 100
            
            performance_summary = {
                "concurrent_requests": len(results),
                "success_rate": success_rate,
                "avg_response_time_ms": statistics.mean(response_times) if response_times else 0,
                "min_response_time_ms": min(response_times) if response_times else 0,
                "max_response_time_ms": max(response_times) if response_times else 0,
                "detailed_results": results
            }
            
            self.results["performance_tests"] = performance_summary
            
            logger.info(f"⚡ Performance test completed:")
            logger.info(f"   Success rate: {success_rate:.1f}%")
            logger.info(f"   Avg response time: {performance_summary['avg_response_time_ms']:.0f}ms")
    
    async def test_response_quality(self):
        """Evaluar calidad de las respuestas TQR"""
        logger.info("🎯 Testing TQR response quality...")
        
        # Usar los resultados de functionality tests
        quality_metrics = {
            "total_responses": 0,
            "valid_structure": 0,
            "non_empty_fields": 0,
            "reasonable_length": 0,
            "quality_score": 0
        }
        
        for test in self.results["functionality_tests"]:
            if test["status"] == "PASS" and "response_data" in test:
                quality_metrics["total_responses"] += 1
                data = test["response_data"]
                
                # Verificar estructura válida
                if test.get("has_all_fields", False):
                    quality_metrics["valid_structure"] += 1
                
                # Verificar campos no vacíos
                if all(data.get(field, "").strip() for field in ["tarea", "cuantificacion", "resultado"]):
                    quality_metrics["non_empty_fields"] += 1
                
                # Verificar longitud razonable (>10 caracteres por campo)
                if all(len(data.get(field, "")) > 10 for field in ["tarea", "cuantificacion", "resultado"]):
                    quality_metrics["reasonable_length"] += 1
        
        # Calcular score de calidad
        if quality_metrics["total_responses"] > 0:
            quality_metrics["quality_score"] = (
                quality_metrics["valid_structure"] + 
                quality_metrics["non_empty_fields"] + 
                quality_metrics["reasonable_length"]
            ) / (quality_metrics["total_responses"] * 3) * 100
        
        self.results["quality_tests"] = quality_metrics
        
        logger.info(f"🎯 Quality assessment:")
        logger.info(f"   Quality score: {quality_metrics['quality_score']:.1f}%")
        logger.info(f"   Valid responses: {quality_metrics['valid_structure']}/{quality_metrics['total_responses']}")
    
    async def test_error_handling(self):
        """Probar manejo de errores con inputs inválidos"""
        logger.info("🛡️ Testing error handling...")
        
        error_test_cases = [
            {"name": "", "position": "Dev", "experience_years": 5, "achievement_description": "Test"},
            {"name": "Test", "position": "", "experience_years": 5, "achievement_description": "Test"},
            {"name": "Test", "position": "Dev", "experience_years": -1, "achievement_description": "Test"},
            {"name": "Test", "position": "Dev", "experience_years": 5, "achievement_description": ""},
            {}  # Empty request
        ]
        
        error_results = []
        
        async with aiohttp.ClientSession() as session:
            for i, test_case in enumerate(error_test_cases):
                try:
                    async with session.post(
                        TQR_ENDPOINT,
                        json=test_case,
                        headers={"Content-Type": "application/json"}
                    ) as response:
                        
                        # Esperamos que estos casos fallen (400, 422, etc.)
                        handled_correctly = response.status >= 400
                        
                        error_results.append({
                            "test_case": i + 1,
                            "input": test_case,
                            "status_code": response.status,
                            "handled_correctly": handled_correctly
                        })
                        
                        if handled_correctly:
                            logger.info(f"✅ Error case {i+1} handled correctly ({response.status})")
                        else:
                            logger.warning(f"⚠️ Error case {i+1} not handled properly ({response.status})")
                            
                except Exception as e:
                    error_results.append({
                        "test_case": i + 1,
                        "input": test_case,
                        "error": str(e),
                        "handled_correctly": True  # Exception is also valid error handling
                    })
        
        self.results["error_handling_tests"] = error_results
    
    def generate_summary(self):
        """Generar resumen de todas las pruebas"""
        summary = {
            "overall_status": "UNKNOWN",
            "health_check_passed": self.results["health_check"],
            "functionality_pass_rate": 0,
            "performance_metrics": {},
            "quality_score": 0,
            "error_handling_score": 0,
            "recommendations": []
        }
        
        # Functionality pass rate
        func_tests = self.results["functionality_tests"]
        if func_tests:
            passed = sum(1 for t in func_tests if t["status"] == "PASS")
            summary["functionality_pass_rate"] = passed / len(func_tests) * 100
        
        # Performance metrics
        if self.results["performance_tests"]:
            summary["performance_metrics"] = {
                "success_rate": self.results["performance_tests"]["success_rate"],
                "avg_response_time": self.results["performance_tests"]["avg_response_time_ms"]
            }
        
        # Quality score
        if self.results["quality_tests"]:
            summary["quality_score"] = self.results["quality_tests"]["quality_score"]
        
        # Error handling score
        error_tests = self.results["error_handling_tests"]
        if error_tests:
            handled_correctly = sum(1 for t in error_tests if t["handled_correctly"])
            summary["error_handling_score"] = handled_correctly / len(error_tests) * 100
        
        # Overall status
        if (summary["health_check_passed"] and 
            summary["functionality_pass_rate"] >= 80 and
            summary["quality_score"] >= 70):
            summary["overall_status"] = "PASS"
        elif summary["functionality_pass_rate"] >= 60:
            summary["overall_status"] = "PARTIAL"
        else:
            summary["overall_status"] = "FAIL"
        
        # Recommendations
        if summary["functionality_pass_rate"] < 100:
            summary["recommendations"].append("Investigar fallos en tests funcionales")
        if summary["performance_metrics"].get("avg_response_time", 0) > 5000:
            summary["recommendations"].append("Optimizar tiempo de respuesta (>5s)")
        if summary["quality_score"] < 80:
            summary["recommendations"].append("Mejorar calidad de respuestas TQR")
        if summary["error_handling_score"] < 80:
            summary["recommendations"].append("Mejorar manejo de errores")
        
        self.results["summary"] = summary
        
        # Log summary
        logger.info("📊 VALIDATION SUMMARY:")
        logger.info(f"   Overall Status: {summary['overall_status']}")
        logger.info(f"   Health Check: {'✅' if summary['health_check_passed'] else '❌'}")
        logger.info(f"   Functionality: {summary['functionality_pass_rate']:.1f}%")
        logger.info(f"   Quality Score: {summary['quality_score']:.1f}%")
        logger.info(f"   Error Handling: {summary['error_handling_score']:.1f}%")

async def main():
    """Función principal"""
    validator = TQRValidator()
    results = await validator.run_all_tests()
    
    # Guardar resultados
    with open("tqr_validation_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info("💾 Results saved to tqr_validation_results.json")
    
    # Mostrar status final
    status = results["summary"]["overall_status"]
    if status == "PASS":
        logger.info("🎉 TQR SYSTEM VALIDATION: PASSED")
    elif status == "PARTIAL":
        logger.warning("⚠️ TQR SYSTEM VALIDATION: PARTIAL - Needs attention")
    else:
        logger.error("❌ TQR SYSTEM VALIDATION: FAILED")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
