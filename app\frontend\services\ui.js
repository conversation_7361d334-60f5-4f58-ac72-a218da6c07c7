// UI state management service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class UIService {
    constructor() {
        this.theme = config.ui.theme;
        this.language = config.ui.language;
        this.state = {
            loading: false,
            errors: {},
            notifications: [],
            modals: new Map(),
            sidebar: {
                isOpen: false,
                items: [],
            },
            tabs: new Map(),
            forms: new Map(),
        };
        this.subscribers = new Set();
    }

    /**
     * Subscribe to state changes
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of state changes
     * @param {string} path - The state path that changed
     * @param {any} value - The new value
     */
    notifySubscribers(path, value) {
        this.subscribers.forEach(callback => {
            try {
                callback(path, value);
            } catch (error) {
                console.error('Error in state subscriber:', error);
            }
        });
    }

    /**
     * Set a state value
     * @param {string} path - The state path
     * @param {any} value - The new value
     */
    setState(path, value) {
        const parts = path.split('.');
        let current = this.state;

        for (let i = 0; i < parts.length - 1; i++) {
            current = current[parts[i]];
        }

        current[parts[parts.length - 1]] = value;
        this.notifySubscribers(path, value);
    }

    /**
     * Get a state value
     * @param {string} path - The state path
     * @returns {any} The state value
     */
    getState(path) {
        const parts = path.split('.');
        let current = this.state;

        for (const part of parts) {
            if (current === undefined) return undefined;
            current = current[part];
        }

        return current;
    }

    /**
     * Set loading state
     * @param {boolean} loading - The loading state
     */
    setLoading(loading) {
        this.setState('loading', loading);
    }

    /**
     * Add an error message
     * @param {string} field - The field name
     * @param {string} message - The error message
     */
    addError(field, message) {
        const errors = { ...this.getState('errors') };
        errors[field] = message;
        this.setState('errors', errors);
    }

    /**
     * Clear errors
     * @param {string} [field] - Optional field to clear
     */
    clearErrors(field) {
        if (field) {
            const errors = { ...this.getState('errors') };
            delete errors[field];
            this.setState('errors', errors);
        } else {
            this.setState('errors', {});
        }
    }

    /**
     * Add a notification
     * @param {Object} notification - The notification object
     */
    addNotification(notification) {
        const notifications = [...this.getState('notifications')];
        notifications.push({
            id: Date.now(),
            ...notification,
        });
        this.setState('notifications', notifications);
    }

    /**
     * Remove a notification
     * @param {number} id - The notification ID
     */
    removeNotification(id) {
        const notifications = this.getState('notifications')
            .filter(n => n.id !== id);
        this.setState('notifications', notifications);
    }

    /**
     * Show a modal
     * @param {string} id - The modal ID
     * @param {Object} props - The modal props
     */
    showModal(id, props = {}) {
        const modals = new Map(this.getState('modals'));
        modals.set(id, { ...props, isOpen: true });
        this.setState('modals', modals);
    }

    /**
     * Hide a modal
     * @param {string} id - The modal ID
     */
    hideModal(id) {
        const modals = new Map(this.getState('modals'));
        modals.delete(id);
        this.setState('modals', modals);
    }

    /**
     * Toggle sidebar
     * @param {boolean} [isOpen] - Optional state to set
     */
    toggleSidebar(isOpen) {
        const sidebar = { ...this.getState('sidebar') };
        sidebar.isOpen = isOpen !== undefined ? isOpen : !sidebar.isOpen;
        this.setState('sidebar', sidebar);
    }

    /**
     * Set active tab
     * @param {string} tabId - The tab ID
     * @param {string} value - The tab value
     */
    setActiveTab(tabId, value) {
        const tabs = new Map(this.getState('tabs'));
        tabs.set(tabId, value);
        this.setState('tabs', tabs);
    }

    /**
     * Set form state
     * @param {string} formId - The form ID
     * @param {Object} state - The form state
     */
    setFormState(formId, state) {
        const forms = new Map(this.getState('forms'));
        forms.set(formId, { ...forms.get(formId), ...state });
        this.setState('forms', forms);
    }

    /**
     * Get form state
     * @param {string} formId - The form ID
     * @returns {Object} The form state
     */
    getFormState(formId) {
        return this.getState('forms').get(formId) || {};
    }

    /**
     * Reset form state
     * @param {string} formId - The form ID
     */
    resetFormState(formId) {
        const forms = new Map(this.getState('forms'));
        forms.delete(formId);
        this.setState('forms', forms);
    }

    /**
     * Set theme
     * @param {string} theme - The theme name
     */
    setTheme(theme) {
        this.theme = theme;
        document.documentElement.setAttribute('data-theme', theme);
    }

    /**
     * Set language
     * @param {string} language - The language code
     */
    setLanguage(language) {
        this.language = language;
        document.documentElement.setAttribute('lang', language);
    }
}

// Create and export a singleton instance
const uiService = new UIService();
export default uiService; 