# Minimal requirements for ImpactCV to run
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0
psycopg2-binary>=2.9.0
asyncpg>=0.29.0

# Redis
redis>=5.0.0

# Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
cryptography>=41.0.0

# AI/ML (latest compatible versions)
openai>=1.6.1
langchain>=0.1.0
langchain-openai>=0.0.2
faiss-cpu>=1.7.4
numpy>=1.24.0

# Document processing
PyMuPDF>=1.23.0
python-docx>=1.1.0

# HTTP clients
httpx>=0.25.0
aiohttp>=3.9.0

# Utilities
structlog>=23.2.0
rich>=13.7.0
aiofiles>=23.2.0
