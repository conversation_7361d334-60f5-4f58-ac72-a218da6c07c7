"""
Integration tests for TQR system
Tests the complete flow from API endpoint to AI service to response
"""

import pytest
import asyncio
import time
from unittest.mock import patch
import httpx

from app.main import app


class TestTQRIntegration:
    """Integration tests for TQR generation system"""
    
    @pytest.fixture
    async def async_client(self):
        """Create async test client"""
        async with httpx.AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    def sample_requests(self):
        """Sample TQR requests for testing"""
        return [
            {
                "name": "<PERSON>",
                "position": "Senior Developer",
                "experience_years": 5,
                "achievement_description": "Optimicé el sistema de base de datos reduciendo los tiempos de consulta en un 40%"
            },
            {
                "name": "<PERSON>",
                "position": "Product Manager", 
                "experience_years": 3,
                "achievement_description": "Lideré el lanzamiento de una nueva funcionalidad que aumentó la retención de usuarios en un 25%"
            },
            {
                "name": "<PERSON>",
                "position": "DevOps Engineer",
                "experience_years": 4,
                "achievement_description": "Implementé un pipeline de CI/CD que redujo el tiempo de deployment de 2 horas a 15 minutos"
            }
        ]

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_health_check_integration(self, async_client):
        """Test health check integration"""
        response = await async_client.get("/api/v1/ai-test/health")
        
        # Should return health status
        assert response.status_code in [200, 503]  # Healthy or service unavailable
        
        data = response.json()
        assert "status" in data
        
        if response.status_code == 200:
            assert data["status"] == "healthy"
        else:
            assert data["status"] == "unhealthy"

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_tqr_generation_end_to_end(self, async_client, sample_requests):
        """Test complete TQR generation flow"""
        for request_data in sample_requests:
            response = await async_client.post(
                "/api/v1/ai-test/generate-tqr",
                json=request_data
            )
            
            # Should succeed or fail gracefully
            assert response.status_code in [200, 500, 503]
            
            if response.status_code == 200:
                data = response.json()
                
                # Verify response structure
                assert "tarea" in data
                assert "cuantificacion" in data
                assert "resultado" in data
                assert "original_description" in data
                
                # Verify content quality
                assert len(data["tarea"]) > 10
                assert len(data["cuantificacion"]) > 10
                assert len(data["resultado"]) > 10
                assert data["original_description"] == request_data["achievement_description"]
                
                # Verify enhancement (should be different from original)
                assert data["tarea"] != request_data["achievement_description"]

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_tqr_generation_performance(self, async_client, sample_requests):
        """Test TQR generation performance"""
        response_times = []
        
        for request_data in sample_requests:
            start_time = time.time()
            
            response = await async_client.post(
                "/api/v1/ai-test/generate-tqr",
                json=request_data
            )
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            if response.status_code == 200:
                response_times.append(response_time)
                
                # Individual request should complete within reasonable time
                assert response_time < 60000  # 60 seconds max
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            print(f"Average response time: {avg_response_time:.2f}ms")
            
            # Average should be reasonable
            assert avg_response_time < 30000  # 30 seconds average

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_concurrent_tqr_requests(self, async_client, sample_requests):
        """Test concurrent TQR generation requests"""
        async def make_request(request_data):
            try:
                response = await async_client.post(
                    "/api/v1/ai-test/generate-tqr",
                    json=request_data
                )
                return {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "data": response.json() if response.status_code == 200 else None
                }
            except Exception as e:
                return {
                    "status_code": "ERROR",
                    "success": False,
                    "error": str(e)
                }
        
        # Create concurrent requests
        tasks = [make_request(request_data) for request_data in sample_requests]
        results = await asyncio.gather(*tasks)
        
        # Analyze results
        successful_requests = [r for r in results if r["success"]]
        failed_requests = [r for r in results if not r["success"]]
        
        print(f"Successful requests: {len(successful_requests)}")
        print(f"Failed requests: {len(failed_requests)}")
        
        # At least some requests should succeed
        success_rate = len(successful_requests) / len(results) * 100
        assert success_rate >= 50  # At least 50% success rate for concurrent requests

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_error_handling_integration(self, async_client):
        """Test error handling in integration"""
        invalid_requests = [
            {},  # Empty request
            {"name": "Test"},  # Missing fields
            {"name": "", "position": "Dev", "experience_years": 5, "achievement_description": "Test"},  # Empty name
            {"name": "Test", "position": "Dev", "experience_years": -1, "achievement_description": "Test"},  # Invalid years
        ]
        
        for invalid_request in invalid_requests:
            response = await async_client.post(
                "/api/v1/ai-test/generate-tqr",
                json=invalid_request
            )
            
            # Should return appropriate error codes
            assert response.status_code in [400, 422, 500]
            
            data = response.json()
            assert "detail" in data or "message" in data

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_response_consistency(self, async_client):
        """Test response consistency for same input"""
        request_data = {
            "name": "Test User",
            "position": "Developer",
            "experience_years": 3,
            "achievement_description": "Optimicé el sistema de base de datos"
        }
        
        responses = []
        for _ in range(3):
            response = await async_client.post(
                "/api/v1/ai-test/generate-tqr",
                json=request_data
            )
            
            if response.status_code == 200:
                responses.append(response.json())
        
        if len(responses) >= 2:
            # Responses should have consistent structure
            for response in responses:
                assert "tarea" in response
                assert "cuantificacion" in response
                assert "resultado" in response
                assert "original_description" in response
                assert response["original_description"] == request_data["achievement_description"]

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_large_input_handling(self, async_client):
        """Test handling of large inputs"""
        large_description = "Este es un logro muy detallado. " * 100  # Large description
        
        request_data = {
            "name": "Test User",
            "position": "Senior Developer",
            "experience_years": 5,
            "achievement_description": large_description
        }
        
        response = await async_client.post(
            "/api/v1/ai-test/generate-tqr",
            json=request_data
        )
        
        # Should handle large inputs appropriately
        assert response.status_code in [200, 413, 422, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "tarea" in data
            assert "cuantificacion" in data
            assert "resultado" in data

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_special_characters_handling(self, async_client):
        """Test handling of special characters"""
        request_data = {
            "name": "José María Ñoño",
            "position": "Développeur Senior",
            "experience_years": 5,
            "achievement_description": "Optimicé el sistema con caracteres especiales: áéíóú, ñ, ç, ü, emojis 🚀💻"
        }
        
        response = await async_client.post(
            "/api/v1/ai-test/generate-tqr",
            json=request_data
        )
        
        # Should handle Unicode characters correctly
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "tarea" in data
            assert "cuantificacion" in data
            assert "resultado" in data
            
            # Should preserve special characters
            assert data["original_description"] == request_data["achievement_description"]

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_timeout_handling(self, async_client):
        """Test timeout handling in integration"""
        request_data = {
            "name": "Test User",
            "position": "Developer",
            "experience_years": 3,
            "achievement_description": "Test timeout scenario"
        }
        
        # Set a very short timeout to test timeout handling
        try:
            async with httpx.AsyncClient(app=app, base_url="http://test", timeout=0.1) as short_timeout_client:
                response = await short_timeout_client.post(
                    "/api/v1/ai-test/generate-tqr",
                    json=request_data
                )
                
                # Should handle timeout gracefully
                assert response.status_code in [200, 500, 504]
        except httpx.TimeoutException:
            # Timeout exception is also acceptable
            pass

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_api_versioning(self, async_client):
        """Test API versioning"""
        # Test that v1 endpoints are accessible
        response = await async_client.get("/api/v1/ai-test/health")
        assert response.status_code in [200, 503]
        
        # Test that invalid versions return 404
        response = await async_client.get("/api/v2/ai-test/health")
        assert response.status_code == 404

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_content_type_handling(self, async_client):
        """Test content type handling"""
        request_data = {
            "name": "Test User",
            "position": "Developer",
            "experience_years": 3,
            "achievement_description": "Test content type"
        }
        
        # Test with correct content type
        response = await async_client.post(
            "/api/v1/ai-test/generate-tqr",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code in [200, 500]
        
        # Test with incorrect content type
        response = await async_client.post(
            "/api/v1/ai-test/generate-tqr",
            content=str(request_data),
            headers={"Content-Type": "text/plain"}
        )
        assert response.status_code in [400, 422]

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_security_headers(self, async_client):
        """Test security headers in responses"""
        response = await async_client.get("/api/v1/ai-test/health")
        
        # Check for security headers
        security_headers = [
            "x-content-type-options",
            "x-frame-options", 
            "content-security-policy"
        ]
        
        for header in security_headers:
            assert header in response.headers or header.upper() in response.headers
