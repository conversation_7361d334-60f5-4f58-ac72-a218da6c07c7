"""
Document Parser Service
Enterprise document processing with format preservation and security
"""

import io
import logging
import mimetypes
import os
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import fitz  # PyMuPDF
from docx import Document as DocxDocument
from pydantic import BaseModel, Field

from app.core.config import settings

logger = logging.getLogger(__name__)


class DocumentContent(BaseModel):
    """Parsed document content model."""
    
    text: str = Field(..., description="Extracted text content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")
    pages: List[Dict[str, Any]] = Field(default_factory=list, description="Page-level content")
    formatting: Dict[str, Any] = Field(default_factory=dict, description="Formatting information")
    images: List[Dict[str, Any]] = Field(default_factory=list, description="Extracted images metadata")


class DocumentParsingError(Exception):
    """Custom exception for document parsing errors."""
    
    def __init__(self, message: str, error_code: str = "PARSING_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DocumentParser:
    """
    Enterprise document parser with security and format preservation.
    
    Features:
    - PDF parsing with PyMuPDF (text, metadata, formatting)
    - DOCX parsing with python-docx
    - Security validation and sanitization
    - Format preservation and structure extraction
    - Error handling and logging
    """
    
    def __init__(self):
        """Initialize document parser."""
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.allowed_extensions = {'.pdf', '.docx', '.doc', '.txt'}
        self.temp_dir = tempfile.gettempdir()
        
        # Security settings
        self.max_pages = 1000
        self.max_text_length = 10 * 1024 * 1024  # 10MB text
        
    def parse_document(self, file_path: str, file_type: Optional[str] = None) -> DocumentContent:
        """
        Parse document and extract content with formatting.
        
        Args:
            file_path: Path to the document file
            file_type: Optional file type hint
            
        Returns:
            DocumentContent: Parsed document content
            
        Raises:
            DocumentParsingError: If parsing fails
        """
        try:
            # Validate file
            self._validate_file(file_path)
            
            # Detect file type
            if not file_type:
                file_type = self._detect_file_type(file_path)
            
            # Parse based on file type
            if file_type == 'application/pdf':
                return self._parse_pdf(file_path)
            elif file_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
                return self._parse_docx(file_path)
            elif file_type == 'text/plain':
                return self._parse_text(file_path)
            else:
                raise DocumentParsingError(
                    f"Unsupported file type: {file_type}",
                    error_code="UNSUPPORTED_FORMAT"
                )
                
        except DocumentParsingError:
            raise
        except Exception as e:
            logger.error(f"Document parsing failed: {e}")
            raise DocumentParsingError(
                f"Document parsing failed: {str(e)}",
                error_code="PARSING_FAILED",
                details={"file_path": file_path, "error": str(e)}
            )
    
    def parse_from_bytes(self, file_bytes: bytes, filename: str) -> DocumentContent:
        """
        Parse document from bytes.
        
        Args:
            file_bytes: Document bytes
            filename: Original filename for type detection
            
        Returns:
            DocumentContent: Parsed document content
        """
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
            temp_file.write(file_bytes)
            temp_path = temp_file.name
        
        try:
            # Parse document
            result = self.parse_document(temp_path)
            return result
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_path)
            except OSError:
                pass
    
    def _validate_file(self, file_path: str) -> None:
        """Validate file for security and constraints."""
        if not os.path.exists(file_path):
            raise DocumentParsingError(
                f"File not found: {file_path}",
                error_code="FILE_NOT_FOUND"
            )
        
        # Check file size
        file_size = os.path.getsize(file_path)
        if file_size > self.max_file_size:
            raise DocumentParsingError(
                f"File too large: {file_size} bytes (max: {self.max_file_size})",
                error_code="FILE_TOO_LARGE"
            )
        
        # Check file extension
        file_ext = Path(file_path).suffix.lower()
        if file_ext not in self.allowed_extensions:
            raise DocumentParsingError(
                f"File extension not allowed: {file_ext}",
                error_code="EXTENSION_NOT_ALLOWED"
            )
    
    def _detect_file_type(self, file_path: str) -> str:
        """Detect file MIME type."""
        mime_type, _ = mimetypes.guess_type(file_path)
        
        if not mime_type:
            # Fallback to extension-based detection
            ext = Path(file_path).suffix.lower()
            ext_to_mime = {
                '.pdf': 'application/pdf',
                '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                '.doc': 'application/msword',
                '.txt': 'text/plain'
            }
            mime_type = ext_to_mime.get(ext, 'application/octet-stream')
        
        return mime_type
    
    def _parse_pdf(self, file_path: str) -> DocumentContent:
        """Parse PDF document using PyMuPDF."""
        try:
            doc = fitz.open(file_path)
            
            # Security check
            if doc.page_count > self.max_pages:
                raise DocumentParsingError(
                    f"PDF has too many pages: {doc.page_count} (max: {self.max_pages})",
                    error_code="TOO_MANY_PAGES"
                )
            
            # Extract metadata
            metadata = {
                'title': doc.metadata.get('title', ''),
                'author': doc.metadata.get('author', ''),
                'subject': doc.metadata.get('subject', ''),
                'creator': doc.metadata.get('creator', ''),
                'producer': doc.metadata.get('producer', ''),
                'creation_date': doc.metadata.get('creationDate', ''),
                'modification_date': doc.metadata.get('modDate', ''),
                'page_count': doc.page_count,
                'encrypted': doc.is_encrypted,
                'pdf_version': doc.pdf_version(),
            }
            
            # Extract content page by page
            pages = []
            full_text = []
            images = []
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                
                # Extract text
                page_text = page.get_text()
                full_text.append(page_text)
                
                # Extract text with formatting
                text_dict = page.get_text("dict")
                
                # Extract images
                image_list = page.get_images()
                for img_index, img in enumerate(image_list):
                    images.append({
                        'page': page_num + 1,
                        'index': img_index,
                        'xref': img[0],
                        'width': img[2],
                        'height': img[3],
                    })
                
                # Page metadata
                page_info = {
                    'page_number': page_num + 1,
                    'text': page_text,
                    'word_count': len(page_text.split()),
                    'char_count': len(page_text),
                    'rect': list(page.rect),
                    'rotation': page.rotation,
                    'blocks': len(text_dict.get('blocks', [])),
                }
                pages.append(page_info)
            
            doc.close()
            
            # Combine all text
            combined_text = '\n\n'.join(full_text)
            
            # Security check for text length
            if len(combined_text) > self.max_text_length:
                logger.warning(f"PDF text too long, truncating: {len(combined_text)} chars")
                combined_text = combined_text[:self.max_text_length]
            
            # Formatting information
            formatting = {
                'has_images': len(images) > 0,
                'image_count': len(images),
                'total_pages': doc.page_count,
                'text_length': len(combined_text),
            }
            
            return DocumentContent(
                text=combined_text,
                metadata=metadata,
                pages=pages,
                formatting=formatting,
                images=images
            )
            
        except Exception as e:
            raise DocumentParsingError(
                f"PDF parsing failed: {str(e)}",
                error_code="PDF_PARSING_FAILED",
                details={"file_path": file_path}
            )
    
    def _parse_docx(self, file_path: str) -> DocumentContent:
        """Parse DOCX document using python-docx."""
        try:
            doc = DocxDocument(file_path)
            
            # Extract text from paragraphs
            paragraphs = []
            full_text = []
            
            for para in doc.paragraphs:
                if para.text.strip():
                    paragraphs.append({
                        'text': para.text,
                        'style': para.style.name if para.style else None,
                    })
                    full_text.append(para.text)
            
            # Extract text from tables
            tables = []
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        row_data.append(cell.text.strip())
                        full_text.append(cell.text)
                    table_data.append(row_data)
                tables.append(table_data)
            
            # Combine all text
            combined_text = '\n'.join(full_text)
            
            # Security check
            if len(combined_text) > self.max_text_length:
                logger.warning(f"DOCX text too long, truncating: {len(combined_text)} chars")
                combined_text = combined_text[:self.max_text_length]
            
            # Extract core properties
            core_props = doc.core_properties
            metadata = {
                'title': core_props.title or '',
                'author': core_props.author or '',
                'subject': core_props.subject or '',
                'keywords': core_props.keywords or '',
                'created': core_props.created.isoformat() if core_props.created else '',
                'modified': core_props.modified.isoformat() if core_props.modified else '',
                'last_modified_by': core_props.last_modified_by or '',
                'paragraph_count': len(paragraphs),
                'table_count': len(tables),
            }
            
            # Formatting information
            formatting = {
                'has_tables': len(tables) > 0,
                'table_count': len(tables),
                'paragraph_count': len(paragraphs),
                'text_length': len(combined_text),
            }
            
            # Create pages (DOCX doesn't have explicit pages)
            pages = [{
                'page_number': 1,
                'text': combined_text,
                'word_count': len(combined_text.split()),
                'char_count': len(combined_text),
                'paragraphs': len(paragraphs),
                'tables': len(tables),
            }]
            
            return DocumentContent(
                text=combined_text,
                metadata=metadata,
                pages=pages,
                formatting=formatting,
                images=[]  # Image extraction from DOCX is more complex
            )
            
        except Exception as e:
            raise DocumentParsingError(
                f"DOCX parsing failed: {str(e)}",
                error_code="DOCX_PARSING_FAILED",
                details={"file_path": file_path}
            )
    
    def _parse_text(self, file_path: str) -> DocumentContent:
        """Parse plain text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            
            # Security check
            if len(text) > self.max_text_length:
                logger.warning(f"Text file too long, truncating: {len(text)} chars")
                text = text[:self.max_text_length]
            
            # Basic metadata
            file_stat = os.stat(file_path)
            metadata = {
                'filename': os.path.basename(file_path),
                'size': file_stat.st_size,
                'created': file_stat.st_ctime,
                'modified': file_stat.st_mtime,
                'encoding': 'utf-8',
            }
            
            # Create single page
            pages = [{
                'page_number': 1,
                'text': text,
                'word_count': len(text.split()),
                'char_count': len(text),
                'line_count': len(text.splitlines()),
            }]
            
            formatting = {
                'text_length': len(text),
                'line_count': len(text.splitlines()),
                'word_count': len(text.split()),
            }
            
            return DocumentContent(
                text=text,
                metadata=metadata,
                pages=pages,
                formatting=formatting,
                images=[]
            )
            
        except Exception as e:
            raise DocumentParsingError(
                f"Text parsing failed: {str(e)}",
                error_code="TEXT_PARSING_FAILED",
                details={"file_path": file_path}
            )
    
    def extract_cv_sections(self, content: DocumentContent) -> Dict[str, str]:
        """
        Extract common CV sections from document content.
        
        Args:
            content: Parsed document content
            
        Returns:
            Dict[str, str]: Extracted sections
        """
        text = content.text.lower()
        sections = {}
        
        # Common section headers
        section_patterns = {
            'summary': ['summary', 'profile', 'objective', 'about'],
            'experience': ['experience', 'employment', 'work history', 'career'],
            'education': ['education', 'academic', 'qualifications'],
            'skills': ['skills', 'competencies', 'expertise', 'technologies'],
            'certifications': ['certifications', 'certificates', 'licenses'],
            'projects': ['projects', 'portfolio', 'achievements'],
            'contact': ['contact', 'personal details', 'information'],
        }
        
        # Simple section extraction (can be enhanced with ML)
        lines = content.text.split('\n')
        current_section = None
        section_content = []
        
        for line in lines:
            line_lower = line.lower().strip()

            # Check if line is a section header (must be short and mostly the keyword)
            is_header = False
            if line_lower and len(line_lower) <= 50:  # Headers are typically short
                for section_name, patterns in section_patterns.items():
                    for pattern in patterns:
                        # Check if the line is primarily the pattern (allowing for some formatting)
                        if (line_lower == pattern or
                            line_lower.startswith(pattern + ':') or
                            line_lower.startswith(pattern + ' ') or
                            line_lower.endswith(' ' + pattern) or
                            (len(line_lower.split()) <= 3 and pattern in line_lower)):

                            # Save previous section
                            if current_section and section_content:
                                sections[current_section] = '\n'.join(section_content).strip()

                            # Start new section
                            current_section = section_name
                            section_content = []
                            is_header = True
                            break
                    if is_header:
                        break

            if not is_header:
                # Add line to current section
                if current_section and line.strip():
                    section_content.append(line)
        
        # Save last section
        if current_section and section_content:
            sections[current_section] = '\n'.join(section_content).strip()
        
        return sections
    
    def get_document_stats(self, content: DocumentContent) -> Dict[str, Any]:
        """Get document statistics."""
        return {
            'total_pages': len(content.pages),
            'total_words': sum(page.get('word_count', 0) for page in content.pages),
            'total_chars': len(content.text),
            'has_images': len(content.images) > 0,
            'image_count': len(content.images),
            'metadata_fields': len(content.metadata),
            'formatting_preserved': bool(content.formatting),
        }


# Global parser instance
document_parser = DocumentParser()
