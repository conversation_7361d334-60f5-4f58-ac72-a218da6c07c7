# 📚 Metadata Management & Data Catalog

> **Comprehensive Metadata Catalog for ImpactCV AI-Powered CV Generation System**  
> **Standards:** DAMA-DMBOK v2 | Dublin Core | ISO 11179 | W3C DCAT

---

## 📋 **EXECUTIVE SUMMARY**

### **Metadata Management Overview**
ImpactCV implements a comprehensive metadata management system that provides a centralized data catalog, business glossary, data lineage tracking, and automated metadata discovery. The system ensures data discoverability, understanding, and governance across the entire AI-powered CV generation platform.

### **Key Components**
1. **Data Catalog** - Centralized inventory of all data assets
2. **Business Glossary** - Standardized business terminology and definitions
3. **Technical Metadata** - Schema, structure, and technical specifications
4. **Operational Metadata** - Usage patterns, quality metrics, and performance data
5. **Lineage Metadata** - Data flow and transformation tracking

---

## 🏗️ **METADATA ARCHITECTURE**

### **Metadata Framework**

```mermaid
graph TB
    subgraph "Metadata Types"
        BUSINESS[Business Metadata]
        TECHNICAL[Technical Metadata]
        OPERATIONAL[Operational Metadata]
        LINEAGE[Lineage Metadata]
        QUALITY[Quality Metadata]
    end
    
    subgraph "Data Catalog"
        ASSETS[Data Assets]
        SCHEMAS[Schema Registry]
        GLOSSARY[Business Glossary]
        POLICIES[Data Policies]
    end
    
    subgraph "Discovery & Search"
        SEARCH[Search Engine]
        TAGS[Tag Management]
        CLASSIFICATION[Auto Classification]
        RECOMMENDATIONS[Recommendations]
    end
    
    subgraph "Governance"
        STEWARDSHIP[Data Stewardship]
        COMPLIANCE[Compliance Tracking]
        ACCESS[Access Control]
        AUDIT[Audit Trail]
    end
    
    BUSINESS --> ASSETS
    TECHNICAL --> SCHEMAS
    OPERATIONAL --> POLICIES
    LINEAGE --> ASSETS
    QUALITY --> ASSETS
    
    ASSETS --> SEARCH
    SCHEMAS --> TAGS
    GLOSSARY --> CLASSIFICATION
    POLICIES --> RECOMMENDATIONS
    
    SEARCH --> STEWARDSHIP
    TAGS --> COMPLIANCE
    CLASSIFICATION --> ACCESS
    RECOMMENDATIONS --> AUDIT
```

---

## 📊 **DATA CATALOG IMPLEMENTATION**

### **Metadata Schema**

```python
# Comprehensive metadata management system
from sqlalchemy import Column, String, Text, JSON, DateTime, Boolean, Integer, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from enum import Enum
import uuid
from datetime import datetime

Base = declarative_base()

class MetadataType(Enum):
    BUSINESS = "business"
    TECHNICAL = "technical"
    OPERATIONAL = "operational"
    LINEAGE = "lineage"
    QUALITY = "quality"

class DataAssetType(Enum):
    TABLE = "table"
    VIEW = "view"
    FILE = "file"
    API = "api"
    STREAM = "stream"
    MODEL = "model"
    REPORT = "report"

class DataAsset(Base):
    __tablename__ = 'data_assets'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Basic information
    name = Column(String, nullable=False, index=True)
    display_name = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    asset_type = Column(String, nullable=False)  # DataAssetType enum
    
    # Location and access
    system_name = Column(String, nullable=False)
    database_name = Column(String, nullable=True)
    schema_name = Column(String, nullable=True)
    table_name = Column(String, nullable=True)
    file_path = Column(String, nullable=True)
    connection_string = Column(String, nullable=True)
    
    # Business context
    business_domain = Column(String, nullable=True)
    business_owner = Column(String, nullable=True)
    technical_owner = Column(String, nullable=True)
    data_steward = Column(String, nullable=True)
    
    # Classification and sensitivity
    data_classification = Column(String, nullable=True)  # public, internal, confidential, restricted
    sensitivity_level = Column(String, nullable=True)  # low, medium, high, critical
    contains_pii = Column(Boolean, default=False)
    gdpr_applicable = Column(Boolean, default=False)
    
    # Quality and usage
    quality_score = Column(Float, nullable=True)
    usage_frequency = Column(String, nullable=True)  # daily, weekly, monthly, rarely
    last_accessed = Column(DateTime, nullable=True)
    record_count = Column(Integer, nullable=True)
    
    # Lifecycle
    created_date = Column(DateTime, default=datetime.utcnow)
    last_updated = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String, nullable=True)
    status = Column(String, default='active')  # active, deprecated, archived
    
    # Tags and categories
    tags = Column(JSON, nullable=True)  # List of tags
    categories = Column(JSON, nullable=True)  # List of categories
    
    # Relationships
    columns = relationship("DataColumn", back_populates="asset")
    lineage_sources = relationship("DataLineage", foreign_keys="DataLineage.target_asset_id")
    lineage_targets = relationship("DataLineage", foreign_keys="DataLineage.source_asset_id")

class DataColumn(Base):
    __tablename__ = 'data_columns'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    asset_id = Column(String, ForeignKey('data_assets.id'), nullable=False)
    
    # Column information
    column_name = Column(String, nullable=False)
    display_name = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    data_type = Column(String, nullable=False)
    max_length = Column(Integer, nullable=True)
    is_nullable = Column(Boolean, default=True)
    is_primary_key = Column(Boolean, default=False)
    is_foreign_key = Column(Boolean, default=False)
    
    # Business context
    business_name = Column(String, nullable=True)
    business_definition = Column(Text, nullable=True)
    business_rules = Column(JSON, nullable=True)
    
    # Data quality
    quality_rules = Column(JSON, nullable=True)
    sample_values = Column(JSON, nullable=True)
    value_distribution = Column(JSON, nullable=True)
    
    # Privacy and security
    contains_pii = Column(Boolean, default=False)
    pii_type = Column(String, nullable=True)  # name, email, phone, ssn, etc.
    encryption_required = Column(Boolean, default=False)
    masking_required = Column(Boolean, default=False)
    
    # Statistics
    distinct_count = Column(Integer, nullable=True)
    null_count = Column(Integer, nullable=True)
    min_value = Column(String, nullable=True)
    max_value = Column(String, nullable=True)
    
    # Relationships
    asset = relationship("DataAsset", back_populates="columns")

class BusinessGlossaryTerm(Base):
    __tablename__ = 'business_glossary'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Term information
    term = Column(String, nullable=False, unique=True, index=True)
    display_name = Column(String, nullable=True)
    definition = Column(Text, nullable=False)
    context = Column(Text, nullable=True)
    
    # Business context
    business_domain = Column(String, nullable=True)
    category = Column(String, nullable=True)
    subcategory = Column(String, nullable=True)
    
    # Relationships
    synonyms = Column(JSON, nullable=True)  # List of synonyms
    related_terms = Column(JSON, nullable=True)  # List of related term IDs
    parent_term_id = Column(String, ForeignKey('business_glossary.id'), nullable=True)
    
    # Governance
    status = Column(String, default='draft')  # draft, approved, deprecated
    approved_by = Column(String, nullable=True)
    approval_date = Column(DateTime, nullable=True)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime, nullable=True)
    
    # Lifecycle
    created_date = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String, nullable=True)
    last_updated = Column(DateTime, default=datetime.utcnow)
    updated_by = Column(String, nullable=True)

class MetadataCatalogManager:
    def __init__(self, db_session):
        self.db_session = db_session
        self.search_engine = MetadataSearchEngine()
        
    def register_data_asset(
        self,
        name: str,
        asset_type: DataAssetType,
        system_name: str,
        description: str = None,
        business_owner: str = None,
        technical_owner: str = None,
        **kwargs
    ) -> str:
        """Register a new data asset in the catalog"""
        
        asset = DataAsset(
            name=name,
            asset_type=asset_type.value,
            system_name=system_name,
            description=description,
            business_owner=business_owner,
            technical_owner=technical_owner,
            **kwargs
        )
        
        self.db_session.add(asset)
        self.db_session.commit()
        
        # Index for search
        self.search_engine.index_asset(asset)
        
        return asset.id
    
    def add_column_metadata(
        self,
        asset_id: str,
        column_name: str,
        data_type: str,
        description: str = None,
        contains_pii: bool = False,
        **kwargs
    ) -> str:
        """Add column metadata to a data asset"""
        
        column = DataColumn(
            asset_id=asset_id,
            column_name=column_name,
            data_type=data_type,
            description=description,
            contains_pii=contains_pii,
            **kwargs
        )
        
        self.db_session.add(column)
        self.db_session.commit()
        
        return column.id
    
    def create_glossary_term(
        self,
        term: str,
        definition: str,
        business_domain: str = None,
        category: str = None,
        created_by: str = None
    ) -> str:
        """Create a new business glossary term"""
        
        glossary_term = BusinessGlossaryTerm(
            term=term,
            definition=definition,
            business_domain=business_domain,
            category=category,
            created_by=created_by
        )
        
        self.db_session.add(glossary_term)
        self.db_session.commit()
        
        return glossary_term.id
    
    def search_assets(self, query: str, filters: dict = None) -> list:
        """Search for data assets using natural language query"""
        
        return self.search_engine.search(query, filters)
    
    def get_asset_lineage(self, asset_id: str) -> dict:
        """Get complete lineage for a data asset"""
        
        asset = self.db_session.query(DataAsset).get(asset_id)
        if not asset:
            return None
        
        # Get upstream dependencies
        upstream = self.db_session.query(DataLineage).filter(
            DataLineage.target_asset_id == asset_id
        ).all()
        
        # Get downstream dependencies
        downstream = self.db_session.query(DataLineage).filter(
            DataLineage.source_asset_id == asset_id
        ).all()
        
        return {
            'asset': {
                'id': asset.id,
                'name': asset.name,
                'type': asset.asset_type
            },
            'upstream': [
                {
                    'asset_id': lineage.source_asset_id,
                    'transformation': lineage.transformation_logic
                }
                for lineage in upstream
            ],
            'downstream': [
                {
                    'asset_id': lineage.target_asset_id,
                    'transformation': lineage.transformation_logic
                }
                for lineage in downstream
            ]
        }
    
    def generate_data_dictionary(self, asset_id: str) -> dict:
        """Generate comprehensive data dictionary for an asset"""
        
        asset = self.db_session.query(DataAsset).get(asset_id)
        if not asset:
            return None
        
        columns = self.db_session.query(DataColumn).filter(
            DataColumn.asset_id == asset_id
        ).all()
        
        data_dictionary = {
            'asset_info': {
                'name': asset.name,
                'description': asset.description,
                'type': asset.asset_type,
                'owner': asset.business_owner,
                'steward': asset.data_steward,
                'classification': asset.data_classification,
                'last_updated': asset.last_updated.isoformat() if asset.last_updated else None
            },
            'columns': []
        }
        
        for column in columns:
            column_info = {
                'name': column.column_name,
                'display_name': column.display_name,
                'description': column.description,
                'data_type': column.data_type,
                'nullable': column.is_nullable,
                'primary_key': column.is_primary_key,
                'contains_pii': column.contains_pii,
                'business_definition': column.business_definition,
                'sample_values': column.sample_values
            }
            data_dictionary['columns'].append(column_info)
        
        return data_dictionary

# Automated metadata discovery
class MetadataDiscoveryEngine:
    def __init__(self, catalog_manager: MetadataCatalogManager):
        self.catalog_manager = catalog_manager
        
    def discover_database_metadata(self, connection_string: str, database_name: str):
        """Automatically discover and catalog database metadata"""
        
        # Connect to database and extract schema information
        # This would use database-specific drivers (psycopg2, pymongo, etc.)
        
        discovered_assets = []
        
        # Example for PostgreSQL
        if 'postgresql' in connection_string:
            discovered_assets = self._discover_postgresql_metadata(connection_string, database_name)
        
        # Register discovered assets
        for asset_info in discovered_assets:
            asset_id = self.catalog_manager.register_data_asset(**asset_info)
            
            # Add column metadata
            for column_info in asset_info.get('columns', []):
                self.catalog_manager.add_column_metadata(asset_id, **column_info)
        
        return len(discovered_assets)
    
    def _discover_postgresql_metadata(self, connection_string: str, database_name: str) -> list:
        """Discover PostgreSQL database metadata"""
        
        import psycopg2
        from psycopg2.extras import RealDictCursor
        
        discovered_assets = []
        
        try:
            conn = psycopg2.connect(connection_string)
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get all tables
            cursor.execute("""
                SELECT table_schema, table_name, table_type
                FROM information_schema.tables
                WHERE table_catalog = %s
                AND table_schema NOT IN ('information_schema', 'pg_catalog')
            """, (database_name,))
            
            tables = cursor.fetchall()
            
            for table in tables:
                # Get column information
                cursor.execute("""
                    SELECT column_name, data_type, is_nullable, column_default,
                           character_maximum_length
                    FROM information_schema.columns
                    WHERE table_catalog = %s
                    AND table_schema = %s
                    AND table_name = %s
                    ORDER BY ordinal_position
                """, (database_name, table['table_schema'], table['table_name']))
                
                columns = cursor.fetchall()
                
                asset_info = {
                    'name': f"{table['table_schema']}.{table['table_name']}",
                    'asset_type': DataAssetType.TABLE,
                    'system_name': 'postgresql',
                    'database_name': database_name,
                    'schema_name': table['table_schema'],
                    'table_name': table['table_name'],
                    'columns': [
                        {
                            'column_name': col['column_name'],
                            'data_type': col['data_type'],
                            'is_nullable': col['is_nullable'] == 'YES',
                            'max_length': col['character_maximum_length']
                        }
                        for col in columns
                    ]
                }
                
                discovered_assets.append(asset_info)
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to discover PostgreSQL metadata: {e}")
        
        return discovered_assets

# Search and discovery
class MetadataSearchEngine:
    def __init__(self):
        # In production, this would use Elasticsearch or similar
        self.search_index = {}
    
    def index_asset(self, asset: DataAsset):
        """Index a data asset for search"""
        
        search_document = {
            'id': asset.id,
            'name': asset.name,
            'display_name': asset.display_name,
            'description': asset.description,
            'type': asset.asset_type,
            'business_domain': asset.business_domain,
            'tags': asset.tags or [],
            'categories': asset.categories or [],
            'business_owner': asset.business_owner,
            'technical_owner': asset.technical_owner
        }
        
        self.search_index[asset.id] = search_document
    
    def search(self, query: str, filters: dict = None) -> list:
        """Search for assets using query and filters"""
        
        results = []
        query_lower = query.lower()
        
        for asset_id, document in self.search_index.items():
            # Simple text matching (in production, use proper search engine)
            if (query_lower in document.get('name', '').lower() or
                query_lower in document.get('description', '').lower() or
                query_lower in document.get('display_name', '').lower()):
                
                # Apply filters if provided
                if filters:
                    if not self._apply_filters(document, filters):
                        continue
                
                results.append(document)
        
        return results
    
    def _apply_filters(self, document: dict, filters: dict) -> bool:
        """Apply search filters to document"""
        
        for filter_key, filter_value in filters.items():
            if filter_key in document:
                if isinstance(filter_value, list):
                    if document[filter_key] not in filter_value:
                        return False
                else:
                    if document[filter_key] != filter_value:
                        return False
        
        return True
```

---

## 📊 **BUSINESS GLOSSARY MANAGEMENT**

### **CV Domain Glossary**

```python
# CV-specific business glossary terms
class CVBusinessGlossary:
    @staticmethod
    def initialize_cv_glossary(catalog_manager: MetadataCatalogManager):
        """Initialize business glossary with CV-specific terms"""
        
        cv_terms = [
            {
                'term': 'Curriculum Vitae',
                'definition': 'A document that outlines a person\'s educational and professional history, typically used for job applications.',
                'business_domain': 'Human Resources',
                'category': 'Document Types',
                'synonyms': ['CV', 'Resume']
            },
            {
                'term': 'Professional Experience',
                'definition': 'Work history including job titles, companies, dates of employment, and key responsibilities.',
                'business_domain': 'Human Resources',
                'category': 'CV Sections'
            },
            {
                'term': 'Educational Background',
                'definition': 'Academic qualifications including degrees, institutions, graduation dates, and relevant coursework.',
                'business_domain': 'Education',
                'category': 'CV Sections'
            },
            {
                'term': 'Skills Assessment',
                'definition': 'Evaluation and categorization of technical and soft skills relevant to career objectives.',
                'business_domain': 'Human Resources',
                'category': 'Assessment'
            },
            {
                'term': 'ATS Optimization',
                'definition': 'Formatting and keyword optimization to ensure CV compatibility with Applicant Tracking Systems.',
                'business_domain': 'Technology',
                'category': 'Optimization'
            },
            {
                'term': 'Personal Identifiable Information',
                'definition': 'Any information that can be used to identify an individual, requiring special privacy protection.',
                'business_domain': 'Privacy',
                'category': 'Data Classification',
                'synonyms': ['PII', 'Personal Data']
            },
            {
                'term': 'Document Parsing',
                'definition': 'Automated extraction of structured information from unstructured document formats.',
                'business_domain': 'Technology',
                'category': 'Data Processing'
            },
            {
                'term': 'AI Enhancement',
                'definition': 'Use of artificial intelligence to improve content quality, relevance, and presentation.',
                'business_domain': 'Technology',
                'category': 'AI Processing'
            }
        ]
        
        for term_data in cv_terms:
            catalog_manager.create_glossary_term(**term_data)
```

---

## 🔍 **METADATA QUALITY & GOVERNANCE**

### **Metadata Quality Assessment**

```python
# Metadata quality management
class MetadataQualityManager:
    def __init__(self, catalog_manager: MetadataCatalogManager):
        self.catalog_manager = catalog_manager
        
    def assess_metadata_quality(self, asset_id: str) -> dict:
        """Assess the quality of metadata for a data asset"""
        
        asset = self.catalog_manager.db_session.query(DataAsset).get(asset_id)
        if not asset:
            return None
        
        quality_score = 0
        max_score = 100
        quality_issues = []
        
        # Basic information completeness (30 points)
        if asset.description:
            quality_score += 15
        else:
            quality_issues.append("Missing asset description")
        
        if asset.business_owner:
            quality_score += 10
        else:
            quality_issues.append("Missing business owner")
        
        if asset.data_steward:
            quality_score += 5
        else:
            quality_issues.append("Missing data steward")
        
        # Classification and governance (25 points)
        if asset.data_classification:
            quality_score += 10
        else:
            quality_issues.append("Missing data classification")
        
        if asset.sensitivity_level:
            quality_score += 10
        else:
            quality_issues.append("Missing sensitivity level")
        
        if asset.tags:
            quality_score += 5
        else:
            quality_issues.append("Missing tags")
        
        # Column metadata completeness (25 points)
        columns = self.catalog_manager.db_session.query(DataColumn).filter(
            DataColumn.asset_id == asset_id
        ).all()
        
        if columns:
            columns_with_description = sum(1 for col in columns if col.description)
            column_description_ratio = columns_with_description / len(columns)
            quality_score += int(15 * column_description_ratio)
            
            columns_with_business_definition = sum(1 for col in columns if col.business_definition)
            business_definition_ratio = columns_with_business_definition / len(columns)
            quality_score += int(10 * business_definition_ratio)
            
            if column_description_ratio < 0.8:
                quality_issues.append(f"Only {column_description_ratio:.1%} of columns have descriptions")
        else:
            quality_issues.append("No column metadata available")
        
        # Usage and maintenance (20 points)
        if asset.last_accessed:
            days_since_access = (datetime.utcnow() - asset.last_accessed).days
            if days_since_access <= 30:
                quality_score += 10
            elif days_since_access <= 90:
                quality_score += 5
        
        if asset.quality_score and asset.quality_score > 0.8:
            quality_score += 10
        
        return {
            'asset_id': asset_id,
            'quality_score': quality_score,
            'quality_percentage': (quality_score / max_score) * 100,
            'quality_level': self._get_quality_level(quality_score / max_score),
            'issues': quality_issues,
            'recommendations': self._generate_quality_recommendations(quality_issues)
        }
    
    def _get_quality_level(self, score_ratio: float) -> str:
        """Determine quality level based on score"""
        if score_ratio >= 0.9:
            return "Excellent"
        elif score_ratio >= 0.75:
            return "Good"
        elif score_ratio >= 0.6:
            return "Fair"
        else:
            return "Poor"
    
    def _generate_quality_recommendations(self, issues: list) -> list:
        """Generate recommendations to improve metadata quality"""
        
        recommendations = []
        
        if "Missing asset description" in issues:
            recommendations.append("Add a comprehensive description explaining the purpose and content of this data asset")
        
        if "Missing business owner" in issues:
            recommendations.append("Assign a business owner responsible for this data asset")
        
        if "Missing data classification" in issues:
            recommendations.append("Classify the data according to your organization's data classification policy")
        
        if any("columns have descriptions" in issue for issue in issues):
            recommendations.append("Add descriptions to all columns to improve data understanding")
        
        return recommendations
```

---

## ✅ **METADATA MANAGEMENT IMPLEMENTATION CHECKLIST**

### **Data Catalog**
- [ ] Data asset registration system
- [ ] Column-level metadata capture
- [ ] Business context documentation
- [ ] Technical specifications tracking
- [ ] Automated metadata discovery

### **Business Glossary**
- [ ] Standardized terminology management
- [ ] CV domain-specific terms
- [ ] Synonym and relationship tracking
- [ ] Approval workflow for terms
- [ ] Usage analytics

### **Search & Discovery**
- [ ] Full-text search capabilities
- [ ] Faceted search and filtering
- [ ] Recommendation engine
- [ ] Tag-based organization
- [ ] Advanced query interface

### **Quality & Governance**
- [ ] Metadata quality assessment
- [ ] Completeness monitoring
- [ ] Data stewardship workflows
- [ ] Compliance tracking
- [ ] Automated quality alerts

### **Integration & Automation**
- [ ] Database schema discovery
- [ ] API metadata extraction
- [ ] File system scanning
- [ ] Real-time metadata updates
- [ ] External system integration

---

*This comprehensive metadata management system ensures that all data assets in ImpactCV are properly cataloged, documented, and discoverable, supporting effective data governance and enabling users to find and understand data assets efficiently.*
