"""
Authentication Endpoints
OAuth 2.0 + JWT authentication with comprehensive security
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr

from app.core.auth import (
    AuthenticationError,
    AuthorizationError,
    JWTManager,
    PasswordManager,
    TokenResponse,
    User,
    auth_service,
    get_current_active_user,
    get_current_user,
    require_admin,
)
from app.core.config import settings
from app.core.logging import security_audit_logger

router = APIRouter()


class LoginRequest(BaseModel):
    """Login request model."""
    
    email: EmailStr
    password: str
    remember_me: bool = False


class RefreshTokenRequest(BaseModel):
    """Refresh token request model."""
    
    refresh_token: str


class ChangePasswordRequest(BaseModel):
    """Change password request model."""
    
    current_password: str
    new_password: str


class PasswordResetRequest(BaseModel):
    """Password reset request model."""
    
    email: EmailStr


class PasswordResetConfirmRequest(BaseModel):
    """Password reset confirmation model."""
    
    token: str
    new_password: str


class UserProfileResponse(BaseModel):
    """User profile response model."""
    
    user: User
    permissions: Dict[str, Any]
    last_login: str
    account_status: str


@router.post("/login", response_model=TokenResponse)
async def login(
    request: Request,
    login_data: LoginRequest
) -> TokenResponse:
    """
    Authenticate user and return JWT tokens.
    
    Args:
        request: FastAPI request object
        login_data: Login credentials
        
    Returns:
        TokenResponse: JWT tokens and user information
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Authenticate user
        user = await auth_service.authenticate_user(
            email=login_data.email,
            password=login_data.password,
            request=request
        )
        
        # Create tokens
        token_response = await auth_service.create_tokens(user)
        
        # Extend refresh token expiration if remember_me is True
        if login_data.remember_me:
            # Create longer-lived refresh token (30 days)
            extended_refresh_token = JWTManager.create_refresh_token(
                data={
                    "sub": user.id,
                    "email": user.email,
                    "scopes": user.scopes,
                },
                expires_delta=timedelta(days=30)
            )
            token_response.refresh_token = extended_refresh_token
        
        return token_response
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/token", response_model=TokenResponse)
async def login_for_access_token(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends()
) -> TokenResponse:
    """
    OAuth2 compatible token endpoint.
    
    Args:
        request: FastAPI request object
        form_data: OAuth2 form data
        
    Returns:
        TokenResponse: JWT tokens and user information
    """
    try:
        # Authenticate user
        user = await auth_service.authenticate_user(
            email=form_data.username,  # OAuth2 uses username field for email
            password=form_data.password,
            request=request
        )
        
        # Create tokens
        return await auth_service.create_tokens(user)
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest
) -> TokenResponse:
    """
    Refresh access token using refresh token.
    
    Args:
        refresh_data: Refresh token data
        
    Returns:
        TokenResponse: New JWT tokens
        
    Raises:
        HTTPException: If refresh fails
    """
    try:
        return await auth_service.refresh_access_token(refresh_data.refresh_token)
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/logout")
async def logout(
    request: Request,
    current_user: User = Depends(get_current_user)
) -> Dict[str, str]:
    """
    Logout user and revoke tokens.
    
    Args:
        request: FastAPI request object
        current_user: Current authenticated user
        
    Returns:
        Dict[str, str]: Logout confirmation
    """
    # Get token from Authorization header
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header[7:]  # Remove "Bearer " prefix
        auth_service.revoke_token(token)
    
    # Log logout event
    security_audit_logger.log_security_event(
        "user_logout",
        severity="INFO",
        description="User logged out",
        user_id=current_user.id,
        ip_address=request.client.host if request.client else "unknown"
    )
    
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=UserProfileResponse)
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user)
) -> UserProfileResponse:
    """
    Get current user profile information.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        UserProfileResponse: User profile data
    """
    # Calculate permissions based on scopes
    permissions = {
        "can_read": "read" in current_user.scopes,
        "can_write": "write" in current_user.scopes,
        "can_admin": "admin" in current_user.scopes,
        "can_generate_cv": "cv:generate" in current_user.scopes,
        "can_read_cv": "cv:read" in current_user.scopes,
        "can_write_cv": "cv:write" in current_user.scopes,
        "can_delete_cv": "cv:delete" in current_user.scopes,
        "can_read_user": "user:read" in current_user.scopes,
        "can_write_user": "user:write" in current_user.scopes,
    }
    
    return UserProfileResponse(
        user=current_user,
        permissions=permissions,
        last_login=current_user.last_login.isoformat() if current_user.last_login else "Never",
        account_status="active" if current_user.is_active else "inactive"
    )


@router.put("/change-password")
async def change_password(
    request: Request,
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, str]:
    """
    Change user password.
    
    Args:
        request: FastAPI request object
        password_data: Password change data
        current_user: Current authenticated user
        
    Returns:
        Dict[str, str]: Success message
        
    Raises:
        HTTPException: If password change fails
    """
    try:
        # TODO: Implement actual password change logic
        # For now, simulate password validation
        
        # Verify current password (would need to get user from DB)
        # user_in_db = await get_user_from_db(current_user.id)
        # if not PasswordManager.verify_password(password_data.current_password, user_in_db.hashed_password):
        #     raise HTTPException(status_code=400, detail="Current password is incorrect")
        
        # Validate new password strength
        from app.core.security import PasswordSecurity
        validation = PasswordSecurity.validate_password_strength(password_data.new_password)
        
        if not validation["valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Password does not meet security requirements",
                    "issues": validation["issues"]
                }
            )
        
        # Hash new password
        new_hashed_password = PasswordManager.hash_password(password_data.new_password)
        
        # TODO: Update password in database
        # await update_user_password(current_user.id, new_hashed_password)
        
        # Log password change
        security_audit_logger.log_security_event(
            "password_changed",
            severity="INFO",
            description="User changed password",
            user_id=current_user.id,
            ip_address=request.client.host if request.client else "unknown"
        )
        
        return {"message": "Password changed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change password"
        )


@router.post("/forgot-password")
async def forgot_password(
    request: Request,
    reset_data: PasswordResetRequest
) -> Dict[str, str]:
    """
    Initiate password reset process.
    
    Args:
        request: FastAPI request object
        reset_data: Password reset request data
        
    Returns:
        Dict[str, str]: Success message
    """
    # TODO: Implement actual password reset logic
    # 1. Verify user exists
    # 2. Generate secure reset token
    # 3. Send reset email
    # 4. Store token with expiration
    
    # Log password reset request
    security_audit_logger.log_security_event(
        "password_reset_requested",
        severity="INFO",
        description="Password reset requested",
        email="[EMAIL_REDACTED]",  # Don't log actual email
        ip_address=request.client.host if request.client else "unknown"
    )
    
    # Always return success to prevent email enumeration
    return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password")
async def reset_password(
    request: Request,
    reset_data: PasswordResetConfirmRequest
) -> Dict[str, str]:
    """
    Confirm password reset with token.
    
    Args:
        request: FastAPI request object
        reset_data: Password reset confirmation data
        
    Returns:
        Dict[str, str]: Success message
        
    Raises:
        HTTPException: If reset fails
    """
    try:
        # TODO: Implement actual password reset confirmation
        # 1. Verify reset token
        # 2. Check token expiration
        # 3. Validate new password
        # 4. Update password
        # 5. Invalidate reset token
        
        # Validate new password strength
        from app.core.security import PasswordSecurity
        validation = PasswordSecurity.validate_password_strength(reset_data.new_password)
        
        if not validation["valid"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Password does not meet security requirements",
                    "issues": validation["issues"]
                }
            )
        
        # Log password reset completion
        security_audit_logger.log_security_event(
            "password_reset_completed",
            severity="INFO",
            description="Password reset completed",
            ip_address=request.client.host if request.client else "unknown"
        )
        
        return {"message": "Password reset successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )


@router.get("/verify-token")
async def verify_token(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Verify if current token is valid.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Dict[str, Any]: Token verification result
    """
    return {
        "valid": True,
        "user_id": current_user.id,
        "email": current_user.email,
        "scopes": current_user.scopes,
        "is_active": current_user.is_active,
        "is_verified": current_user.is_verified
    }


@router.get("/admin/users", dependencies=[Depends(require_admin)])
async def list_users(
    current_user: User = Depends(require_admin)
) -> Dict[str, Any]:
    """
    List all users (admin only).
    
    Args:
        current_user: Current admin user
        
    Returns:
        Dict[str, Any]: List of users
    """
    # TODO: Implement actual user listing from database
    
    # Log admin action
    security_audit_logger.log_data_access(
        user_id=current_user.id,
        resource="admin/users",
        action="LIST"
    )
    
    return {
        "users": [
            {
                "id": "1",
                "email": "<EMAIL>",
                "username": "admin",
                "is_active": True,
                "is_verified": True,
                "is_superuser": True
            }
        ],
        "total": 1
    }
