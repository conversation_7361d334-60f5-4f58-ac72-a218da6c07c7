# 🗂️ Data Retention Policies

> **Automated Lifecycle Management for ImpactCV AI-Powered CV Generation System**  
> **Compliance:** GDPR Article 5(1)(e) | CCPA | SOX | HIPAA | Industry Best Practices

---

## 📋 **EXECUTIVE SUMMARY**

### **Data Retention Overview**
ImpactCV implements comprehensive data retention policies that ensure compliance with privacy regulations, optimize storage costs, and maintain data security throughout the information lifecycle. The automated retention management system handles data classification, retention scheduling, secure deletion, and audit trail maintenance.

### **Key Principles**
1. **Storage Limitation** - Data retained only as long as necessary for specified purposes
2. **Automated Lifecycle** - Systematic data progression through retention stages
3. **Secure Deletion** - Cryptographic shredding and verified data destruction
4. **Audit Compliance** - Complete retention audit trails for regulatory requirements
5. **Business Continuity** - Balanced retention for operational and legal needs

---

## 🔄 **DATA LIFECYCLE MANAGEMENT**

### **Data Lifecycle Stages**

```mermaid
graph TD
    subgraph "Data Lifecycle Stages"
        CREATE[Data Creation]
        ACTIVE[Active Use]
        INACTIVE[Inactive Storage]
        ARCHIVE[Long-term Archive]
        DISPOSE[Secure Disposal]
    end
    
    subgraph "Retention Triggers"
        PURPOSE[Purpose Fulfillment]
        LEGAL[Legal Requirements]
        CONSENT[Consent Withdrawal]
        REQUEST[Deletion Request]
        SCHEDULE[Scheduled Retention]
    end
    
    subgraph "Deletion Methods"
        CRYPTO[Crypto Shredding]
        PHYSICAL[Physical Destruction]
        OVERWRITE[Secure Overwrite]
        ANONYMIZE[Anonymization]
    end
    
    CREATE --> ACTIVE
    ACTIVE --> INACTIVE
    INACTIVE --> ARCHIVE
    ARCHIVE --> DISPOSE
    
    PURPOSE --> DISPOSE
    LEGAL --> ARCHIVE
    CONSENT --> DISPOSE
    REQUEST --> DISPOSE
    SCHEDULE --> DISPOSE
    
    DISPOSE --> CRYPTO
    DISPOSE --> PHYSICAL
    DISPOSE --> OVERWRITE
    DISPOSE --> ANONYMIZE
```

---

## 📊 **RETENTION POLICY FRAMEWORK**

### **Data Classification & Retention Periods**

```python
# Data retention policy implementation
from enum import Enum
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import uuid

class DataCategory(Enum):
    PERSONAL_DATA = "personal_data"
    CV_CONTENT = "cv_content"
    UPLOADED_DOCUMENTS = "uploaded_documents"
    SYSTEM_LOGS = "system_logs"
    AUDIT_TRAILS = "audit_trails"
    ANALYTICS_DATA = "analytics_data"
    SECURITY_LOGS = "security_logs"
    BACKUP_DATA = "backup_data"
    TEMPORARY_DATA = "temporary_data"

class RetentionPurpose(Enum):
    SERVICE_DELIVERY = "service_delivery"
    LEGAL_COMPLIANCE = "legal_compliance"
    SECURITY_MONITORING = "security_monitoring"
    BUSINESS_ANALYTICS = "business_analytics"
    SYSTEM_OPERATION = "system_operation"
    AUDIT_REQUIREMENTS = "audit_requirements"

class DeletionMethod(Enum):
    CRYPTO_SHREDDING = "crypto_shredding"
    SECURE_OVERWRITE = "secure_overwrite"
    PHYSICAL_DESTRUCTION = "physical_destruction"
    ANONYMIZATION = "anonymization"
    LOGICAL_DELETION = "logical_deletion"

class RetentionPolicy:
    def __init__(
        self,
        policy_id: str,
        data_category: DataCategory,
        retention_purpose: RetentionPurpose,
        retention_period_days: int,
        deletion_method: DeletionMethod,
        legal_basis: str = None,
        exceptions: List[str] = None
    ):
        self.policy_id = policy_id
        self.data_category = data_category
        self.retention_purpose = retention_purpose
        self.retention_period_days = retention_period_days
        self.deletion_method = deletion_method
        self.legal_basis = legal_basis
        self.exceptions = exceptions or []
        self.created_date = datetime.utcnow()
        self.last_updated = datetime.utcnow()

class RetentionSchedule(Base):
    __tablename__ = 'retention_schedules'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Data identification
    data_id = Column(String, nullable=False, index=True)
    data_category = Column(String, nullable=False)
    data_location = Column(String, nullable=False)  # table, file path, etc.
    
    # Retention policy
    policy_id = Column(String, nullable=False)
    retention_purpose = Column(String, nullable=False)
    
    # Timeline
    creation_date = Column(DateTime, nullable=False)
    retention_start_date = Column(DateTime, nullable=False)
    scheduled_deletion_date = Column(DateTime, nullable=False)
    actual_deletion_date = Column(DateTime, nullable=True)
    
    # Status tracking
    status = Column(String, default='active')  # active, pending_deletion, deleted, archived
    deletion_method = Column(String, nullable=False)
    
    # Compliance tracking
    legal_hold = Column(Boolean, default=False)
    legal_hold_reason = Column(Text, nullable=True)
    consent_withdrawal_date = Column(DateTime, nullable=True)
    
    # User context
    user_id = Column(String, nullable=True, index=True)
    session_id = Column(String, nullable=True)
    
    # Audit information
    created_by = Column(String, default='system')
    last_modified = Column(DateTime, default=datetime.utcnow)
    deletion_verified = Column(Boolean, default=False)
    verification_method = Column(String, nullable=True)

class RetentionPolicyManager:
    def __init__(self, db_session):
        self.db_session = db_session
        self.policies = self._initialize_default_policies()
    
    def _initialize_default_policies(self) -> Dict[str, RetentionPolicy]:
        """Initialize default retention policies for ImpactCV"""
        
        policies = {}
        
        # Personal data - GDPR compliance
        policies['PERSONAL_DATA_001'] = RetentionPolicy(
            policy_id='PERSONAL_DATA_001',
            data_category=DataCategory.PERSONAL_DATA,
            retention_purpose=RetentionPurpose.SERVICE_DELIVERY,
            retention_period_days=365,  # 1 year
            deletion_method=DeletionMethod.CRYPTO_SHREDDING,
            legal_basis='GDPR Article 6(1)(a) - Consent'
        )
        
        # CV content - Service delivery
        policies['CV_CONTENT_001'] = RetentionPolicy(
            policy_id='CV_CONTENT_001',
            data_category=DataCategory.CV_CONTENT,
            retention_purpose=RetentionPurpose.SERVICE_DELIVERY,
            retention_period_days=730,  # 2 years
            deletion_method=DeletionMethod.SECURE_OVERWRITE,
            legal_basis='Contract performance'
        )
        
        # Uploaded documents - Temporary processing
        policies['UPLOADED_DOCS_001'] = RetentionPolicy(
            policy_id='UPLOADED_DOCS_001',
            data_category=DataCategory.UPLOADED_DOCUMENTS,
            retention_purpose=RetentionPurpose.SERVICE_DELIVERY,
            retention_period_days=90,  # 3 months
            deletion_method=DeletionMethod.CRYPTO_SHREDDING,
            legal_basis='Processing necessity'
        )
        
        # System logs - Operational requirements
        policies['SYSTEM_LOGS_001'] = RetentionPolicy(
            policy_id='SYSTEM_LOGS_001',
            data_category=DataCategory.SYSTEM_LOGS,
            retention_purpose=RetentionPurpose.SYSTEM_OPERATION,
            retention_period_days=180,  # 6 months
            deletion_method=DeletionMethod.LOGICAL_DELETION,
            legal_basis='Legitimate interests'
        )
        
        # Audit trails - Legal compliance
        policies['AUDIT_TRAILS_001'] = RetentionPolicy(
            policy_id='AUDIT_TRAILS_001',
            data_category=DataCategory.AUDIT_TRAILS,
            retention_purpose=RetentionPurpose.LEGAL_COMPLIANCE,
            retention_period_days=2555,  # 7 years
            deletion_method=DeletionMethod.ANONYMIZATION,
            legal_basis='Legal obligation'
        )
        
        # Security logs - Security monitoring
        policies['SECURITY_LOGS_001'] = RetentionPolicy(
            policy_id='SECURITY_LOGS_001',
            data_category=DataCategory.SECURITY_LOGS,
            retention_purpose=RetentionPurpose.SECURITY_MONITORING,
            retention_period_days=1095,  # 3 years
            deletion_method=DeletionMethod.SECURE_OVERWRITE,
            legal_basis='Legitimate interests'
        )
        
        # Analytics data - Business insights (anonymized)
        policies['ANALYTICS_DATA_001'] = RetentionPolicy(
            policy_id='ANALYTICS_DATA_001',
            data_category=DataCategory.ANALYTICS_DATA,
            retention_purpose=RetentionPurpose.BUSINESS_ANALYTICS,
            retention_period_days=1095,  # 3 years
            deletion_method=DeletionMethod.ANONYMIZATION,
            legal_basis='Legitimate interests'
        )
        
        # Temporary data - Processing only
        policies['TEMPORARY_DATA_001'] = RetentionPolicy(
            policy_id='TEMPORARY_DATA_001',
            data_category=DataCategory.TEMPORARY_DATA,
            retention_purpose=RetentionPurpose.SERVICE_DELIVERY,
            retention_period_days=1,  # 24 hours
            deletion_method=DeletionMethod.SECURE_OVERWRITE,
            legal_basis='Processing necessity'
        )
        
        return policies
    
    def schedule_data_retention(
        self,
        data_id: str,
        data_category: DataCategory,
        data_location: str,
        user_id: str = None,
        session_id: str = None,
        custom_retention_days: int = None
    ) -> str:
        """Schedule data for retention management"""
        
        # Get applicable policy
        policy = self._get_policy_for_category(data_category)
        if not policy:
            raise ValueError(f"No retention policy found for category {data_category}")
        
        # Use custom retention period if provided
        retention_days = custom_retention_days or policy.retention_period_days
        
        # Calculate dates
        creation_date = datetime.utcnow()
        retention_start_date = creation_date
        scheduled_deletion_date = creation_date + timedelta(days=retention_days)
        
        # Create retention schedule
        schedule = RetentionSchedule(
            data_id=data_id,
            data_category=data_category.value,
            data_location=data_location,
            policy_id=policy.policy_id,
            retention_purpose=policy.retention_purpose.value,
            creation_date=creation_date,
            retention_start_date=retention_start_date,
            scheduled_deletion_date=scheduled_deletion_date,
            deletion_method=policy.deletion_method.value,
            user_id=user_id,
            session_id=session_id
        )
        
        self.db_session.add(schedule)
        self.db_session.commit()
        
        return schedule.id
    
    def _get_policy_for_category(self, category: DataCategory) -> Optional[RetentionPolicy]:
        """Get retention policy for data category"""
        for policy in self.policies.values():
            if policy.data_category == category:
                return policy
        return None
    
    def process_scheduled_deletions(self) -> Dict[str, int]:
        """Process all scheduled deletions that are due"""
        
        current_time = datetime.utcnow()
        
        # Find schedules due for deletion
        due_schedules = self.db_session.query(RetentionSchedule).filter(
            RetentionSchedule.scheduled_deletion_date <= current_time,
            RetentionSchedule.status == 'active',
            RetentionSchedule.legal_hold == False
        ).all()
        
        deletion_results = {
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0
        }
        
        for schedule in due_schedules:
            deletion_results['processed'] += 1
            
            try:
                # Check for legal hold or consent withdrawal
                if self._check_deletion_eligibility(schedule):
                    success = self._execute_deletion(schedule)
                    
                    if success:
                        deletion_results['successful'] += 1
                        schedule.status = 'deleted'
                        schedule.actual_deletion_date = current_time
                        schedule.deletion_verified = True
                    else:
                        deletion_results['failed'] += 1
                        schedule.status = 'deletion_failed'
                else:
                    deletion_results['skipped'] += 1
                    schedule.status = 'deletion_skipped'
                
            except Exception as e:
                deletion_results['failed'] += 1
                schedule.status = 'deletion_error'
                # Log error
                logger.error(f"Deletion failed for schedule {schedule.id}: {e}")
        
        self.db_session.commit()
        return deletion_results
    
    def _check_deletion_eligibility(self, schedule: RetentionSchedule) -> bool:
        """Check if data is eligible for deletion"""
        
        # Check legal hold
        if schedule.legal_hold:
            return False
        
        # Check for active legal proceedings
        if self._has_active_legal_proceedings(schedule.user_id):
            return False
        
        # Check for regulatory requirements
        if self._has_regulatory_retention_requirements(schedule):
            return False
        
        return True
    
    def _execute_deletion(self, schedule: RetentionSchedule) -> bool:
        """Execute the actual data deletion"""
        
        deletion_method = DeletionMethod(schedule.deletion_method)
        
        try:
            if deletion_method == DeletionMethod.CRYPTO_SHREDDING:
                return self._crypto_shred_data(schedule)
            elif deletion_method == DeletionMethod.SECURE_OVERWRITE:
                return self._secure_overwrite_data(schedule)
            elif deletion_method == DeletionMethod.ANONYMIZATION:
                return self._anonymize_data(schedule)
            elif deletion_method == DeletionMethod.LOGICAL_DELETION:
                return self._logical_delete_data(schedule)
            else:
                logger.error(f"Unknown deletion method: {deletion_method}")
                return False
                
        except Exception as e:
            logger.error(f"Deletion execution failed: {e}")
            return False
    
    def _crypto_shred_data(self, schedule: RetentionSchedule) -> bool:
        """Perform cryptographic shredding by destroying encryption keys"""
        
        # For encrypted data, destroy the encryption keys
        # This makes the data unrecoverable even if the encrypted data remains
        
        try:
            # Get data location and type
            data_location = schedule.data_location
            
            if data_location.startswith('file:'):
                # File-based data
                file_path = data_location.replace('file:', '')
                return self._crypto_shred_file(file_path, schedule.data_id)
            
            elif data_location.startswith('db:'):
                # Database record
                table_info = data_location.replace('db:', '').split('.')
                return self._crypto_shred_db_record(table_info[0], table_info[1], schedule.data_id)
            
            else:
                logger.error(f"Unknown data location format: {data_location}")
                return False
                
        except Exception as e:
            logger.error(f"Crypto shredding failed: {e}")
            return False
    
    def _crypto_shred_file(self, file_path: str, data_id: str) -> bool:
        """Crypto shred encrypted file by destroying key"""
        
        # Remove encryption key from key management system
        key_manager = KeyManager()
        key_destroyed = key_manager.destroy_key(f"file_{data_id}")
        
        if key_destroyed:
            # Optionally remove the encrypted file as well
            try:
                os.remove(file_path)
            except FileNotFoundError:
                pass  # File already removed
            
            return True
        
        return False
    
    def _secure_overwrite_data(self, schedule: RetentionSchedule) -> bool:
        """Perform secure overwrite of data"""
        
        # Implement secure deletion by overwriting data multiple times
        # This is more thorough than simple deletion
        
        try:
            data_location = schedule.data_location
            
            if data_location.startswith('db:'):
                # For database records, overwrite with random data before deletion
                table_info = data_location.replace('db:', '').split('.')
                return self._secure_overwrite_db_record(table_info[0], table_info[1], schedule.data_id)
            
            elif data_location.startswith('file:'):
                # For files, overwrite with random data multiple times
                file_path = data_location.replace('file:', '')
                return self._secure_overwrite_file(file_path)
            
            return False
            
        except Exception as e:
            logger.error(f"Secure overwrite failed: {e}")
            return False
    
    def apply_legal_hold(self, data_id: str, reason: str) -> bool:
        """Apply legal hold to prevent deletion"""
        
        schedules = self.db_session.query(RetentionSchedule).filter(
            RetentionSchedule.data_id == data_id
        ).all()
        
        for schedule in schedules:
            schedule.legal_hold = True
            schedule.legal_hold_reason = reason
            schedule.last_modified = datetime.utcnow()
        
        self.db_session.commit()
        return len(schedules) > 0
    
    def remove_legal_hold(self, data_id: str) -> bool:
        """Remove legal hold and resume normal retention"""
        
        schedules = self.db_session.query(RetentionSchedule).filter(
            RetentionSchedule.data_id == data_id,
            RetentionSchedule.legal_hold == True
        ).all()
        
        for schedule in schedules:
            schedule.legal_hold = False
            schedule.legal_hold_reason = None
            schedule.last_modified = datetime.utcnow()
        
        self.db_session.commit()
        return len(schedules) > 0
```

---

## 🔒 **SECURE DELETION METHODS**

### **Cryptographic Shredding Implementation**

```python
# Advanced secure deletion methods
class SecureDeletionService:
    def __init__(self, key_manager):
        self.key_manager = key_manager
        
    def crypto_shred_user_data(self, user_id: str) -> Dict[str, bool]:
        """Perform cryptographic shredding of all user data"""
        
        results = {}
        
        # Destroy user-specific encryption keys
        user_keys = [
            f"user_profile_{user_id}",
            f"user_documents_{user_id}",
            f"user_cv_data_{user_id}",
            f"user_uploads_{user_id}"
        ]
        
        for key_id in user_keys:
            try:
                destroyed = self.key_manager.destroy_key(key_id)
                results[key_id] = destroyed
            except Exception as e:
                logger.error(f"Failed to destroy key {key_id}: {e}")
                results[key_id] = False
        
        # Log crypto shredding event
        self._log_crypto_shredding_event(user_id, results)
        
        return results
    
    def secure_file_deletion(self, file_path: str, passes: int = 3) -> bool:
        """Perform secure file deletion with multiple overwrite passes"""
        
        try:
            if not os.path.exists(file_path):
                return True  # File already doesn't exist
            
            file_size = os.path.getsize(file_path)
            
            with open(file_path, 'r+b') as file:
                for pass_num in range(passes):
                    # Overwrite with random data
                    file.seek(0)
                    random_data = os.urandom(file_size)
                    file.write(random_data)
                    file.flush()
                    os.fsync(file.fileno())  # Force write to disk
            
            # Finally remove the file
            os.remove(file_path)
            
            return True
            
        except Exception as e:
            logger.error(f"Secure file deletion failed for {file_path}: {e}")
            return False
    
    def anonymize_audit_data(self, user_id: str) -> bool:
        """Anonymize audit data while preserving audit trail"""
        
        try:
            # Replace user ID with anonymized identifier
            anonymized_id = f"ANON_{hashlib.sha256(user_id.encode()).hexdigest()[:16]}"
            
            # Update audit records
            audit_records = self.db_session.query(AuditLog).filter(
                AuditLog.user_id == user_id
            ).all()
            
            for record in audit_records:
                record.user_id = anonymized_id
                record.user_details = None  # Remove personal details
                record.anonymized = True
                record.anonymization_date = datetime.utcnow()
            
            self.db_session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Audit data anonymization failed: {e}")
            return False

# Automated retention scheduler
class RetentionScheduler:
    def __init__(self, retention_manager: RetentionPolicyManager):
        self.retention_manager = retention_manager
        
    async def run_daily_retention_job(self):
        """Daily job to process retention schedules"""
        
        logger.info("Starting daily retention processing job")
        
        try:
            # Process scheduled deletions
            deletion_results = self.retention_manager.process_scheduled_deletions()
            
            # Generate retention report
            report = self._generate_retention_report(deletion_results)
            
            # Send notifications if needed
            if deletion_results['failed'] > 0:
                await self._send_retention_alert(deletion_results)
            
            logger.info(f"Retention job completed: {deletion_results}")
            
        except Exception as e:
            logger.error(f"Retention job failed: {e}")
            await self._send_retention_error_alert(str(e))
    
    def _generate_retention_report(self, deletion_results: Dict) -> Dict:
        """Generate daily retention processing report"""
        
        return {
            'date': datetime.utcnow().date().isoformat(),
            'deletion_summary': deletion_results,
            'total_active_schedules': self._count_active_schedules(),
            'upcoming_deletions': self._count_upcoming_deletions(),
            'legal_holds_active': self._count_legal_holds()
        }
    
    async def _send_retention_alert(self, deletion_results: Dict):
        """Send alert for retention processing issues"""
        
        alert_message = f"""
        Retention Processing Alert
        
        Failed deletions: {deletion_results['failed']}
        Successful deletions: {deletion_results['successful']}
        Skipped deletions: {deletion_results['skipped']}
        
        Please review retention logs for details.
        """
        
        # Send to monitoring system
        await self._send_alert(alert_message, severity="warning")
```

---

## 📊 **RETENTION MONITORING & REPORTING**

### **Compliance Reporting**

```python
# Retention compliance reporting
class RetentionComplianceReporter:
    def __init__(self, db_session):
        self.db_session = db_session
    
    def generate_retention_compliance_report(self, start_date: datetime, end_date: datetime) -> Dict:
        """Generate comprehensive retention compliance report"""
        
        report = {
            'report_period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'retention_summary': self._get_retention_summary(start_date, end_date),
            'deletion_summary': self._get_deletion_summary(start_date, end_date),
            'compliance_metrics': self._calculate_compliance_metrics(),
            'policy_adherence': self._assess_policy_adherence(),
            'legal_holds': self._get_legal_holds_summary(),
            'recommendations': self._generate_compliance_recommendations()
        }
        
        return report
    
    def _get_retention_summary(self, start_date: datetime, end_date: datetime) -> Dict:
        """Get retention activity summary"""
        
        schedules = self.db_session.query(RetentionSchedule).filter(
            RetentionSchedule.creation_date.between(start_date, end_date)
        ).all()
        
        summary = {
            'total_schedules_created': len(schedules),
            'by_category': {},
            'by_purpose': {},
            'average_retention_period': 0
        }
        
        # Group by category and purpose
        for schedule in schedules:
            category = schedule.data_category
            purpose = schedule.retention_purpose
            
            summary['by_category'][category] = summary['by_category'].get(category, 0) + 1
            summary['by_purpose'][purpose] = summary['by_purpose'].get(purpose, 0) + 1
        
        # Calculate average retention period
        if schedules:
            total_days = sum(
                (schedule.scheduled_deletion_date - schedule.creation_date).days
                for schedule in schedules
            )
            summary['average_retention_period'] = total_days / len(schedules)
        
        return summary
    
    def _calculate_compliance_metrics(self) -> Dict:
        """Calculate key compliance metrics"""
        
        total_schedules = self.db_session.query(RetentionSchedule).count()
        overdue_deletions = self.db_session.query(RetentionSchedule).filter(
            RetentionSchedule.scheduled_deletion_date < datetime.utcnow(),
            RetentionSchedule.status == 'active'
        ).count()
        
        compliance_rate = ((total_schedules - overdue_deletions) / total_schedules * 100) if total_schedules > 0 else 100
        
        return {
            'total_retention_schedules': total_schedules,
            'overdue_deletions': overdue_deletions,
            'compliance_rate_percentage': compliance_rate,
            'legal_holds_active': self._count_legal_holds(),
            'deletion_success_rate': self._calculate_deletion_success_rate()
        }
```

---

## ✅ **RETENTION POLICY IMPLEMENTATION CHECKLIST**

### **Policy Framework**
- [ ] Data classification schema implemented
- [ ] Retention periods defined for all data categories
- [ ] Legal basis documented for each policy
- [ ] Exception handling procedures
- [ ] Policy review and update process

### **Automated Lifecycle Management**
- [ ] Retention scheduling system
- [ ] Automated deletion processing
- [ ] Secure deletion methods implemented
- [ ] Cryptographic shredding capability
- [ ] Verification and audit trails

### **Compliance & Legal**
- [ ] Legal hold management
- [ ] GDPR Article 5(1)(e) compliance
- [ ] Regulatory retention requirements
- [ ] Data subject deletion requests
- [ ] Audit trail maintenance

### **Monitoring & Reporting**
- [ ] Retention compliance dashboard
- [ ] Automated compliance reporting
- [ ] Deletion success monitoring
- [ ] Legal hold tracking
- [ ] Policy adherence metrics

---

*This comprehensive data retention policy framework ensures that ImpactCV maintains compliance with privacy regulations while optimizing storage costs and maintaining data security throughout the complete data lifecycle.*
