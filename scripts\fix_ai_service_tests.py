#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix AI Service tests by replacing all patch.object calls
"""

import re

def fix_ai_service_tests():
    """Fix all AI Service tests to use direct _provider assignment"""
    
    file_path = "tests/unit/test_ai_service.py"
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace all instances of patch.object with direct assignment
    patterns_to_replace = [
        (
            r'with patch\.object\(ai_service_instance, \'provider\', ([^)]+)\):',
            r'# Mock the _provider directly\n        ai_service_instance._provider = \1'
        ),
        (
            r'with patch\.object\(ai_service_instance, \'provider\', ([^)]+)\):\s*\n\s*with patch\.object\(ai_service_instance, \'settings\'\) as mock_settings:\s*\n\s*mock_settings\.AI_PROVIDER = "([^"]+)"\s*\n',
            r'# Mock the _provider directly\n        ai_service_instance._provider = \1\n        with patch.object(ai_service_instance, \'settings\') as mock_settings:\n            mock_settings.AI_PROVIDER = "\2"\n'
        )
    ]
    
    for pattern, replacement in patterns_to_replace:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    # Fix indentation issues
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Fix indentation after direct assignment
        if line.strip().startswith('ai_service_instance._provider ='):
            fixed_lines.append(line)
            # Next lines should be properly indented
            j = i + 1
            while j < len(lines) and lines[j].strip():
                if lines[j].startswith('            '):  # Over-indented
                    fixed_lines.append(lines[j][4:])  # Remove 4 spaces
                else:
                    fixed_lines.append(lines[j])
                j += 1
            # Skip the lines we already processed
            i = j - 1
        else:
            fixed_lines.append(line)
    
    # Write back the fixed content
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(fixed_lines))
    
    print("✅ Fixed AI Service tests")

if __name__ == "__main__":
    fix_ai_service_tests()
