"""
Performance tests for CV generation system
"""

import pytest
import time
import tempfile
import os
import threading
import concurrent.futures
from unittest.mock import patch, Mock

from app.services.document_parser import document_parser
from app.services.data_normalizer import data_normalizer
from app.core.pii_detector import pii_detector
from app.core.validators import data_validator
from app.services.data_quality import data_quality_service
from app.core.rag_pipeline import rag_pipeline


class TestPerformanceBenchmarks:
    """Performance benchmark tests."""
    
    @pytest.fixture
    def sample_cv_content(self):
        """Generate sample CV content for testing."""
        return """
        <PERSON>
        Senior Software Engineer
        <EMAIL>
        (*************
        
        SUMMARY
        Experienced software engineer with 8+ years in full-stack development.
        Expertise in Python, JavaScript, and cloud technologies. Led teams of 5-10 developers
        and delivered multiple high-impact projects. Passionate about scalable architecture
        and clean code practices.
        
        EXPERIENCE
        Senior Software Engineer - Tech Corp (2020-2023)
        - Led development of microservices architecture serving 1M+ users
        - Implemented CI/CD pipelines reducing deployment time by 75%
        - Mentored 5 junior developers and conducted code reviews
        - Technologies: Python, Django, React, PostgreSQL, AWS, Docker
        
        Software Engineer - StartupXYZ (2018-2020)
        - Built REST APIs handling 10K+ requests per minute
        - Developed real-time analytics dashboard using React and WebSockets
        - Optimized database queries improving response time by 60%
        - Technologies: Python, Flask, JavaScript, MongoDB, Redis
        
        Junior Developer - WebCorp (2016-2018)
        - Developed responsive web applications using HTML, CSS, JavaScript
        - Collaborated with design team to implement pixel-perfect UIs
        - Fixed bugs and implemented new features in legacy codebase
        - Technologies: JavaScript, jQuery, PHP, MySQL
        
        EDUCATION
        Bachelor of Science in Computer Science
        University of Technology (2012-2016)
        GPA: 3.8/4.0
        Relevant Coursework: Data Structures, Algorithms, Database Systems, Software Engineering
        
        SKILLS
        Programming Languages: Python, JavaScript, Java, Go, TypeScript
        Web Frameworks: Django, Flask, React, Vue.js, Node.js, Express
        Databases: PostgreSQL, MongoDB, Redis, MySQL, Elasticsearch
        Cloud & DevOps: AWS, Docker, Kubernetes, Jenkins, GitLab CI
        Tools: Git, JIRA, Confluence, Slack, VS Code, PyCharm
        
        CERTIFICATIONS
        AWS Certified Solutions Architect - Associate (2022)
        Certified Kubernetes Administrator (2021)
        Python Institute PCAP Certification (2020)
        
        PROJECTS
        E-commerce Platform
        - Built scalable e-commerce platform using microservices architecture
        - Handled 100K+ concurrent users during peak traffic
        - Technologies: Python, Django, React, PostgreSQL, Redis, AWS
        - URL: github.com/johndoe/ecommerce-platform
        
        Real-time Chat Application
        - Developed real-time chat application with WebSocket support
        - Implemented end-to-end encryption for secure messaging
        - Technologies: Node.js, Socket.io, React, MongoDB
        - URL: github.com/johndoe/chat-app
        """
    
    @pytest.fixture
    def large_cv_content(self):
        """Generate large CV content for stress testing."""
        base_content = self.sample_cv_content(self)
        # Repeat content to create larger document
        return base_content * 10
    
    def test_document_parsing_performance(self, sample_cv_content):
        """Test document parsing performance."""
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
            tmp.write(sample_cv_content)
            tmp_path = tmp.name
        
        try:
            # Measure parsing time
            start_time = time.time()
            result = document_parser.parse_document(tmp_path)
            end_time = time.time()
            
            parsing_time = end_time - start_time
            
            # Performance assertions
            assert parsing_time < 1.0, f"Document parsing took {parsing_time:.2f}s, should be < 1.0s"
            assert result.text is not None
            assert len(result.text) > 0
            
            # Test section extraction performance
            start_time = time.time()
            sections = document_parser.extract_cv_sections(result)
            end_time = time.time()
            
            extraction_time = end_time - start_time
            assert extraction_time < 0.5, f"Section extraction took {extraction_time:.2f}s, should be < 0.5s"
            assert len(sections) > 0
            
        finally:
            os.unlink(tmp_path)
    
    def test_data_normalization_performance(self, sample_cv_content):
        """Test data normalization performance."""
        # Create CV data structure
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            'summary': sample_cv_content[:500],
            'experience': [
                {
                    'job_title': 'Senior Software Engineer',
                    'company': 'Tech Corp',
                    'start_date': '2020-01',
                    'end_date': '2023-12',
                    'description': sample_cv_content[500:1000]
                },
                {
                    'job_title': 'Software Engineer',
                    'company': 'StartupXYZ',
                    'start_date': '2018-06',
                    'end_date': '2020-01',
                    'description': sample_cv_content[1000:1500]
                }
            ],
            'education': [
                {
                    'degree': 'Bachelor of Science in Computer Science',
                    'institution': 'University of Technology',
                    'start_date': '2012-09',
                    'end_date': '2016-05',
                    'gpa': '3.8'
                }
            ],
            'skills': [
                {'name': 'Python', 'level': 'Expert'},
                {'name': 'JavaScript', 'level': 'Advanced'},
                {'name': 'React', 'level': 'Advanced'},
                {'name': 'Django', 'level': 'Expert'},
                {'name': 'PostgreSQL', 'level': 'Intermediate'}
            ]
        }
        
        # Measure normalization time
        start_time = time.time()
        result = data_normalizer.normalize_cv_data(cv_data)
        end_time = time.time()
        
        normalization_time = end_time - start_time
        
        # Performance assertions
        assert normalization_time < 0.5, f"Data normalization took {normalization_time:.2f}s, should be < 0.5s"
        assert result.is_valid
        assert result.normalized_data is not None
    
    def test_pii_detection_performance(self, sample_cv_content):
        """Test PII detection performance."""
        # Measure PII detection time
        start_time = time.time()
        result = pii_detector.detect_pii(sample_cv_content)
        end_time = time.time()
        
        detection_time = end_time - start_time
        
        # Performance assertions
        assert detection_time < 1.0, f"PII detection took {detection_time:.2f}s, should be < 1.0s"
        assert result.sanitized_text is not None
        assert len(result.pii_matches) >= 0
    
    def test_data_validation_performance(self, sample_cv_content):
        """Test data validation performance."""
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            'summary': sample_cv_content[:300],
            'experience': [
                {
                    'job_title': 'Senior Software Engineer',
                    'company': 'Tech Corp',
                    'start_date': '2020',
                    'end_date': '2023'
                }
            ],
            'education': [
                {
                    'degree': 'Bachelor of Science',
                    'institution': 'University of Technology'
                }
            ],
            'skills': [
                {'name': 'Python'},
                {'name': 'JavaScript'}
            ]
        }
        
        # Measure validation time
        start_time = time.time()
        result = data_validator.validate_cv_data(cv_data)
        end_time = time.time()
        
        validation_time = end_time - start_time
        
        # Performance assertions
        assert validation_time < 0.5, f"Data validation took {validation_time:.2f}s, should be < 0.5s"
        assert result.score >= 0
    
    def test_quality_assessment_performance(self, sample_cv_content):
        """Test data quality assessment performance."""
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            'summary': sample_cv_content[:300],
            'experience': [
                {
                    'job_title': 'Senior Software Engineer',
                    'company': 'Tech Corp',
                    'start_date': '2020',
                    'end_date': '2023'
                }
            ],
            'skills': [
                {'name': 'Python'},
                {'name': 'JavaScript'}
            ]
        }
        
        # Measure quality assessment time
        start_time = time.time()
        result = data_quality_service.assess_cv_quality(cv_data)
        end_time = time.time()
        
        assessment_time = end_time - start_time
        
        # Performance assertions
        assert assessment_time < 1.0, f"Quality assessment took {assessment_time:.2f}s, should be < 1.0s"
        assert result.overall_score >= 0
        assert len(result.metrics) > 0
    
    @patch('app.services.llm_service.llm_service.generate_completion')
    def test_rag_pipeline_performance(self, mock_generate, sample_cv_content):
        """Test RAG pipeline performance."""
        # Mock LLM response
        mock_generate.return_value = {
            'content': 'Generated CV content based on provided context',
            'usage': {'total_tokens': 150},
            'model': 'gpt-4o'
        }
        
        # Measure RAG generation time
        start_time = time.time()
        result = rag_pipeline.generate_cv_section(
            section_type="summary",
            user_input="Generate a professional summary",
            context_data={'experience': sample_cv_content[:1000]}
        )
        end_time = time.time()
        
        generation_time = end_time - start_time
        
        # Performance assertions (excluding actual LLM call time)
        assert generation_time < 2.0, f"RAG pipeline took {generation_time:.2f}s, should be < 2.0s"
        assert result['content'] is not None
    
    def test_concurrent_processing_performance(self, sample_cv_content):
        """Test concurrent processing performance."""
        # Create multiple CV files
        file_paths = []
        for i in range(5):
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
                tmp.write(f"CV {i}\n{sample_cv_content}")
                file_paths.append(tmp.name)
        
        try:
            # Test concurrent document parsing
            start_time = time.time()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = [
                    executor.submit(document_parser.parse_document, path)
                    for path in file_paths
                ]
                
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            end_time = time.time()
            concurrent_time = end_time - start_time
            
            # Should process multiple files efficiently
            assert concurrent_time < 5.0, f"Concurrent processing took {concurrent_time:.2f}s, should be < 5.0s"
            assert len(results) == 5
            assert all(result.text is not None for result in results)
            
        finally:
            # Cleanup
            for path in file_paths:
                try:
                    os.unlink(path)
                except OSError:
                    pass
    
    def test_memory_usage_performance(self, large_cv_content):
        """Test memory usage with large documents."""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create large temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
            tmp.write(large_cv_content)
            tmp_path = tmp.name
        
        try:
            # Process large document
            result = document_parser.parse_document(tmp_path)
            
            # Check memory usage after processing
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            # Memory increase should be reasonable (< 100MB for test data)
            assert memory_increase < 100, f"Memory usage increased by {memory_increase:.2f}MB, should be < 100MB"
            assert result.text is not None
            
        finally:
            os.unlink(tmp_path)
    
    def test_pipeline_end_to_end_performance(self, sample_cv_content):
        """Test complete pipeline performance."""
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
            tmp.write(sample_cv_content)
            tmp_path = tmp.name
        
        try:
            # Measure complete pipeline time
            start_time = time.time()
            
            # Step 1: Document parsing
            parsed_content = document_parser.parse_document(tmp_path)
            sections = document_parser.extract_cv_sections(parsed_content)
            
            # Step 2: Data normalization
            cv_data = {
                'personal': {
                    'full_name': 'John Doe',
                    'email': '<EMAIL>',
                    'phone': '(*************'
                },
                'summary': sections.get('summary', ''),
                'experience': [
                    {
                        'job_title': 'Senior Software Engineer',
                        'company': 'Tech Corp',
                        'start_date': '2020',
                        'end_date': '2023'
                    }
                ],
                'skills': [
                    {'name': 'Python'},
                    {'name': 'JavaScript'}
                ]
            }
            
            normalized_result = data_normalizer.normalize_cv_data(cv_data)
            
            # Step 3: PII detection
            pii_result = pii_detector.detect_pii(parsed_content.text)
            
            # Step 4: Data validation
            validation_result = data_validator.validate_cv_data(cv_data)
            
            # Step 5: Quality assessment
            quality_report = data_quality_service.assess_cv_quality(cv_data)
            
            end_time = time.time()
            pipeline_time = end_time - start_time
            
            # Performance assertions
            assert pipeline_time < 5.0, f"Complete pipeline took {pipeline_time:.2f}s, should be < 5.0s"
            
            # Verify all steps completed successfully
            assert parsed_content.text is not None
            assert normalized_result.is_valid
            assert pii_result.sanitized_text is not None
            assert validation_result.score >= 0
            assert quality_report.overall_score >= 0
            
        finally:
            os.unlink(tmp_path)
    
    def test_stress_testing(self, sample_cv_content):
        """Test system under stress conditions."""
        # Test with many small requests
        start_time = time.time()
        
        for i in range(50):
            # Quick PII detection on small text
            small_text = f"Test {i}: john.doe{i}@email.com"
            result = pii_detector.detect_pii(small_text)
            assert result.sanitized_text is not None
        
        end_time = time.time()
        stress_time = end_time - start_time
        
        # Should handle many small requests efficiently
        assert stress_time < 10.0, f"Stress test took {stress_time:.2f}s, should be < 10.0s"
    
    def test_cache_performance(self):
        """Test caching performance improvements."""
        # Test repeated operations to verify caching
        test_text = "John Doe, email: <EMAIL>, phone: (*************"
        
        # First run (no cache)
        start_time = time.time()
        result1 = pii_detector.detect_pii(test_text)
        first_run_time = time.time() - start_time
        
        # Second run (should use cache if implemented)
        start_time = time.time()
        result2 = pii_detector.detect_pii(test_text)
        second_run_time = time.time() - start_time
        
        # Results should be identical
        assert result1.sanitized_text == result2.sanitized_text
        assert len(result1.pii_matches) == len(result2.pii_matches)
        
        # Second run should be faster or similar (cache benefit)
        # Note: This test might not show improvement if caching isn't implemented
        assert second_run_time <= first_run_time * 1.5  # Allow some variance


class TestScalabilityMetrics:
    """Test scalability metrics and limits."""
    
    def test_throughput_measurement(self, sample_cv_content):
        """Measure system throughput."""
        # Create test files
        file_paths = []
        for i in range(10):
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
                tmp.write(f"CV {i}\n{sample_cv_content}")
                file_paths.append(tmp.name)
        
        try:
            # Measure throughput
            start_time = time.time()
            
            processed_count = 0
            for path in file_paths:
                result = document_parser.parse_document(path)
                if result.text is not None:
                    processed_count += 1
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Calculate throughput
            throughput = processed_count / total_time  # files per second
            
            # Should process at least 2 files per second
            assert throughput >= 2.0, f"Throughput is {throughput:.2f} files/sec, should be >= 2.0"
            assert processed_count == 10
            
        finally:
            # Cleanup
            for path in file_paths:
                try:
                    os.unlink(path)
                except OSError:
                    pass
    
    def test_resource_limits(self):
        """Test system behavior at resource limits."""
        # Test with maximum allowed file size
        max_size = document_parser.max_file_size
        
        # Create file at 90% of max size
        test_size = int(max_size * 0.9)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
            # Write large content
            content = 'x' * test_size
            tmp.write(content)
            tmp_path = tmp.name
        
        try:
            # Should handle large file within limits
            start_time = time.time()
            result = document_parser.parse_document(tmp_path)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # Should complete within reasonable time even for large files
            assert processing_time < 30.0, f"Large file processing took {processing_time:.2f}s, should be < 30.0s"
            assert result.text is not None
            
        finally:
            os.unlink(tmp_path)
