"""
Vector Store Service
FAISS-based vector database for similarity search and RAG
"""

import json
import logging
import os
import pickle
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import faiss
import numpy as np
from pydantic import BaseModel, Field

from app.core.config import settings
from app.services.embedding_service import embedding_service

logger = logging.getLogger(__name__)


class VectorDocument(BaseModel):
    """Document stored in vector database."""
    
    id: str = Field(..., description="Unique document ID")
    content: str = Field(..., description="Document content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")
    embedding: Optional[List[float]] = Field(None, description="Document embedding")


class SearchResult(BaseModel):
    """Search result from vector database."""
    
    document: VectorDocument = Field(..., description="Found document")
    score: float = Field(..., description="Similarity score")
    rank: int = Field(..., description="Result rank")


class SearchRequest(BaseModel):
    """Search request model."""
    
    query: str = Field(..., min_length=1, description="Search query")
    top_k: int = Field(default=5, ge=1, le=100, description="Number of results to return")
    score_threshold: float = Field(default=0.0, ge=0.0, le=1.0, description="Minimum similarity score")
    filter_metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata filters")


class VectorStoreError(Exception):
    """Custom exception for vector store errors."""
    
    def __init__(self, message: str, error_code: str = "VECTOR_STORE_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class VectorStoreService:
    """
    Enterprise vector store service using FAISS.
    
    Features:
    - FAISS index for fast similarity search
    - Persistent storage with metadata
    - Batch operations for efficiency
    - Index optimization and rebuilding
    - Comprehensive error handling
    """
    
    def __init__(self, index_path: Optional[str] = None):
        """Initialize vector store service."""
        self.index_path = index_path or os.path.join(settings.DATA_DIR, "vector_store")
        self.index_file = os.path.join(self.index_path, "faiss.index")
        self.metadata_file = os.path.join(self.index_path, "metadata.json")
        self.documents_file = os.path.join(self.index_path, "documents.pkl")
        
        self.index = None
        self.documents = {}  # id -> VectorDocument
        self.id_to_index = {}  # document_id -> faiss_index
        self.index_to_id = {}  # faiss_index -> document_id
        
        self.dimension = 1536  # Default for text-embedding-3-small
        self.initialized = False
        self.document_count = 0
    
    async def initialize(self, dimension: int = 1536) -> None:
        """Initialize vector store."""
        if self.initialized:
            return
        
        try:
            self.dimension = dimension
            
            # Create directory if it doesn't exist
            Path(self.index_path).mkdir(parents=True, exist_ok=True)
            
            # Load existing index or create new one
            if os.path.exists(self.index_file):
                await self._load_index()
            else:
                await self._create_new_index()
            
            self.initialized = True
            logger.info(f"Vector store initialized with {self.document_count} documents")
            
        except Exception as e:
            logger.error(f"Failed to initialize vector store: {e}")
            raise VectorStoreError(
                f"Vector store initialization failed: {str(e)}",
                error_code="INITIALIZATION_FAILED"
            )
    
    async def _create_new_index(self) -> None:
        """Create a new FAISS index."""
        # Use IndexFlatIP for cosine similarity (after L2 normalization)
        self.index = faiss.IndexFlatIP(self.dimension)
        self.documents = {}
        self.id_to_index = {}
        self.index_to_id = {}
        self.document_count = 0
        
        logger.info(f"Created new FAISS index with dimension {self.dimension}")
    
    async def _load_index(self) -> None:
        """Load existing FAISS index and metadata."""
        try:
            # Load FAISS index
            self.index = faiss.read_index(self.index_file)
            logger.info(f"Loaded FAISS index from {self.index_file}")
            
            # Load documents
            if os.path.exists(self.documents_file):
                with open(self.documents_file, 'rb') as f:
                    self.documents = pickle.load(f)
            
            # Load metadata mappings
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r') as f:
                    metadata = json.load(f)
                    self.id_to_index = metadata.get("id_to_index", {})
                    self.index_to_id = metadata.get("index_to_id", {})
                    self.document_count = metadata.get("document_count", 0)
            
            # Verify consistency
            if len(self.documents) != self.document_count:
                logger.warning(f"Document count mismatch: {len(self.documents)} vs {self.document_count}")
                self.document_count = len(self.documents)
            
        except Exception as e:
            logger.error(f"Failed to load vector store: {e}")
            # Create new index if loading fails
            await self._create_new_index()
    
    async def add_document(self, document: VectorDocument) -> None:
        """
        Add a single document to the vector store.
        
        Args:
            document: Document to add
        """
        await self.add_documents([document])
    
    async def add_documents(self, documents: List[VectorDocument]) -> None:
        """
        Add multiple documents to the vector store.
        
        Args:
            documents: List of documents to add
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # Generate embeddings for documents without them
            docs_to_embed = []
            texts_to_embed = []
            
            for doc in documents:
                if doc.embedding is None:
                    docs_to_embed.append(doc)
                    texts_to_embed.append(doc.content)
            
            # Generate embeddings in batch
            if texts_to_embed:
                from app.services.embedding_service import EmbeddingRequest

                embedding_request = EmbeddingRequest(texts=texts_to_embed)
                embedding_response = await embedding_service.generate_embeddings(embedding_request)
                
                # Assign embeddings to documents
                for i, doc in enumerate(docs_to_embed):
                    doc.embedding = embedding_response.embeddings[i]
            
            # Prepare embeddings for FAISS
            embeddings = []
            valid_documents = []
            
            for doc in documents:
                if doc.embedding and len(doc.embedding) == self.dimension:
                    # Normalize embedding for cosine similarity
                    embedding = np.array(doc.embedding, dtype=np.float32)
                    embedding = embedding / np.linalg.norm(embedding)
                    embeddings.append(embedding)
                    valid_documents.append(doc)
                else:
                    logger.warning(f"Skipping document {doc.id} due to invalid embedding")
            
            if not embeddings:
                logger.warning("No valid embeddings to add")
                return
            
            # Add to FAISS index
            embeddings_array = np.array(embeddings)
            start_index = self.index.ntotal
            self.index.add(embeddings_array)
            
            # Update mappings and store documents
            for i, doc in enumerate(valid_documents):
                faiss_index = start_index + i
                
                # Remove old document if it exists
                if doc.id in self.id_to_index:
                    await self._remove_document_by_id(doc.id)
                
                # Add new mappings
                self.id_to_index[doc.id] = faiss_index
                self.index_to_id[str(faiss_index)] = doc.id
                self.documents[doc.id] = doc
            
            self.document_count = len(self.documents)
            
            # Save to disk
            await self._save_index()
            
            logger.info(f"Added {len(valid_documents)} documents to vector store")
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            raise VectorStoreError(
                f"Failed to add documents: {str(e)}",
                error_code="ADD_DOCUMENTS_FAILED"
            )
    
    async def search(self, request: SearchRequest) -> List[SearchResult]:
        """
        Search for similar documents.
        
        Args:
            request: Search request
            
        Returns:
            List[SearchResult]: Search results
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # Generate embedding for query
            query_embedding = await embedding_service.generate_single_embedding(request.query)
            
            # Normalize for cosine similarity
            query_vector = np.array(query_embedding, dtype=np.float32)
            query_vector = query_vector / np.linalg.norm(query_vector)
            query_vector = query_vector.reshape(1, -1)
            
            # Search in FAISS
            scores, indices = self.index.search(query_vector, request.top_k)
            
            # Convert results
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx == -1:  # FAISS returns -1 for invalid results
                    continue
                
                if score < request.score_threshold:
                    continue
                
                doc_id = self.index_to_id.get(str(idx))
                if not doc_id or doc_id not in self.documents:
                    logger.warning(f"Document not found for index {idx}")
                    continue
                
                document = self.documents[doc_id]
                
                # Apply metadata filters
                if request.filter_metadata:
                    if not self._matches_filters(document.metadata, request.filter_metadata):
                        continue
                
                results.append(SearchResult(
                    document=document,
                    score=float(score),
                    rank=i + 1
                ))
            
            logger.info(f"Search for '{request.query}' returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise VectorStoreError(
                f"Search failed: {str(e)}",
                error_code="SEARCH_FAILED"
            )
    
    async def get_document(self, document_id: str) -> Optional[VectorDocument]:
        """Get document by ID."""
        return self.documents.get(document_id)
    
    async def remove_document(self, document_id: str) -> bool:
        """Remove document by ID."""
        return await self._remove_document_by_id(document_id)
    
    async def _remove_document_by_id(self, document_id: str) -> bool:
        """Remove document and update mappings."""
        if document_id not in self.id_to_index:
            return False
        
        # Note: FAISS doesn't support efficient removal of individual vectors
        # For now, we just remove from our mappings
        # In production, you might want to rebuild the index periodically
        
        faiss_index = self.id_to_index[document_id]
        del self.id_to_index[document_id]
        del self.index_to_id[str(faiss_index)]
        del self.documents[document_id]
        
        self.document_count = len(self.documents)
        await self._save_index()
        
        return True
    
    def _matches_filters(self, metadata: Dict[str, Any], filters: Dict[str, Any]) -> bool:
        """Check if document metadata matches filters."""
        for key, value in filters.items():
            if key not in metadata:
                return False
            
            if isinstance(value, list):
                if metadata[key] not in value:
                    return False
            else:
                if metadata[key] != value:
                    return False
        
        return True
    
    async def _save_index(self) -> None:
        """Save FAISS index and metadata to disk."""
        try:
            # Save FAISS index
            faiss.write_index(self.index, self.index_file)
            
            # Save documents
            with open(self.documents_file, 'wb') as f:
                pickle.dump(self.documents, f)
            
            # Save metadata
            metadata = {
                "id_to_index": self.id_to_index,
                "index_to_id": self.index_to_id,
                "document_count": self.document_count,
                "dimension": self.dimension,
                "last_updated": time.time()
            }
            
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
        except Exception as e:
            logger.error(f"Failed to save vector store: {e}")
            raise VectorStoreError(
                f"Failed to save vector store: {str(e)}",
                error_code="SAVE_FAILED"
            )
    
    async def rebuild_index(self) -> None:
        """Rebuild FAISS index from documents."""
        if not self.documents:
            return
        
        logger.info("Rebuilding FAISS index...")
        
        # Create new index
        self.index = faiss.IndexFlatIP(self.dimension)
        
        # Collect all embeddings
        embeddings = []
        doc_ids = []
        
        for doc_id, doc in self.documents.items():
            if doc.embedding and len(doc.embedding) == self.dimension:
                embedding = np.array(doc.embedding, dtype=np.float32)
                embedding = embedding / np.linalg.norm(embedding)
                embeddings.append(embedding)
                doc_ids.append(doc_id)
        
        if embeddings:
            # Add to index
            embeddings_array = np.array(embeddings)
            self.index.add(embeddings_array)
            
            # Rebuild mappings
            self.id_to_index = {}
            self.index_to_id = {}
            
            for i, doc_id in enumerate(doc_ids):
                self.id_to_index[doc_id] = i
                self.index_to_id[str(i)] = doc_id
        
        await self._save_index()
        logger.info(f"Index rebuilt with {len(embeddings)} documents")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get vector store statistics."""
        return {
            "initialized": self.initialized,
            "document_count": self.document_count,
            "index_size": self.index.ntotal if self.index else 0,
            "dimension": self.dimension,
            "index_path": self.index_path,
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        try:
            if not self.initialized:
                await self.initialize()
            
            # Test search
            if self.document_count > 0:
                request = SearchRequest(query="test", top_k=1)
                start_time = time.time()
                results = await self.search(request)
                search_time = (time.time() - start_time) * 1000
                
                return {
                    "status": "healthy",
                    "document_count": self.document_count,
                    "search_time_ms": search_time,
                }
            else:
                return {
                    "status": "healthy",
                    "document_count": 0,
                    "message": "No documents in index",
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
            }


# Global service instance
vector_store = VectorStoreService()
