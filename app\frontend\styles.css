/* Custom styles for ImpactCV */

/* Mobile-friendly styles */
@media (max-width: 640px) {
    .mobile-friendly {
        display: block;
    }
    
    .container {
        padding: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    button {
        width: 100%;
        margin-top: 1rem;
    }
}

/* Tablet-friendly styles */
@media (min-width: 641px) and (max-width: 1024px) {
    .tablet-friendly {
        display: block;
    }
    
    .container {
        padding: 2rem;
    }
}

/* Desktop-friendly styles */
@media (min-width: 1025px) {
    .desktop-friendly {
        display: block;
    }
    
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 3rem;
    }
}

/* Form styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #D1D5DB;
    border-radius: 0.375rem;
    font-size: 1rem;
    transition: border-color 0.15s ease-in-out;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4F46E5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Button styles */
button {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Results section */
#results {
    margin-top: 2rem;
    padding: 1.5rem;
    border-radius: 0.5rem;
    background-color: #F9FAFB;
    border: 1px solid #E5E7EB;
}

#results h3 {
    color: #111827;
    margin-bottom: 1.5rem;
}

#results h4 {
    color: #374151;
    margin-bottom: 0.5rem;
}

#results p {
    color: #4B5563;
    line-height: 1.5;
}

/* Loading state */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1.5rem;
    height: 1.5rem;
    margin: -0.75rem 0 0 -0.75rem;
    border: 2px solid #E5E7EB;
    border-top-color: #4F46E5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error message */
.error-message {
    color: #DC2626;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Success message */
.success-message {
    color: #059669;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
:focus {
    outline: 2px solid #4F46E5;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .no-print {
        display: none;
    }
    
    #results {
        border: none;
        padding: 0;
    }
    
    body {
        font-size: 12pt;
    }
}

/* Base styles */
:root {
    --primary-color: #4F46E5;
    --primary-hover: #4338CA;
    --text-color: #1F2937;
    --light-text: #6B7280;
    --background: #F9FAFB;
    --white: #FFFFFF;
    --border: #E5E7EB;
    --error: #DC2626;
    --success: #059669;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Roboto", Helvetica, Arial, sans-serif;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--background);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.navbar {
    background-color: var(--white);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.hero {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: var(--white);
    padding: 4rem 0;
    text-align: center;
}

.form-section {
    padding: 4rem 0;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

input, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: 0.375rem;
    font-size: 1rem;
    transition: border-color 0.2s;
}

input:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.results-section {
    padding: 2rem 0;
}

.hidden {
    display: none;
} 