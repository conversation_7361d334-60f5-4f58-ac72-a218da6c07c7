// Gesture service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class GestureService {
    constructor() {
        this.gestures = new Map();
        this.subscribers = new Set();
        this.touchStartTime = null;
        this.touchStartX = null;
        this.touchStartY = null;
        this.touchEndX = null;
        this.touchEndY = null;
        this.touchStartDistance = null;
        this.touchStartAngle = null;
        this.initialize();
    }

    /**
     * Initialize the gesture service
     */
    initialize() {
        document.addEventListener('touchstart', this.handleTouchStart.bind(this));
        document.addEventListener('touchmove', this.handleTouchMove.bind(this));
        document.addEventListener('touchend', this.handleTouchEnd.bind(this));
        document.addEventListener('touchcancel', this.handleTouchCancel.bind(this));
    }

    /**
     * Subscribe to gesture events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of gesture events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in gesture subscriber:', error);
            }
        });
    }

    /**
     * Register a gesture
     * @param {string} id - The gesture ID
     * @param {Object} options - The gesture options
     */
    register(id, options = {}) {
        this.gestures.set(id, {
            ...options,
            element: document.getElementById(id),
        });
    }

    /**
     * Handle touch start events
     * @param {TouchEvent} event - The touch event
     */
    handleTouchStart(event) {
        return measurePerformance('gesture_touchstart', () => {
            const touch = event.touches[0];
            this.touchStartTime = Date.now();
            this.touchStartX = touch.clientX;
            this.touchStartY = touch.clientY;

            if (event.touches.length === 2) {
                const touch1 = event.touches[0];
                const touch2 = event.touches[1];
                this.touchStartDistance = this.getDistance(touch1, touch2);
                this.touchStartAngle = this.getAngle(touch1, touch2);
            }

            this.notifySubscribers('touchstart', { event });
        });
    }

    /**
     * Handle touch move events
     * @param {TouchEvent} event - The touch event
     */
    handleTouchMove(event) {
        return measurePerformance('gesture_touchmove', () => {
            const touch = event.touches[0];
            this.touchEndX = touch.clientX;
            this.touchEndY = touch.clientY;

            if (event.touches.length === 2) {
                const touch1 = event.touches[0];
                const touch2 = event.touches[1];
                const distance = this.getDistance(touch1, touch2);
                const angle = this.getAngle(touch1, touch2);

                // Handle pinch gesture
                if (this.touchStartDistance !== null) {
                    const scale = distance / this.touchStartDistance;
                    this.notifySubscribers('pinch', { scale, event });
                }

                // Handle rotate gesture
                if (this.touchStartAngle !== null) {
                    const rotation = angle - this.touchStartAngle;
                    this.notifySubscribers('rotate', { rotation, event });
                }
            }

            this.notifySubscribers('touchmove', { event });
        });
    }

    /**
     * Handle touch end events
     * @param {TouchEvent} event - The touch event
     */
    handleTouchEnd(event) {
        return measurePerformance('gesture_touchend', () => {
            if (this.touchStartTime === null) {
                return;
            }

            const touch = event.changedTouches[0];
            this.touchEndX = touch.clientX;
            this.touchEndY = touch.clientY;

            const duration = Date.now() - this.touchStartTime;
            const distance = this.getDistance(
                { clientX: this.touchStartX, clientY: this.touchStartY },
                { clientX: this.touchEndX, clientY: this.touchEndY }
            );

            // Handle swipe gesture
            if (duration < 300 && distance > 50) {
                const direction = this.getSwipeDirection();
                this.notifySubscribers('swipe', { direction, event });
            }

            // Handle tap gesture
            if (duration < 300 && distance < 10) {
                this.notifySubscribers('tap', { event });
            }

            // Reset touch state
            this.touchStartTime = null;
            this.touchStartX = null;
            this.touchStartY = null;
            this.touchEndX = null;
            this.touchEndY = null;
            this.touchStartDistance = null;
            this.touchStartAngle = null;

            this.notifySubscribers('touchend', { event });
        });
    }

    /**
     * Handle touch cancel events
     * @param {TouchEvent} event - The touch event
     */
    handleTouchCancel(event) {
        return measurePerformance('gesture_touchcancel', () => {
            // Reset touch state
            this.touchStartTime = null;
            this.touchStartX = null;
            this.touchStartY = null;
            this.touchEndX = null;
            this.touchEndY = null;
            this.touchStartDistance = null;
            this.touchStartAngle = null;

            this.notifySubscribers('touchcancel', { event });
        });
    }

    /**
     * Get the distance between two touch points
     * @param {Touch} touch1 - The first touch point
     * @param {Touch} touch2 - The second touch point
     * @returns {number} The distance between the touch points
     */
    getDistance(touch1, touch2) {
        const dx = touch2.clientX - touch1.clientX;
        const dy = touch2.clientY - touch1.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * Get the angle between two touch points
     * @param {Touch} touch1 - The first touch point
     * @param {Touch} touch2 - The second touch point
     * @returns {number} The angle between the touch points
     */
    getAngle(touch1, touch2) {
        return Math.atan2(
            touch2.clientY - touch1.clientY,
            touch2.clientX - touch1.clientX
        ) * 180 / Math.PI;
    }

    /**
     * Get the swipe direction
     * @returns {string} The swipe direction
     */
    getSwipeDirection() {
        const dx = this.touchEndX - this.touchStartX;
        const dy = this.touchEndY - this.touchStartY;
        const angle = Math.atan2(dy, dx) * 180 / Math.PI;

        if (angle > -45 && angle <= 45) {
            return 'right';
        } else if (angle > 45 && angle <= 135) {
            return 'down';
        } else if (angle > 135 || angle <= -135) {
            return 'left';
        } else {
            return 'up';
        }
    }

    /**
     * Get gesture data
     * @param {string} id - The gesture ID
     * @returns {Object} The gesture data
     */
    getGestureData(id) {
        return this.gestures.get(id);
    }

    /**
     * Update gesture data
     * @param {string} id - The gesture ID
     * @param {Object} data - The new gesture data
     */
    updateGestureData(id, data) {
        const gesture = this.gestures.get(id);
        if (gesture) {
            Object.assign(gesture, data);
        }
    }
}

// Create and export a singleton instance
const gestureService = new GestureService();
export default gestureService; 