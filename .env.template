# =============================================================================
# IMPACTCV ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# NEVER commit .env to version control - it contains sensitive information

# =============================================================================
# OPENAI API CONFIGURATION (ONLY PAID SERVICE REQUIRED)
# =============================================================================
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL database credentials
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_USER=impactcv_user
POSTGRES_DB=impactcv

# Database connection URL (auto-generated from above)
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgresql:5432/${POSTGRES_DB}

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Redis cache and session store
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT secret key - MUST be long and secure for production
# Generate with: openssl rand -hex 32
JWT_SECRET_KEY=your_very_long_and_secure_jwt_secret_key_at_least_32_characters

# JWT algorithm and expiration
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment setting
ENVIRONMENT=development

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=ImpactCV
PROJECT_VERSION=1.0.0

# CORS settings for development
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000","http://localhost:8080"]

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Grafana admin credentials
GRAFANA_USER=admin
GRAFANA_PASSWORD=your_secure_grafana_password_here

# Prometheus configuration
PROMETHEUS_RETENTION_TIME=200h

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# Maximum file size for uploads (in bytes)
MAX_FILE_SIZE=10485760  # 10MB
MAX_FILES_PER_REQUEST=5

# Allowed file types
ALLOWED_FILE_TYPES=["pdf","docx","doc"]

# Upload paths
UPLOAD_PATH=/app/uploads
CV_OUTPUT_PATH=/app/storage

# =============================================================================
# RAG PIPELINE CONFIGURATION
# =============================================================================
# OpenAI models for embeddings and generation
EMBEDDING_MODEL=text-embedding-3-large
LLM_MODEL=gpt-4o

# FAISS configuration
FAISS_INDEX_PATH=/app/faiss_index
FAISS_INDEX_TYPE=IndexFlatIP  # Inner Product for cosine similarity

# RAG parameters
RAG_CHUNK_SIZE=1000
RAG_CHUNK_OVERLAP=200
RAG_TOP_K_RESULTS=5
RAG_SIMILARITY_THRESHOLD=0.7

# =============================================================================
# SECURITY HEADERS & POLICIES
# =============================================================================
# CORS policy
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET","POST","PUT","DELETE","OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# Security headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_POLICY="default-src 'self'"

# =============================================================================
# RATE LIMITING
# =============================================================================
# API rate limits
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=1000
CV_GENERATION_LIMIT_PER_HOUR=10

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Structured logging
LOG_FORMAT=json
LOG_TIMESTAMP_FORMAT=iso

# Log retention
LOG_RETENTION_DAYS=30

# =============================================================================
# GDPR & PRIVACY CONFIGURATION
# =============================================================================
# Data retention policies
USER_DATA_RETENTION_DAYS=365
CV_DATA_RETENTION_DAYS=90
LOG_DATA_RETENTION_DAYS=30

# PII detection and anonymization
PII_DETECTION_ENABLED=true
PII_ANONYMIZATION_ENABLED=true

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Connection pools
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Redis connection pool
REDIS_POOL_SIZE=10
REDIS_POOL_TIMEOUT=5

# HTTP timeouts
HTTP_TIMEOUT=30
OPENAI_TIMEOUT=60

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Development mode settings
DEBUG=true
RELOAD=true

# Testing configuration
TESTING=false
TEST_DATABASE_URL=********************************************************/impactcv_test

# =============================================================================
# SSL/TLS CONFIGURATION (LOCAL DEVELOPMENT)
# =============================================================================
# SSL certificate paths for local HTTPS
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Generate self-signed certificates with:
# openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Database backup settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=7
BACKUP_PATH=/app/backups

# =============================================================================
# HEALTH CHECK CONFIGURATION
# =============================================================================
# Health check intervals
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# =============================================================================
# MICROSERVICES CONFIGURATION
# =============================================================================
# Service URLs (for inter-service communication)
AUTH_SERVICE_URL=http://auth-service:8000
CV_GENERATION_SERVICE_URL=http://cv-generation-service:8000
RAG_SERVICE_URL=http://rag-service:8000
DATA_PROCESSING_SERVICE_URL=http://data-processing-service:8000

# =============================================================================
# EXAMPLE VALUES FOR QUICK SETUP
# =============================================================================
# Uncomment and modify these for quick local development setup:

# OPENAI_API_KEY=sk-your-actual-openai-key-here
# POSTGRES_PASSWORD=dev_postgres_123
# REDIS_PASSWORD=dev_redis_123
# JWT_SECRET_KEY=dev_jwt_secret_key_very_long_and_secure_for_development_only
# GRAFANA_PASSWORD=admin123

# =============================================================================
# PRODUCTION NOTES
# =============================================================================
# For production deployment:
# 1. Use strong, unique passwords for all services
# 2. Generate secure JWT secret key (32+ characters)
# 3. Enable SSL/TLS with proper certificates
# 4. Configure proper CORS origins
# 5. Set appropriate rate limits
# 6. Enable all security headers
# 7. Configure proper backup and monitoring
# 8. Review and adjust all timeout values
# 9. Set up proper log aggregation
# 10. Configure alerting and notifications
