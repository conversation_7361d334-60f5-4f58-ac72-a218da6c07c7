"""
Alembic Environment Configuration
Database migration environment with security and logging
"""

import asyncio
import os
from logging.config import fileConfig

from alembic import context
from sqlalchemy import engine_from_config, pool
from sqlalchemy.ext.asyncio import AsyncEngine

from app.core.config import settings
from app.models.base import Base

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def get_database_url() -> str:
    """Get database URL from environment or config."""
    # Try to get from environment variable first
    db_url = os.getenv("DATABASE_URL")
    if db_url:
        return db_url
    
    # Fall back to settings
    return settings.get_database_url()


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = get_database_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        # Security: Include schema in migration context
        include_schemas=True,
        # Performance: Compare types for better migrations
        compare_type=True,
        # Include indexes in autogenerate
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection):
    """Run migrations with the given connection."""
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        # Security: Include schema in migration context
        include_schemas=True,
        # Performance: Compare types for better migrations
        compare_type=True,
        # Include indexes and server defaults in autogenerate
        compare_server_default=True,
        # Render item for better migration generation
        render_as_batch=False,
        # Transaction per migration for better error handling
        transaction_per_migration=True,
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations():
    """Run migrations in async mode."""
    from sqlalchemy.ext.asyncio import create_async_engine
    
    # Get database URL and convert to async
    database_url = get_database_url()
    if database_url.startswith("postgresql://"):
        database_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
    
    connectable = create_async_engine(
        database_url,
        poolclass=pool.NullPool,
        # Migration-specific settings
        echo=False,
        future=True,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Check if we're running in async mode
    if context.is_offline_mode():
        run_migrations_offline()
        return
    
    # Try to run async migrations first
    try:
        asyncio.run(run_async_migrations())
        return
    except Exception as e:
        print(f"Async migration failed, falling back to sync: {e}")
    
    # Fall back to synchronous migrations
    database_url = get_database_url()
    
    # Override the sqlalchemy.url in the alembic config
    config.set_main_option("sqlalchemy.url", database_url)
    
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
        # Security settings
        connect_args={
            "application_name": "ImpactCV-Migration",
            "connect_timeout": 30,
        }
    )

    with connectable.connect() as connection:
        do_run_migrations(connection)


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
