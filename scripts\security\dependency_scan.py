#!/usr/bin/env python3
"""
ImpactCV Dependency Vulnerability Scanner
Comprehensive dependency security scanning and analysis
"""

import argparse
import json
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class DependencyScanner:
    """Comprehensive dependency vulnerability scanner."""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.results_dir = self.project_root / "security-reports"
        self.results_dir.mkdir(exist_ok=True)
        
        # Scan results storage
        self.scan_results = {
            "scan_metadata": {
                "timestamp": datetime.now().isoformat(),
                "scan_type": "dependency_vulnerability_scan",
                "project_root": str(self.project_root)
            },
            "tools_results": {},
            "vulnerability_summary": {
                "total_vulnerabilities": 0,
                "critical": 0,
                "high": 0,
                "medium": 0,
                "low": 0
            },
            "recommendations": []
        }
    
    def check_tool_availability(self, tool: str) -> bool:
        """Check if a security tool is available."""
        try:
            subprocess.run([tool, "--version"], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def install_missing_tools(self) -> bool:
        """Install missing security tools."""
        tools_to_install = []
        
        if not self.check_tool_availability("safety"):
            tools_to_install.append("safety")
        
        if not self.check_tool_availability("pip-audit"):
            tools_to_install.append("pip-audit")
        
        if tools_to_install:
            print(f"📦 Installing missing tools: {', '.join(tools_to_install)}")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install"
                ] + tools_to_install, check=True)
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install tools: {e}")
                return False
        
        return True
    
    def run_safety_scan(self) -> Tuple[bool, Dict]:
        """Run Safety vulnerability scan."""
        print("🔍 Running Safety dependency vulnerability scan...")
        
        output_file = self.results_dir / "safety-report.json"
        
        cmd = [
            "safety", "check",
            "--json",
            "--output", str(output_file)
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            # Safety returns non-zero when vulnerabilities are found
            success = result.returncode in [0, 64]  # 64 = vulnerabilities found
            
            if output_file.exists():
                with open(output_file, 'r') as f:
                    report_data = json.load(f)
            else:
                report_data = []
            
            # Process results
            vulnerabilities = report_data if isinstance(report_data, list) else []
            
            scan_result = {
                "tool": "safety",
                "vulnerabilities_found": len(vulnerabilities),
                "vulnerabilities": vulnerabilities,
                "exit_code": result.returncode,
                "success": success
            }
            
            if result.stderr:
                scan_result["stderr"] = result.stderr
            
            self.scan_results["tools_results"]["safety"] = scan_result
            self.scan_results["vulnerability_summary"]["total_vulnerabilities"] += len(vulnerabilities)
            self.scan_results["vulnerability_summary"]["medium"] += len(vulnerabilities)
            
            print(f"✅ Safety scan completed. Found {len(vulnerabilities)} vulnerabilities.")
            return success, scan_result
            
        except FileNotFoundError:
            print("❌ Safety not found. Install with: pip install safety")
            return False, {}
        except Exception as e:
            print(f"❌ Safety scan failed: {e}")
            return False, {}
    
    def run_pip_audit_scan(self) -> Tuple[bool, Dict]:
        """Run pip-audit vulnerability scan."""
        print("🔍 Running pip-audit vulnerability scan...")
        
        output_file = self.results_dir / "pip-audit-report.json"
        
        cmd = [
            "pip-audit",
            "--format=json",
            "--output", str(output_file)
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            success = result.returncode == 0
            
            if output_file.exists():
                with open(output_file, 'r') as f:
                    report_data = json.load(f)
            else:
                report_data = {"vulnerabilities": []}
            
            # Process results
            vulnerabilities = report_data.get("vulnerabilities", [])
            
            scan_result = {
                "tool": "pip-audit",
                "vulnerabilities_found": len(vulnerabilities),
                "vulnerabilities": vulnerabilities,
                "exit_code": result.returncode,
                "success": success
            }
            
            if result.stderr:
                scan_result["stderr"] = result.stderr
            
            self.scan_results["tools_results"]["pip_audit"] = scan_result
            self.scan_results["vulnerability_summary"]["total_vulnerabilities"] += len(vulnerabilities)
            self.scan_results["vulnerability_summary"]["high"] += len(vulnerabilities)
            
            print(f"✅ Pip-audit scan completed. Found {len(vulnerabilities)} vulnerabilities.")
            return success, scan_result
            
        except FileNotFoundError:
            print("❌ Pip-audit not found. Install with: pip install pip-audit")
            return False, {}
        except Exception as e:
            print(f"❌ Pip-audit scan failed: {e}")
            return False, {}
    
    def analyze_dependencies(self) -> Dict:
        """Analyze dependency tree and outdated packages."""
        print("📊 Analyzing dependency tree...")
        
        analysis = {
            "total_dependencies": 0,
            "direct_dependencies": 0,
            "outdated_packages": [],
            "dependency_tree": {}
        }
        
        try:
            # Install pipdeptree if not available
            if not self.check_tool_availability("pipdeptree"):
                subprocess.run([sys.executable, "-m", "pip", "install", "pipdeptree"], check=True)
            
            # Get dependency tree
            result = subprocess.run(
                ["pipdeptree", "--json-tree"],
                capture_output=True,
                text=True,
                check=True
            )
            
            dependency_tree = json.loads(result.stdout)
            analysis["dependency_tree"] = dependency_tree
            analysis["total_dependencies"] = len(dependency_tree)
            analysis["direct_dependencies"] = len([
                dep for dep in dependency_tree 
                if dep.get("package", {}).get("installed_version")
            ])
            
            # Check for outdated packages
            result = subprocess.run(
                ["pip", "list", "--outdated", "--format=json"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                outdated = json.loads(result.stdout)
                analysis["outdated_packages"] = outdated
            
            print(f"📊 Dependency analysis completed:")
            print(f"  Total dependencies: {analysis['total_dependencies']}")
            print(f"  Direct dependencies: {analysis['direct_dependencies']}")
            print(f"  Outdated packages: {len(analysis['outdated_packages'])}")
            
        except Exception as e:
            print(f"⚠️ Dependency analysis failed: {e}")
        
        return analysis
    
    def check_license_compliance(self) -> Dict:
        """Check license compliance of dependencies."""
        print("📄 Checking license compliance...")
        
        compliance = {
            "approved_licenses": 0,
            "restricted_licenses": 0,
            "unknown_licenses": 0,
            "license_details": []
        }
        
        # Define license categories
        APPROVED_LICENSES = {
            'MIT', 'Apache Software License', 'Apache 2.0', 'Apache License 2.0',
            'BSD License', 'BSD', '3-Clause BSD License', '2-Clause BSD License',
            'ISC License', 'Python Software Foundation License', 
            'Mozilla Public License 2.0 (MPL 2.0)', 'MIT License'
        }
        
        RESTRICTED_LICENSES = {
            'GNU General Public License v3 (GPLv3)', 'GNU General Public License v2 (GPLv2)',
            'GNU Affero General Public License v3', 'AGPL-3.0'
        }
        
        try:
            # Install pip-licenses if not available
            if not self.check_tool_availability("pip-licenses"):
                subprocess.run([sys.executable, "-m", "pip", "install", "pip-licenses"], check=True)
            
            # Get license information
            result = subprocess.run(
                ["pip-licenses", "--format=json"],
                capture_output=True,
                text=True,
                check=True
            )
            
            licenses = json.loads(result.stdout)
            
            for pkg in licenses:
                license_name = pkg.get('License', 'Unknown')
                
                if license_name in APPROVED_LICENSES:
                    compliance["approved_licenses"] += 1
                    status = "approved"
                elif license_name in RESTRICTED_LICENSES:
                    compliance["restricted_licenses"] += 1
                    status = "restricted"
                else:
                    compliance["unknown_licenses"] += 1
                    status = "unknown"
                
                compliance["license_details"].append({
                    "package": pkg.get('Name', 'Unknown'),
                    "license": license_name,
                    "status": status
                })
            
            print(f"📄 License compliance check completed:")
            print(f"  Approved licenses: {compliance['approved_licenses']}")
            print(f"  Restricted licenses: {compliance['restricted_licenses']}")
            print(f"  Unknown licenses: {compliance['unknown_licenses']}")
            
        except Exception as e:
            print(f"⚠️ License compliance check failed: {e}")
        
        return compliance
    
    def generate_recommendations(self) -> List[str]:
        """Generate security recommendations based on scan results."""
        recommendations = []
        
        total_vulns = self.scan_results["vulnerability_summary"]["total_vulnerabilities"]
        
        if total_vulns == 0:
            recommendations.append("✅ No vulnerabilities found in dependencies")
            recommendations.append("Continue monitoring dependencies with regular scans")
        else:
            recommendations.append(f"🚨 {total_vulns} vulnerabilities found - immediate action required")
            recommendations.append("Update vulnerable packages to latest secure versions")
            recommendations.append("Review security advisories for affected packages")
        
        # Tool-specific recommendations
        safety_result = self.scan_results["tools_results"].get("safety", {})
        if safety_result.get("vulnerabilities_found", 0) > 0:
            recommendations.append(f"Safety found {safety_result['vulnerabilities_found']} known vulnerabilities")
        
        pip_audit_result = self.scan_results["tools_results"].get("pip_audit", {})
        if pip_audit_result.get("vulnerabilities_found", 0) > 0:
            recommendations.append(f"Pip-audit found {pip_audit_result['vulnerabilities_found']} security issues")
        
        # General security recommendations
        recommendations.extend([
            "Consider using dependency pinning for critical packages",
            "Set up automated dependency updates with testing",
            "Monitor security advisories for used packages",
            "Implement dependency scanning in CI/CD pipeline"
        ])
        
        return recommendations
    
    def run_comprehensive_scan(self) -> bool:
        """Run comprehensive dependency vulnerability scan."""
        print("🚀 Starting comprehensive dependency vulnerability scan...")
        print(f"📁 Project root: {self.project_root}")
        print(f"📊 Results directory: {self.results_dir}")
        
        # Install missing tools
        if not self.install_missing_tools():
            print("❌ Failed to install required tools")
            return False
        
        all_success = True
        
        # Run vulnerability scans
        safety_success, _ = self.run_safety_scan()
        pip_audit_success, _ = self.run_pip_audit_scan()
        
        all_success = safety_success and pip_audit_success
        
        # Run additional analysis
        dependency_analysis = self.analyze_dependencies()
        license_compliance = self.check_license_compliance()
        
        # Add analysis results to scan results
        self.scan_results["dependency_analysis"] = dependency_analysis
        self.scan_results["license_compliance"] = license_compliance
        
        # Generate recommendations
        self.scan_results["recommendations"] = self.generate_recommendations()
        
        # Calculate risk assessment
        total_vulns = self.scan_results["vulnerability_summary"]["total_vulnerabilities"]
        if total_vulns == 0:
            risk_level = "LOW"
        elif total_vulns <= 5:
            risk_level = "MEDIUM"
        elif total_vulns <= 15:
            risk_level = "HIGH"
        else:
            risk_level = "CRITICAL"
        
        self.scan_results["risk_assessment"] = {
            "overall_risk_level": risk_level,
            "risk_score": total_vulns,
            "risk_factors": [
                f"Total vulnerabilities: {total_vulns}",
                f"Tools used: {len(self.scan_results['tools_results'])}",
                f"Outdated packages: {len(dependency_analysis.get('outdated_packages', []))}"
            ]
        }
        
        # Save comprehensive report
        report_file = self.results_dir / "dependency-vulnerability-report.json"
        with open(report_file, 'w') as f:
            json.dump(self.scan_results, f, indent=2)
        
        # Print summary
        self._print_summary()
        
        print(f"\n📊 Comprehensive report saved: {report_file}")
        
        return all_success
    
    def _print_summary(self):
        """Print scan summary to console."""
        print("\n" + "="*70)
        print("🔍 DEPENDENCY VULNERABILITY SCAN SUMMARY")
        print("="*70)
        
        summary = self.scan_results["vulnerability_summary"]
        risk = self.scan_results["risk_assessment"]
        
        print(f"📊 Total Vulnerabilities: {summary['total_vulnerabilities']}")
        print(f"📊 Risk Level: {risk['overall_risk_level']}")
        print(f"📊 Risk Score: {risk['risk_score']}")
        
        # Tool results
        print(f"\n🛠️ TOOL RESULTS:")
        for tool_name, tool_result in self.scan_results["tools_results"].items():
            status = "✅" if tool_result.get("success", False) else "❌"
            vulns = tool_result.get("vulnerabilities_found", 0)
            print(f"  {status} {tool_name.title()}: {vulns} vulnerabilities")
        
        # Dependencies info
        dep_analysis = self.scan_results.get("dependency_analysis", {})
        if dep_analysis:
            print(f"\n📦 DEPENDENCY ANALYSIS:")
            print(f"  Total Dependencies: {dep_analysis.get('total_dependencies', 0)}")
            print(f"  Direct Dependencies: {dep_analysis.get('direct_dependencies', 0)}")
            print(f"  Outdated Packages: {len(dep_analysis.get('outdated_packages', []))}")
        
        # License compliance
        license_info = self.scan_results.get("license_compliance", {})
        if license_info:
            print(f"\n📄 LICENSE COMPLIANCE:")
            print(f"  Approved Licenses: {license_info.get('approved_licenses', 0)}")
            print(f"  Restricted Licenses: {license_info.get('restricted_licenses', 0)}")
            print(f"  Unknown Licenses: {license_info.get('unknown_licenses', 0)}")
        
        # Recommendations
        print(f"\n🎯 RECOMMENDATIONS:")
        for i, rec in enumerate(self.scan_results["recommendations"][:5], 1):
            print(f"  {i}. {rec}")
        
        print("="*70)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="ImpactCV Dependency Vulnerability Scanner")
    parser.add_argument(
        "--project-root",
        default=".",
        help="Project root directory (default: current directory)"
    )
    parser.add_argument(
        "--fail-on-vulnerabilities",
        action="store_true",
        help="Exit with non-zero code if vulnerabilities are found"
    )
    
    args = parser.parse_args()
    
    scanner = DependencyScanner(args.project_root)
    success = scanner.run_comprehensive_scan()
    
    if args.fail_on_vulnerabilities:
        total_vulns = scanner.scan_results["vulnerability_summary"]["total_vulnerabilities"]
        if total_vulns > 0:
            print(f"\n❌ Exiting with error due to {total_vulns} vulnerabilities found")
            sys.exit(1)
    
    if not success:
        print("\n❌ Some scans failed - check the output above")
        sys.exit(1)
    
    print("\n✅ Dependency vulnerability scan completed successfully")
    sys.exit(0)


if __name__ == "__main__":
    main()
