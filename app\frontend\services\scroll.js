// Scroll management service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ScrollService {
    constructor() {
        this.subscribers = new Set();
        this.scrollPositions = new Map();
        this.scrollHistory = [];
        this.maxHistoryLength = 50;
        this.isScrolling = false;
        this.scrollTimeout = null;
        this.initialize();
    }

    /**
     * Initialize the scroll service
     */
    initialize() {
        window.addEventListener('scroll', this.handleScroll.bind(this));
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    /**
     * Subscribe to scroll events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of scroll events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in scroll subscriber:', error);
            }
        });
    }

    /**
     * Handle scroll events
     * @param {Event} event - The scroll event
     */
    handleScroll(event) {
        return measurePerformance('scroll_handle', () => {
            const position = {
                x: window.scrollX,
                y: window.scrollY,
                timestamp: Date.now(),
            };

            // Update scroll position
            this.scrollPositions.set(window.location.pathname, position);

            // Add to history
            this.scrollHistory.push(position);
            if (this.scrollHistory.length > this.maxHistoryLength) {
                this.scrollHistory.shift();
            }

            // Set scrolling state
            this.isScrolling = true;
            clearTimeout(this.scrollTimeout);
            this.scrollTimeout = setTimeout(() => {
                this.isScrolling = false;
                this.notifySubscribers('scrollend', { position });
            }, 150);

            // Notify subscribers
            this.notifySubscribers('scroll', { position, event });
        });
    }

    /**
     * Handle resize events
     * @param {Event} event - The resize event
     */
    handleResize(event) {
        return measurePerformance('scroll_resize', () => {
            const position = {
                x: window.scrollX,
                y: window.scrollY,
                timestamp: Date.now(),
            };

            this.notifySubscribers('resize', { position, event });
        });
    }

    /**
     * Scroll to a position
     * @param {number} x - The x position
     * @param {number} y - The y position
     * @param {Object} [options] - The scroll options
     */
    scrollTo(x, y, options = {}) {
        return measurePerformance('scroll_to', () => {
            const {
                behavior = 'smooth',
                block = 'start',
                inline = 'nearest',
            } = options;

            window.scrollTo({
                top: y,
                left: x,
                behavior,
            });

            this.notifySubscribers('scrollto', { x, y, options });
        });
    }

    /**
     * Scroll to an element
     * @param {HTMLElement} element - The element to scroll to
     * @param {Object} [options] - The scroll options
     */
    scrollToElement(element, options = {}) {
        return measurePerformance('scroll_to_element', () => {
            const {
                behavior = 'smooth',
                block = 'start',
                inline = 'nearest',
            } = options;

            element.scrollIntoView({
                behavior,
                block,
                inline,
            });

            this.notifySubscribers('scrolltoelement', { element, options });
        });
    }

    /**
     * Get the current scroll position
     * @returns {Object} The current scroll position
     */
    getCurrentPosition() {
        return {
            x: window.scrollX,
            y: window.scrollY,
            timestamp: Date.now(),
        };
    }

    /**
     * Get the saved scroll position for a path
     * @param {string} path - The path
     * @returns {Object|null} The saved scroll position
     */
    getSavedPosition(path) {
        return this.scrollPositions.get(path) || null;
    }

    /**
     * Save the current scroll position for a path
     * @param {string} path - The path
     */
    savePosition(path) {
        this.scrollPositions.set(path, this.getCurrentPosition());
    }

    /**
     * Restore the scroll position for a path
     * @param {string} path - The path
     * @param {Object} [options] - The scroll options
     */
    restorePosition(path, options = {}) {
        const position = this.getSavedPosition(path);
        if (position) {
            this.scrollTo(position.x, position.y, options);
        }
    }

    /**
     * Get the scroll history
     * @returns {Array} The scroll history
     */
    getScrollHistory() {
        return [...this.scrollHistory];
    }

    /**
     * Clear the scroll history
     */
    clearScrollHistory() {
        this.scrollHistory = [];
    }

    /**
     * Check if the page is currently scrolling
     * @returns {boolean} Whether the page is scrolling
     */
    isPageScrolling() {
        return this.isScrolling;
    }

    /**
     * Get the scroll direction
     * @returns {string} The scroll direction ('up', 'down', or 'none')
     */
    getScrollDirection() {
        if (this.scrollHistory.length < 2) {
            return 'none';
        }

        const current = this.scrollHistory[this.scrollHistory.length - 1];
        const previous = this.scrollHistory[this.scrollHistory.length - 2];

        if (current.y > previous.y) {
            return 'down';
        } else if (current.y < previous.y) {
            return 'up';
        } else {
            return 'none';
        }
    }
}

// Create and export a singleton instance
const scrollService = new ScrollService();
export default scrollService; 