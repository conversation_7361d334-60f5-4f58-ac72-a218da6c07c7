"""
ImpactCV - Main FastAPI Application
Enterprise-grade AI-powered CV generation system with comprehensive security
"""

import logging
import time
from contextlib import asynccontextmanager
from typing import Any, Dict

import uvicorn
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse, FileResponse
from fastapi.staticfiles import StaticFiles
from starlette.middleware.base import BaseHTTPMiddleware

from app import APP_DESCRIPTION, APP_NAME, APP_VERSION
from app.api.v1.api import api_router
from app.core.config import settings
from app.core.database import check_db_health, close_db, init_db
from app.core.cache import cache_service
from app.core.rag_pipeline import rag_pipeline
from app.services.llm_service import llm_service
from app.services.embedding_service import embedding_service
from app.services.vector_store import vector_store
from app.services.retrieval_service import retrieval_service
from app.core.logging import setup_logging
from app.core.security import SecurityHeadersMiddleware
from app.middleware.rate_limiting import RateLimitMiddleware
from app.middleware.request_logging import RequestLoggingMiddleware


# Setup logging
logger = setup_logging()


class TimingMiddleware(BaseHTTPMiddleware):
    """Middleware to add response timing headers."""
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """Add timing information to response headers."""
        start_time = time.time()
        
        response = await call_next(request)
        
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    Handles startup and shutdown events.
    """
    # Startup
    logger.info("🚀 Starting ImpactCV application...")
    logger.info(f"Environment: {settings.ENVIRONMENT}")
    logger.info(f"Debug mode: {settings.DEBUG}")
    logger.info(f"Version: {APP_VERSION}")
    
    # Initialize services
    try:
        # Database initialization
        await init_db()
        logger.info("📊 Database connection initialized")

        # AI/ML services initialization (commented out for testing without OpenAI API)
        # await llm_service.initialize()
        logger.info("🤖 LLM service skipped (testing mode)")

        # await embedding_service.initialize()
        logger.info("🔤 Embedding service skipped (testing mode)")

        # await vector_store.initialize()
        logger.info("🗂️ Vector store skipped (testing mode)")

        # await retrieval_service.initialize()
        logger.info("🔍 Retrieval service skipped (testing mode)")

        # await rag_pipeline.initialize()
        logger.info("🚀 RAG pipeline skipped (testing mode)")

        # Cache initialization (in-memory alternative to Redis)
        await cache_service.initialize()
        logger.info("🔄 Cache service initialized")

        logger.info("✅ Application startup completed successfully")

    except Exception as e:
        logger.error(f"❌ Application startup failed: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down ImpactCV application...")

    try:
        # Cleanup resources
        await close_db()
        logger.info("🧹 Cleaning up resources...")
        logger.info("✅ Application shutdown completed successfully")

    except Exception as e:
        logger.error(f"❌ Application shutdown failed: {e}")


def create_application() -> FastAPI:
    """
    Create and configure FastAPI application.
    
    Returns:
        FastAPI: Configured application instance
    """
    
    # Create FastAPI application
    app = FastAPI(
        title=APP_NAME,
        description=APP_DESCRIPTION,
        version=APP_VERSION,
        openapi_url="/api/v1/openapi.json" if settings.DEBUG else None,
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan,
        # Security configuration
        swagger_ui_parameters={
            "deepLinking": True,
            "displayRequestDuration": True,
            "docExpansion": "none",
            "operationsSorter": "method",
            "filter": True,
            "tagsSorter": "alpha",
        } if settings.DEBUG else None,
    )
    
    # ========================================================================
    # SECURITY MIDDLEWARE
    # ========================================================================
    
    # Security headers middleware (first for security)
    if settings.SECURITY_HEADERS_ENABLED:
        app.add_middleware(SecurityHeadersMiddleware)
    
    # Trusted host middleware
    if not settings.is_development():
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["localhost", "127.0.0.1", "*.impactcv.com"]
        )
    
    # Rate limiting middleware
    if settings.RATE_LIMIT_ENABLED:
        app.add_middleware(
            RateLimitMiddleware,
            requests_per_minute=settings.RATE_LIMIT_REQUESTS_PER_MINUTE
        )
    
    # ========================================================================
    # CORS MIDDLEWARE
    # ========================================================================
    
    # CORS configuration
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:8000"],
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["X-Process-Time", "X-Request-ID"],
    )
    
    # ========================================================================
    # MONITORING MIDDLEWARE
    # ========================================================================
    
    # Request logging middleware
    app.add_middleware(RequestLoggingMiddleware)
    
    # Timing middleware
    app.add_middleware(TimingMiddleware)
    
    # ========================================================================
    # EXCEPTION HANDLERS
    # ========================================================================
    
    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc: Any) -> JSONResponse:
        """Handle 404 errors."""
        return JSONResponse(
            status_code=404,
            content={
                "error": "Not Found",
                "message": "The requested resource was not found",
                "path": str(request.url.path),
                "timestamp": time.time(),
            }
        )
    
    @app.exception_handler(500)
    async def internal_error_handler(request: Request, exc: Any) -> JSONResponse:
        """Handle 500 errors."""
        logger.error(f"Internal server error: {exc}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "An internal server error occurred",
                "timestamp": time.time(),
            }
        )
    
    # ========================================================================
    # ROUTES
    # ========================================================================
    
    # Include API routes
    app.include_router(api_router, prefix="/api/v1")

    # Mount static files for frontend
    app.mount("/static", StaticFiles(directory="app/frontend"), name="static")

    # Root endpoint
    @app.get("/", tags=["Root"])
    async def root() -> Dict[str, Any]:
        """
        Root endpoint with application information.
        
        Returns:
            Dict[str, Any]: Application metadata
        """
        return {
            "name": APP_NAME,
            "version": APP_VERSION,
            "description": APP_DESCRIPTION,
            "environment": settings.ENVIRONMENT,
            "status": "healthy",
            "timestamp": time.time(),
            "docs_url": "/docs" if settings.DEBUG else None,
            "openapi_url": "/api/v1/openapi.json" if settings.DEBUG else None,
        }

    # Frontend route
    @app.get("/app", tags=["Frontend"])
    async def frontend() -> FileResponse:
        """
        Serve the frontend application.

        Returns:
            FileResponse: Frontend HTML file
        """
        return FileResponse("app/frontend/index.html")

    return app


# Create application instance
app = create_application()


def main() -> None:
    """
    Main entry point for running the application.
    """
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,  # Security: hide server header
        server_header=False,
        date_header=False,    # Security: hide date header
    )


if __name__ == "__main__":
    main()
