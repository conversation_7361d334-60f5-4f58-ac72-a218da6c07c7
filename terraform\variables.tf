# ImpactCV AI-Powered CV Generation System
# Terraform Variables Configuration

# ============================================================================
# GENERAL CONFIGURATION
# ============================================================================
variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
  
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-west-2"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "impactcv"
}

# ============================================================================
# NETWORKING CONFIGURATION
# ============================================================================
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "private_subnets" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["********/24", "********/24", "********/24"]
}

variable "public_subnets" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["**********/24", "**********/24", "**********/24"]
}

variable "allowed_cidr_blocks" {
  description = "CIDR blocks allowed to access the infrastructure"
  type        = list(string)
  default     = ["0.0.0.0/0"]  # Restrict this in production
}

# ============================================================================
# EKS CONFIGURATION
# ============================================================================
variable "kubernetes_version" {
  description = "Kubernetes version for EKS cluster"
  type        = string
  default     = "1.28"
}

variable "eks_node_groups" {
  description = "EKS node group configurations"
  type = map(object({
    instance_types = list(string)
    capacity_type  = string
    min_size      = number
    max_size      = number
    desired_size  = number
    disk_size     = number
    ami_type      = string
    labels        = map(string)
    taints = list(object({
      key    = string
      value  = string
      effect = string
    }))
  }))
  
  default = {
    general = {
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      min_size      = 1
      max_size      = 5
      desired_size  = 2
      disk_size     = 50
      ami_type      = "AL2_x86_64"
      labels = {
        role = "general"
      }
      taints = []
    }
    
    compute = {
      instance_types = ["c5.large"]
      capacity_type  = "SPOT"
      min_size      = 0
      max_size      = 10
      desired_size  = 1
      disk_size     = 100
      ami_type      = "AL2_x86_64"
      labels = {
        role = "compute"
      }
      taints = [{
        key    = "compute"
        value  = "true"
        effect = "NO_SCHEDULE"
      }]
    }
  }
}

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================
variable "postgres_version" {
  description = "PostgreSQL engine version"
  type        = string
  default     = "15.4"
}

variable "db_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.t3.micro"
}

variable "db_allocated_storage" {
  description = "Allocated storage for RDS instance (GB)"
  type        = number
  default     = 20
}

variable "db_backup_retention_period" {
  description = "Backup retention period in days"
  type        = number
  default     = 7
}

variable "db_backup_window" {
  description = "Backup window"
  type        = string
  default     = "03:00-04:00"
}

variable "db_maintenance_window" {
  description = "Maintenance window"
  type        = string
  default     = "sun:04:00-sun:05:00"
}

# ============================================================================
# CACHE CONFIGURATION
# ============================================================================
variable "redis_node_type" {
  description = "ElastiCache node type"
  type        = string
  default     = "cache.t3.micro"
}

variable "redis_num_nodes" {
  description = "Number of cache nodes"
  type        = number
  default     = 1
}

variable "redis_parameter_group" {
  description = "Redis parameter group"
  type        = string
  default     = "default.redis7"
}

variable "redis_auth_token" {
  description = "Redis authentication token"
  type        = string
  sensitive   = true
  default     = null
}

# ============================================================================
# MONITORING CONFIGURATION
# ============================================================================
variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 30
}

variable "enable_prometheus" {
  description = "Enable Prometheus monitoring"
  type        = bool
  default     = true
}

variable "enable_grafana" {
  description = "Enable Grafana dashboards"
  type        = bool
  default     = true
}

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
variable "domain_name" {
  description = "Domain name for SSL certificate"
  type        = string
  default     = "impactcv.com"
}

variable "enable_waf" {
  description = "Enable AWS WAF"
  type        = bool
  default     = true
}

# ============================================================================
# APPLICATION CONFIGURATION
# ============================================================================
variable "openai_api_key" {
  description = "OpenAI API key for AI services"
  type        = string
  sensitive   = true
}

# ============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# ============================================================================
variable "environment_configs" {
  description = "Environment-specific configuration overrides"
  type = map(object({
    db_instance_class     = string
    redis_node_type      = string
    eks_node_desired_size = number
    log_retention_days   = number
  }))
  
  default = {
    dev = {
      db_instance_class     = "db.t3.micro"
      redis_node_type      = "cache.t3.micro"
      eks_node_desired_size = 1
      log_retention_days   = 7
    }
    
    staging = {
      db_instance_class     = "db.t3.small"
      redis_node_type      = "cache.t3.small"
      eks_node_desired_size = 2
      log_retention_days   = 14
    }
    
    prod = {
      db_instance_class     = "db.r6g.large"
      redis_node_type      = "cache.r6g.large"
      eks_node_desired_size = 3
      log_retention_days   = 90
    }
  }
}

# ============================================================================
# FEATURE FLAGS
# ============================================================================
variable "feature_flags" {
  description = "Feature flags for optional components"
  type = object({
    enable_backup_bucket     = bool
    enable_cdn              = bool
    enable_elasticsearch    = bool
    enable_external_secrets = bool
    enable_istio           = bool
    enable_vault           = bool
  })
  
  default = {
    enable_backup_bucket     = true
    enable_cdn              = false
    enable_elasticsearch    = false
    enable_external_secrets = false
    enable_istio           = false
    enable_vault           = false
  }
}

# ============================================================================
# COST OPTIMIZATION
# ============================================================================
variable "cost_optimization" {
  description = "Cost optimization settings"
  type = object({
    use_spot_instances      = bool
    enable_cluster_autoscaler = bool
    enable_vertical_pod_autoscaler = bool
    enable_scheduled_scaling = bool
  })
  
  default = {
    use_spot_instances      = false
    enable_cluster_autoscaler = true
    enable_vertical_pod_autoscaler = false
    enable_scheduled_scaling = false
  }
}

# ============================================================================
# COMPLIANCE CONFIGURATION
# ============================================================================
variable "compliance_settings" {
  description = "Compliance and governance settings"
  type = object({
    enable_config_rules     = bool
    enable_cloudtrail      = bool
    enable_guardduty       = bool
    enable_security_hub    = bool
    enable_inspector       = bool
  })
  
  default = {
    enable_config_rules     = true
    enable_cloudtrail      = true
    enable_guardduty       = true
    enable_security_hub    = true
    enable_inspector       = true
  }
}

# ============================================================================
# BACKUP CONFIGURATION
# ============================================================================
variable "backup_settings" {
  description = "Backup and disaster recovery settings"
  type = object({
    enable_cross_region_backup = bool
    backup_schedule           = string
    backup_retention_days     = number
    enable_point_in_time_recovery = bool
  })
  
  default = {
    enable_cross_region_backup = false
    backup_schedule           = "cron(0 2 * * ? *)"  # Daily at 2 AM
    backup_retention_days     = 30
    enable_point_in_time_recovery = true
  }
}

# ============================================================================
# SCALING CONFIGURATION
# ============================================================================
variable "scaling_settings" {
  description = "Auto-scaling configuration"
  type = object({
    target_cpu_utilization    = number
    target_memory_utilization = number
    scale_up_cooldown         = number
    scale_down_cooldown       = number
  })
  
  default = {
    target_cpu_utilization    = 70
    target_memory_utilization = 80
    scale_up_cooldown         = 300
    scale_down_cooldown       = 300
  }
}
