"""
RAG Pipeline
Complete Retrieval-Augmented Generation pipeline for CV generation
"""

import logging
import time
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from app.services.llm_service import LLMRequest, LLMResponse, llm_service
from app.services.retrieval_service import RetrievalRequest, retrieval_service

logger = logging.getLogger(__name__)


class RAGRequest(BaseModel):
    """RAG pipeline request model."""
    
    query: str = Field(..., min_length=1, description="User query or prompt")
    system_prompt: Optional[str] = Field(None, description="System prompt template")
    context_config: Dict[str, Any] = Field(default_factory=dict, description="Context retrieval configuration")
    llm_config: Dict[str, Any] = Field(default_factory=dict, description="LLM generation configuration")
    user_id: Optional[str] = Field(None, description="User ID for tracking")
    request_id: Optional[str] = Field(None, description="Request correlation ID")


class RAGResponse(BaseModel):
    """RAG pipeline response model."""
    
    query: str = Field(..., description="Original query")
    response: str = Field(..., description="Generated response")
    context_used: str = Field(..., description="Context that was used")
    sources: List[Dict[str, Any]] = Field(..., description="Source documents")
    
    # Performance metrics
    retrieval_time_ms: float = Field(..., description="Context retrieval time")
    generation_time_ms: float = Field(..., description="LLM generation time")
    total_time_ms: float = Field(..., description="Total pipeline time")
    
    # Token usage
    context_tokens: int = Field(..., description="Tokens used for context")
    generation_tokens: Dict[str, int] = Field(..., description="LLM token usage")
    
    # Quality metrics
    context_relevance_score: float = Field(..., description="Average relevance of retrieved context")
    sources_count: int = Field(..., description="Number of source documents used")
    
    # Metadata
    model_used: str = Field(..., description="LLM model used")
    cost_estimate: float = Field(..., description="Estimated cost")
    request_id: Optional[str] = Field(None, description="Request correlation ID")


class RAGError(Exception):
    """Custom exception for RAG pipeline errors."""
    
    def __init__(self, message: str, error_code: str = "RAG_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class RAGPipeline:
    """
    Enterprise RAG pipeline for CV generation.
    
    Features:
    - Context retrieval and assembly
    - Prompt engineering and injection
    - LLM generation with monitoring
    - Quality assessment and validation
    - Performance tracking and optimization
    """
    
    def __init__(self):
        """Initialize RAG pipeline."""
        self.initialized = False
        self.request_count = 0
        self.total_pipeline_time = 0.0
        self.success_count = 0
        
        # Default configurations
        self.default_context_config = {
            "top_k": 5,
            "score_threshold": 0.3,
            "context_window": 4000,
            "include_metadata": True,
            "rerank": True,
        }
        
        self.default_llm_config = {
            "model": "gpt-4o",
            "temperature": 0.7,
            "max_tokens": 2000,
        }
    
    async def initialize(self) -> None:
        """Initialize RAG pipeline components."""
        if self.initialized:
            return
        
        try:
            # Initialize services
            await retrieval_service.initialize()
            await llm_service.initialize()
            
            self.initialized = True
            logger.info("RAG pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAG pipeline: {e}")
            raise RAGError(
                f"RAG pipeline initialization failed: {str(e)}",
                error_code="INITIALIZATION_FAILED"
            )
    
    async def generate(self, request: RAGRequest) -> RAGResponse:
        """
        Execute complete RAG pipeline.
        
        Args:
            request: RAG request with query and configuration
            
        Returns:
            RAGResponse: Generated response with metadata
        """
        if not self.initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            # Step 1: Retrieve relevant context
            retrieval_start = time.time()
            context_result = await self._retrieve_context(request)
            retrieval_time_ms = (time.time() - retrieval_start) * 1000
            
            # Step 2: Prepare prompt with context
            enhanced_prompt = await self._prepare_prompt(
                request.query,
                context_result.context,
                request.system_prompt
            )
            
            # Step 3: Generate response using LLM
            generation_start = time.time()
            llm_response = await self._generate_response(request, enhanced_prompt)
            generation_time_ms = (time.time() - generation_start) * 1000
            
            # Step 4: Post-process and validate response
            final_response = await self._post_process_response(llm_response.content)
            
            # Calculate metrics
            total_time_ms = (time.time() - start_time) * 1000
            context_relevance_score = self._calculate_context_relevance(context_result.sources)
            
            # Update statistics
            self.request_count += 1
            self.success_count += 1
            self.total_pipeline_time += total_time_ms
            
            # Create response
            rag_response = RAGResponse(
                query=request.query,
                response=final_response,
                context_used=context_result.context,
                sources=context_result.sources,
                retrieval_time_ms=retrieval_time_ms,
                generation_time_ms=generation_time_ms,
                total_time_ms=total_time_ms,
                context_tokens=context_result.total_tokens,
                generation_tokens=llm_response.usage,
                context_relevance_score=context_relevance_score,
                sources_count=len(context_result.sources),
                model_used=llm_response.model,
                cost_estimate=llm_response.cost_estimate or 0.0,
                request_id=request.request_id
            )
            
            logger.info(f"RAG pipeline completed in {total_time_ms:.2f}ms for query: {request.query[:50]}...")
            return rag_response
            
        except Exception as e:
            self.request_count += 1
            logger.error(f"RAG pipeline failed: {e}")
            raise RAGError(
                f"RAG pipeline execution failed: {str(e)}",
                error_code="PIPELINE_FAILED",
                details={"query": request.query}
            )
    
    async def _retrieve_context(self, request: RAGRequest):
        """Retrieve relevant context for the query."""
        # Merge default and request-specific context config
        context_config = {**self.default_context_config, **request.context_config}
        
        retrieval_request = RetrievalRequest(
            query=request.query,
            **context_config
        )
        
        return await retrieval_service.retrieve_context(retrieval_request)
    
    async def _prepare_prompt(
        self,
        query: str,
        context: str,
        system_prompt: Optional[str] = None
    ) -> str:
        """Prepare enhanced prompt with context injection."""
        
        # Default system prompt for CV generation
        if not system_prompt:
            system_prompt = """You are an expert CV/resume writer with extensive experience in creating professional, compelling resumes that help candidates stand out to employers.

Your task is to help users create, improve, or customize their CVs based on their background, target roles, and industry best practices.

Use the provided context documents as reference for formatting, content structure, and industry-specific requirements. However, always prioritize the user's specific information and requirements.

Guidelines:
- Create professional, ATS-friendly content
- Use action verbs and quantifiable achievements
- Tailor content to the target role/industry
- Maintain consistent formatting and structure
- Ensure all information is accurate and relevant
- Follow modern CV best practices

Context Documents:
{context}

Please provide helpful, actionable advice and generate high-quality CV content based on the user's request."""
        
        # Inject context into system prompt
        enhanced_system_prompt = system_prompt.format(context=context)
        
        # Create the final prompt
        enhanced_prompt = f"System: {enhanced_system_prompt}\n\nUser Query: {query}"
        
        return enhanced_prompt
    
    async def _generate_response(self, request: RAGRequest, prompt: str) -> LLMResponse:
        """Generate response using LLM."""
        # Merge default and request-specific LLM config
        llm_config = {**self.default_llm_config, **request.llm_config}
        
        llm_request = LLMRequest(
            prompt=prompt,
            user_id=request.user_id,
            request_id=request.request_id,
            **llm_config
        )
        
        return await llm_service.generate_completion(llm_request)
    
    async def _post_process_response(self, response: str) -> str:
        """Post-process and validate the generated response."""
        # Basic post-processing
        processed_response = response.strip()
        
        # Remove any potential system artifacts
        if processed_response.startswith("System:"):
            lines = processed_response.split("\n")
            processed_response = "\n".join(lines[1:]).strip()
        
        # Ensure minimum response length
        if len(processed_response) < 50:
            logger.warning("Generated response is very short, may indicate an issue")
        
        return processed_response
    
    def _calculate_context_relevance(self, sources: List[Dict[str, Any]]) -> float:
        """Calculate average relevance score of retrieved context."""
        if not sources:
            return 0.0
        
        total_score = sum(source.get("score", 0.0) for source in sources)
        return total_score / len(sources)
    
    async def generate_cv_section(
        self,
        section_type: str,
        user_data: Dict[str, Any],
        context_filters: Optional[Dict[str, Any]] = None
    ) -> RAGResponse:
        """
        Generate a specific CV section using RAG.
        
        Args:
            section_type: Type of section (summary, experience, skills, etc.)
            user_data: User's information for the section
            context_filters: Filters for retrieving relevant examples
            
        Returns:
            RAGResponse: Generated section content
        """
        # Create section-specific query
        query = f"Generate a professional {section_type} section for a CV with the following information: {user_data}"
        
        # Add section-specific context filters
        context_config = self.default_context_config.copy()
        if context_filters:
            context_config["filter_metadata"] = context_filters
        else:
            context_config["filter_metadata"] = {"section_type": section_type}
        
        # Create RAG request
        request = RAGRequest(
            query=query,
            context_config=context_config,
            llm_config={"temperature": 0.5}  # Lower temperature for more consistent output
        )
        
        return await self.generate(request)
    
    async def improve_cv_content(
        self,
        current_content: str,
        improvement_type: str = "general"
    ) -> RAGResponse:
        """
        Improve existing CV content using RAG.
        
        Args:
            current_content: Current CV content to improve
            improvement_type: Type of improvement (clarity, impact, ats, etc.)
            
        Returns:
            RAGResponse: Improved content suggestions
        """
        query = f"Improve the following CV content for {improvement_type}: {current_content}"
        
        request = RAGRequest(
            query=query,
            context_config={
                **self.default_context_config,
                "filter_metadata": {"document_type": "best_practices"}
            },
            llm_config={"temperature": 0.3}  # Lower temperature for improvement tasks
        )
        
        return await self.generate(request)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get RAG pipeline statistics."""
        avg_pipeline_time = (
            self.total_pipeline_time / self.request_count
            if self.request_count > 0 else 0
        )
        
        success_rate = (
            self.success_count / self.request_count
            if self.request_count > 0 else 0
        )
        
        return {
            "initialized": self.initialized,
            "request_count": self.request_count,
            "success_count": self.success_count,
            "success_rate": success_rate,
            "total_pipeline_time_ms": self.total_pipeline_time,
            "avg_pipeline_time_ms": avg_pipeline_time,
            "retrieval_stats": retrieval_service.get_stats(),
            "llm_stats": llm_service.get_service_stats(),
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        try:
            if not self.initialized:
                await self.initialize()
            
            # Test complete pipeline
            test_request = RAGRequest(
                query="Generate a brief professional summary for a software engineer",
                llm_config={"max_tokens": 100}
            )
            
            start_time = time.time()
            response = await self.generate(test_request)
            response_time = (time.time() - start_time) * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "response_length": len(response.response),
                "sources_found": response.sources_count,
                "context_relevance": response.context_relevance_score,
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
            }


# Global pipeline instance
rag_pipeline = RAGPipeline()
