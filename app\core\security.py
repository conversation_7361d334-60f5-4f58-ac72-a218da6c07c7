"""
ImpactCV Security Middleware and Utilities
Comprehensive security controls and OWASP Top 10 protection
"""

import hashlib
import hmac
import secrets
import time
from typing import Any, Dict, List, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from app.core.config import settings
from app.core.logging import security_audit_logger


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Security headers middleware implementing OWASP security best practices.
    
    Implements:
    - Content Security Policy (CSP)
    - X-Frame-Options
    - X-Content-Type-Options
    - X-XSS-Protection
    - Strict-Transport-Security
    - Referrer-Policy
    - Permissions-Policy
    """
    
    def __init__(self, app, **kwargs):
        """Initialize security headers middleware."""
        super().__init__(app)
        self.csp_nonce_cache = {}
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Generate nonce for CSP
        nonce = secrets.token_urlsafe(16)
        
        # Security headers
        security_headers = {
            # Content Security Policy
            "Content-Security-Policy": (
                f"default-src 'self'; "
                f"script-src 'self' 'nonce-{nonce}' 'unsafe-inline'; "
                f"style-src 'self' 'unsafe-inline'; "
                f"img-src 'self' data: https:; "
                f"font-src 'self'; "
                f"connect-src 'self'; "
                f"frame-ancestors 'none'; "
                f"base-uri 'self'; "
                f"form-action 'self'"
            ),
            
            # Prevent clickjacking
            "X-Frame-Options": "DENY",
            
            # Prevent MIME type sniffing
            "X-Content-Type-Options": "nosniff",
            
            # XSS Protection
            "X-XSS-Protection": "1; mode=block",
            
            # HSTS (only in production with HTTPS)
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload" if not settings.is_development() else None,
            
            # Referrer Policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # Permissions Policy
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            ),
            
            # Remove server information
            "Server": "",
            
            # Cache control for sensitive endpoints
            "Cache-Control": "no-store, no-cache, must-revalidate, private" if self._is_sensitive_endpoint(request) else None,
            
            # Additional security headers
            "X-Permitted-Cross-Domain-Policies": "none",
            "Cross-Origin-Embedder-Policy": "require-corp",
            "Cross-Origin-Opener-Policy": "same-origin",
            "Cross-Origin-Resource-Policy": "same-origin",
        }
        
        # Add headers to response
        for header, value in security_headers.items():
            if value is not None:
                response.headers[header] = value
        
        # Add nonce to response for CSP
        response.headers["X-CSP-Nonce"] = nonce
        
        return response
    
    def _is_sensitive_endpoint(self, request: Request) -> bool:
        """Check if endpoint handles sensitive data."""
        sensitive_paths = ["/api/v1/auth", "/api/v1/users", "/api/v1/cv"]
        return any(request.url.path.startswith(path) for path in sensitive_paths)


class InputSanitizer:
    """
    Input sanitization utilities for preventing injection attacks.
    """
    
    # Dangerous patterns for SQL injection
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(--|#|/\*|\*/)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
    ]
    
    # XSS patterns
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>.*?</iframe>",
        r"<object[^>]*>.*?</object>",
        r"<embed[^>]*>.*?</embed>",
    ]
    
    # Command injection patterns
    COMMAND_INJECTION_PATTERNS = [
        r"[;&|`$(){}[\]\\]",  # Keep basic command separators
        r"\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl)\b",  # Keep basic command names
    ]
    
    @classmethod
    def sanitize_string(cls, value: str, max_length: int = 1000) -> str:
        """
        Sanitize string input.
        
        Args:
            value: Input string to sanitize
            max_length: Maximum allowed length
            
        Returns:
            str: Sanitized string
            
        Raises:
            ValueError: If input contains dangerous patterns
        """
        if not isinstance(value, str):
            raise ValueError("Input must be a string")
        
        # Length check
        if len(value) > max_length:
            raise ValueError(f"Input too long (max {max_length} characters)")
        
        # Check for dangerous patterns
        import re
        
        # SQL injection check
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                security_audit_logger.log_security_event(
                    "sql_injection_attempt",
                    severity="WARNING",
                    description="Potential SQL injection attempt detected",
                    pattern=pattern,
                    input_value="[REDACTED]"
                )
                raise ValueError("Input contains potentially dangerous SQL patterns")
        
        # XSS check
        for pattern in cls.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                security_audit_logger.log_security_event(
                    "xss_attempt",
                    severity="WARNING",
                    description="Potential XSS attempt detected",
                    pattern=pattern,
                    input_value="[REDACTED]"
                )
                raise ValueError("Input contains potentially dangerous XSS patterns")
        
        # Command injection check - only for actual command patterns
        for pattern in cls.COMMAND_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                # Check if it's a legitimate CV content
                if not any(keyword in value.lower() for keyword in ['achievement', 'experience', 'skill', 'project', 'education']):
                    security_audit_logger.log_security_event(
                        "command_injection_attempt",
                        severity="WARNING",
                        description="Potential command injection attempt detected",
                        pattern=pattern,
                        input_value="[REDACTED]"
                    )
                    raise ValueError("Input contains potentially dangerous command patterns")
        
        # Basic HTML entity encoding for XSS prevention
        value = value.replace("&", "&amp;")
        value = value.replace("<", "&lt;")
        value = value.replace(">", "&gt;")
        value = value.replace('"', "&quot;")
        value = value.replace("'", "&#x27;")
        
        return value.strip()
    
    @classmethod
    def sanitize_filename(cls, filename: str) -> str:
        """
        Sanitize filename for safe file operations.
        
        Args:
            filename: Original filename
            
        Returns:
            str: Sanitized filename
        """
        import re
        
        # Remove path traversal attempts
        filename = filename.replace("..", "")
        filename = filename.replace("/", "")
        filename = filename.replace("\\", "")
        
        # Remove dangerous characters
        filename = re.sub(r'[<>:"|?*]', '', filename)
        
        # Limit length
        if len(filename) > 255:
            name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
            filename = name[:250] + ('.' + ext if ext else '')
        
        return filename


class PasswordSecurity:
    """
    Password security utilities with bcrypt hashing.
    """
    
    @staticmethod
    def hash_password(password: str) -> str:
        """
        Hash password using bcrypt.
        
        Args:
            password: Plain text password
            
        Returns:
            str: Hashed password
        """
        import bcrypt
        
        # Generate salt and hash password
        salt = bcrypt.gensalt(rounds=settings.PASSWORD_HASH_ROUNDS)
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        
        return hashed.decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """
        Verify password against hash.
        
        Args:
            password: Plain text password
            hashed: Hashed password
            
        Returns:
            bool: True if password matches
        """
        import bcrypt
        
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception:
            return False
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """
        Validate password strength.
        
        Args:
            password: Password to validate
            
        Returns:
            Dict[str, Any]: Validation result
        """
        import re
        
        result = {
            "valid": True,
            "score": 0,
            "issues": []
        }
        
        # Length check
        if len(password) < 8:
            result["issues"].append("Password must be at least 8 characters long")
            result["valid"] = False
        else:
            result["score"] += 1
        
        # Uppercase check
        if not re.search(r'[A-Z]', password):
            result["issues"].append("Password must contain at least one uppercase letter")
            result["valid"] = False
        else:
            result["score"] += 1
        
        # Lowercase check
        if not re.search(r'[a-z]', password):
            result["issues"].append("Password must contain at least one lowercase letter")
            result["valid"] = False
        else:
            result["score"] += 1
        
        # Number check
        if not re.search(r'\d', password):
            result["issues"].append("Password must contain at least one number")
            result["valid"] = False
        else:
            result["score"] += 1
        
        # Special character check
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            result["issues"].append("Password must contain at least one special character")
            result["valid"] = False
        else:
            result["score"] += 1
        
        # Common password check
        common_passwords = [
            "password", "123456", "password123", "admin", "qwerty",
            "letmein", "welcome", "monkey", "dragon", "master"
        ]
        
        if password.lower() in common_passwords:
            result["issues"].append("Password is too common")
            result["valid"] = False
        
        return result


class TokenSecurity:
    """
    Token generation and validation utilities.
    """
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """
        Generate cryptographically secure token.
        
        Args:
            length: Token length in bytes
            
        Returns:
            str: Secure token
        """
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def generate_api_key() -> str:
        """
        Generate API key with specific format.
        
        Returns:
            str: API key
        """
        prefix = "ik_"  # ImpactCV key
        key_part = secrets.token_urlsafe(32)
        return f"{prefix}{key_part}"
    
    @staticmethod
    def constant_time_compare(a: str, b: str) -> bool:
        """
        Constant time string comparison to prevent timing attacks.
        
        Args:
            a: First string
            b: Second string
            
        Returns:
            bool: True if strings are equal
        """
        return hmac.compare_digest(a, b)


class RateLimitTracker:
    """
    Simple in-memory rate limit tracker.
    In production, this should use Redis or similar.
    """
    
    def __init__(self):
        """Initialize rate limit tracker."""
        self.requests = {}
        self.cleanup_interval = 60  # seconds
        self.last_cleanup = time.time()
    
    def is_allowed(self, identifier: str, limit: int, window: int) -> bool:
        """
        Check if request is allowed under rate limit.
        
        Args:
            identifier: Unique identifier (IP, user ID, etc.)
            limit: Maximum requests allowed
            window: Time window in seconds
            
        Returns:
            bool: True if request is allowed
        """
        now = time.time()
        
        # Cleanup old entries periodically
        if now - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_entries(now, window)
            self.last_cleanup = now
        
        # Get or create request history for identifier
        if identifier not in self.requests:
            self.requests[identifier] = []
        
        request_times = self.requests[identifier]
        
        # Remove requests outside the window
        cutoff_time = now - window
        request_times[:] = [t for t in request_times if t > cutoff_time]
        
        # Check if under limit
        if len(request_times) < limit:
            request_times.append(now)
            return True
        
        return False
    
    def _cleanup_old_entries(self, now: float, window: int) -> None:
        """Clean up old entries to prevent memory leaks."""
        cutoff_time = now - window
        
        for identifier in list(self.requests.keys()):
            request_times = self.requests[identifier]
            request_times[:] = [t for t in request_times if t > cutoff_time]
            
            # Remove empty entries
            if not request_times:
                del self.requests[identifier]


# Global rate limit tracker instance
rate_limit_tracker = RateLimitTracker()
