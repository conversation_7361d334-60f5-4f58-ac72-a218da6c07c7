version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: impactcv_postgres_dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: impactcv
      POSTGRES_USER: impactcv_user
      POSTGRES_PASSWORD: impactcv_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U impactcv_user -d impactcv"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: impactcv_redis_dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    command: redis-server --appendonly yes --requirepass impactcv_redis_password
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: impactcv_pgadmin_dev
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data_dev:/var/lib/pgadmin
    depends_on:
      - postgres

volumes:
  postgres_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  pgadmin_data_dev:
    driver: local
