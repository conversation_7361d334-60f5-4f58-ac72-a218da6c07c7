"""
Integration tests for complete CV generation pipeline
"""

import pytest
import tempfile
import os
from unittest.mock import patch, Mock

from app.services.document_parser import document_parser
from app.services.data_normalizer import data_normalizer
from app.core.pii_detector import pii_detector
from app.core.validators import data_validator
from app.services.data_quality import data_quality_service
from app.services.lineage_tracker import lineage_tracker
from app.services.data_catalog import data_catalog
from app.core.rag_pipeline import rag_pipeline
from app.services.llm_service import llm_service


class TestCVGenerationPipeline:
    """Integration tests for complete CV generation pipeline."""
    
    @pytest.fixture
    def sample_cv_text(self):
        """Sample CV text for testing."""
        return """
        John <PERSON>
        Software Engineer
        <EMAIL>
        (*************
        
        SUMMARY
        Experienced software engineer with 5+ years in web development.
        Passionate about building scalable applications and leading teams.
        
        EXPERIENCE
        Senior Software Engineer - Tech Corp (2020-2023)
        - Led development of microservices architecture
        - Managed team of 5 developers
        - Improved system performance by 40%
        
        Software Engineer - StartupXYZ (2018-2020)
        - Built REST APIs using Python/Django
        - Implemented CI/CD pipelines
        - Reduced deployment time by 60%
        
        EDUCATION
        Bachelor of Science in Computer Science
        University of Technology (2014-2018)
        GPA: 3.8/4.0
        
        SKILLS
        Programming: Python, JavaScript, Java, Go
        Web: React, Django, FastAPI, Node.js
        Databases: PostgreSQL, MongoDB, Redis
        Cloud: AWS, Docker, Kubernetes
        
        CERTIFICATIONS
        AWS Certified Solutions Architect (2022)
        Certified Kubernetes Administrator (2021)
        
        PROJECTS
        E-commerce Platform
        - Built scalable e-commerce platform using microservices
        - Technologies: Python, React, PostgreSQL, Redis
        - URL: github.com/johndoe/ecommerce
        """
    
    @pytest.fixture
    def sample_cv_file(self, sample_cv_text):
        """Create temporary CV file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
            tmp.write(sample_cv_text)
            tmp_path = tmp.name
        
        yield tmp_path
        
        # Cleanup
        try:
            os.unlink(tmp_path)
        except OSError:
            pass
    
    def test_document_parsing_integration(self, sample_cv_file):
        """Test document parsing integration."""
        # Parse document
        result = document_parser.parse_document(sample_cv_file)
        
        # Verify parsing results
        assert result.text is not None
        assert len(result.text) > 0
        assert len(result.pages) == 1
        assert result.pages[0]['word_count'] > 50
        
        # Extract sections
        sections = document_parser.extract_cv_sections(result)
        
        # Verify sections
        assert 'summary' in sections
        assert 'experience' in sections
        assert 'education' in sections
        assert 'skills' in sections
        
        # Verify content
        assert "Experienced software engineer" in sections['summary']
        assert "Senior Software Engineer" in sections['experience']
        assert "Bachelor of Science" in sections['education']
        assert "Python" in sections['skills']
    
    def test_data_normalization_integration(self, sample_cv_file):
        """Test data normalization integration."""
        # Parse document first
        parsed_content = document_parser.parse_document(sample_cv_file)
        sections = document_parser.extract_cv_sections(parsed_content)
        
        # Create CV data structure
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            'summary': sections.get('summary', ''),
            'experience': [
                {
                    'job_title': 'Senior Software Engineer',
                    'company': 'Tech Corp',
                    'start_date': '2020',
                    'end_date': '2023',
                    'description': 'Led development of microservices architecture'
                },
                {
                    'job_title': 'Software Engineer',
                    'company': 'StartupXYZ',
                    'start_date': '2018',
                    'end_date': '2020',
                    'description': 'Built REST APIs using Python/Django'
                }
            ],
            'education': [
                {
                    'degree': 'Bachelor of Science in Computer Science',
                    'institution': 'University of Technology',
                    'start_date': '2014',
                    'end_date': '2018',
                    'gpa': '3.8'
                }
            ],
            'skills': [
                {'name': 'Python'},
                {'name': 'JavaScript'},
                {'name': 'React'},
                {'name': 'Django'}
            ]
        }
        
        # Normalize data
        normalized_result = data_normalizer.normalize_cv_data(cv_data)
        
        # Verify normalization
        assert normalized_result.is_valid
        assert normalized_result.normalized_data is not None
        assert 'personal' in normalized_result.normalized_data
        assert 'experience' in normalized_result.normalized_data
        assert 'education' in normalized_result.normalized_data
        assert 'skills' in normalized_result.normalized_data
        
        # Verify personal info normalization
        personal = normalized_result.normalized_data['personal']
        assert personal['full_name'] == 'John Doe'
        assert personal['email'] == '<EMAIL>'
        assert 'first_name' in personal
        assert 'last_name' in personal
        
        # Verify experience normalization
        experience = normalized_result.normalized_data['experience']
        assert len(experience) == 2
        assert experience[0]['job_title'] == 'Senior Software Engineer'
        assert experience[0]['company'] == 'Tech Corp'
        
        # Verify metadata
        metadata = normalized_result.normalized_data['metadata']
        assert 'total_experience_years' in metadata
        assert 'skill_categories' in metadata
        assert 'experience_level' in metadata
    
    def test_pii_detection_integration(self, sample_cv_file):
        """Test PII detection integration."""
        # Parse document
        parsed_content = document_parser.parse_document(sample_cv_file)
        
        # Detect PII
        pii_result = pii_detector.detect_pii(parsed_content.text)
        
        # Verify PII detection
        assert pii_result.original_text == parsed_content.text
        assert pii_result.sanitized_text is not None
        assert len(pii_result.pii_matches) > 0
        
        # Should detect email and phone
        pii_types = pii_result.pii_types
        assert 'email' in pii_types
        assert 'phone' in pii_types
        
        # Verify risk assessment
        assert pii_result.risk_level in ['low', 'medium', 'high']
        
        # Verify sanitization
        assert pii_result.sanitization_method in ['mask', 'remove', 'hash', 'replace']
    
    def test_data_validation_integration(self, sample_cv_file):
        """Test data validation integration."""
        # Parse and normalize data first
        parsed_content = document_parser.parse_document(sample_cv_file)
        sections = document_parser.extract_cv_sections(parsed_content)
        
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            'summary': sections.get('summary', ''),
            'experience': [
                {
                    'job_title': 'Senior Software Engineer',
                    'company': 'Tech Corp',
                    'start_date': '2020',
                    'end_date': '2023'
                }
            ],
            'education': [
                {
                    'degree': 'Bachelor of Science',
                    'institution': 'University of Technology',
                    'start_date': '2014',
                    'end_date': '2018'
                }
            ],
            'skills': [
                {'name': 'Python'},
                {'name': 'JavaScript'}
            ]
        }
        
        # Validate data
        validation_result = data_validator.validate_cv_data(cv_data)
        
        # Verify validation
        assert validation_result.is_valid == True
        assert validation_result.score > 0.7  # Should have good quality score
        assert validation_result.validated_data is not None
        
        # Should have minimal errors for well-formed data
        error_count = len([e for e in validation_result.errors if e.severity == 'error'])
        assert error_count == 0
    
    def test_data_quality_assessment_integration(self, sample_cv_file):
        """Test data quality assessment integration."""
        # Parse and normalize data
        parsed_content = document_parser.parse_document(sample_cv_file)
        sections = document_parser.extract_cv_sections(parsed_content)
        
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            'summary': sections.get('summary', ''),
            'experience': [
                {
                    'job_title': 'Senior Software Engineer',
                    'company': 'Tech Corp',
                    'start_date': '2020',
                    'end_date': '2023'
                }
            ],
            'education': [
                {
                    'degree': 'Bachelor of Science',
                    'institution': 'University of Technology'
                }
            ],
            'skills': [
                {'name': 'Python'},
                {'name': 'JavaScript'}
            ]
        }
        
        # Assess quality
        quality_report = data_quality_service.assess_cv_quality(cv_data)
        
        # Verify quality assessment
        assert quality_report.overall_score > 0.5
        assert quality_report.grade in ['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D', 'F']
        assert len(quality_report.metrics) == 7  # 7 quality dimensions
        
        # Verify metrics
        metric_names = [m.name for m in quality_report.metrics]
        expected_metrics = ['completeness', 'accuracy', 'consistency', 'validity', 'uniqueness', 'timeliness', 'integrity']
        for metric in expected_metrics:
            assert metric in metric_names
        
        # Verify compliance status
        assert 'DAMA-DMBOK' in quality_report.compliance_status
    
    def test_lineage_tracking_integration(self, sample_cv_file):
        """Test data lineage tracking integration."""
        cv_id = "test_cv_001"
        user_id = "test_user_001"
        
        # Define processing steps
        processing_steps = [
            {
                "type": "document_parsing",
                "name": "Parse CV Document",
                "description": "Extract text and metadata from CV file",
                "metadata": {"file_type": "text/plain"},
                "success": True,
                "duration_ms": 150
            },
            {
                "type": "data_normalization",
                "name": "Normalize CV Data",
                "description": "Standardize CV data format",
                "metadata": {"sections_found": 5},
                "success": True,
                "duration_ms": 75
            },
            {
                "type": "pii_detection",
                "name": "Detect PII",
                "description": "Identify and sanitize personal information",
                "metadata": {"pii_found": 2},
                "success": True,
                "duration_ms": 50
            },
            {
                "type": "cv_generation",
                "name": "Generate CV",
                "description": "Generate final CV using AI",
                "metadata": {"model": "gpt-4o"},
                "success": True,
                "duration_ms": 2000
            }
        ]
        
        # Track lineage
        lineage_graph = lineage_tracker.track_cv_processing_lineage(
            cv_id, user_id, processing_steps
        )
        
        # Verify lineage tracking
        assert lineage_graph.name == f"CV_Processing_{cv_id}"
        assert len(lineage_graph.nodes) == len(processing_steps)
        assert len(lineage_graph.edges) == len(processing_steps) - 1
        
        # Verify metadata
        assert lineage_graph.metadata['cv_id'] == cv_id
        assert lineage_graph.metadata['user_id'] == user_id
        assert lineage_graph.metadata['processing_type'] == 'cv_generation'
        
        # Verify nodes
        node_types = [node.type for node in lineage_graph.nodes]
        assert 'source' in node_types
        assert 'transformation' in node_types
        assert 'destination' in node_types
    
    def test_data_catalog_integration(self, sample_cv_file):
        """Test data catalog integration."""
        cv_id = "test_cv_002"
        user_id = "test_user_002"
        
        # Parse CV data
        parsed_content = document_parser.parse_document(sample_cv_file)
        sections = document_parser.extract_cv_sections(parsed_content)
        
        cv_data = {
            'personal': {'full_name': 'John Doe'},
            'summary': sections.get('summary', ''),
            'experience': [{'job_title': 'Engineer', 'company': 'Tech Corp'}],
            'skills': [{'name': 'Python'}]
        }
        
        # Register CV assets
        assets = data_catalog.register_cv_assets(cv_id, user_id, cv_data)
        
        # Verify asset registration
        assert len(assets) > 1  # Should have main CV + section assets
        
        # Verify main CV asset
        cv_asset = assets[0]
        assert cv_asset.id == f"cv_{cv_id}"
        assert cv_asset.type == "cv_document"
        assert cv_asset.owner == user_id
        assert cv_asset.domain == "hr_documents"
        assert cv_asset.classification == "confidential"
        
        # Search for assets
        search_result = data_catalog.search_assets(
            query="John Doe",
            filters={"type": "cv_document"}
        )
        
        # Verify search results
        assert search_result.total_count >= 1
        assert len(search_result.assets) >= 1
        assert search_result.search_time_ms > 0
    
    @patch('app.services.llm_service.llm_service.generate_completion')
    def test_rag_pipeline_integration(self, mock_generate, sample_cv_file):
        """Test RAG pipeline integration."""
        # Mock LLM response
        mock_generate.return_value = {
            'content': 'Generated CV content based on provided context',
            'usage': {'total_tokens': 150},
            'model': 'gpt-4o'
        }
        
        # Parse CV data
        parsed_content = document_parser.parse_document(sample_cv_file)
        sections = document_parser.extract_cv_sections(parsed_content)
        
        # Test RAG generation
        user_input = "Generate a professional summary for a software engineer"
        context_data = {
            'experience': sections.get('experience', ''),
            'skills': sections.get('skills', ''),
            'education': sections.get('education', '')
        }
        
        # Generate with RAG
        result = rag_pipeline.generate_cv_section(
            section_type="summary",
            user_input=user_input,
            context_data=context_data
        )
        
        # Verify RAG result
        assert result['content'] is not None
        assert len(result['content']) > 0
        assert 'usage' in result
        assert 'model' in result
        
        # Verify LLM was called
        mock_generate.assert_called_once()
    
    def test_complete_pipeline_integration(self, sample_cv_file):
        """Test complete CV processing pipeline."""
        cv_id = "test_cv_complete"
        user_id = "test_user_complete"
        
        # Step 1: Document Parsing
        parsed_content = document_parser.parse_document(sample_cv_file)
        sections = document_parser.extract_cv_sections(parsed_content)
        
        assert parsed_content.text is not None
        assert len(sections) > 0
        
        # Step 2: Data Normalization
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            'summary': sections.get('summary', ''),
            'experience': [
                {
                    'job_title': 'Senior Software Engineer',
                    'company': 'Tech Corp',
                    'start_date': '2020',
                    'end_date': '2023'
                }
            ],
            'education': [
                {
                    'degree': 'Bachelor of Science',
                    'institution': 'University of Technology'
                }
            ],
            'skills': [
                {'name': 'Python'},
                {'name': 'JavaScript'}
            ]
        }
        
        normalized_result = data_normalizer.normalize_cv_data(cv_data)
        assert normalized_result.is_valid
        
        # Step 3: PII Detection
        pii_result = pii_detector.detect_pii(parsed_content.text)
        assert len(pii_result.pii_matches) > 0
        
        # Step 4: Data Validation
        validation_result = data_validator.validate_cv_data(cv_data)
        assert validation_result.is_valid
        
        # Step 5: Quality Assessment
        quality_report = data_quality_service.assess_cv_quality(cv_data)
        assert quality_report.overall_score > 0.5
        
        # Step 6: Lineage Tracking
        processing_steps = [
            {"type": "parsing", "name": "Parse Document", "description": "Extract content", "success": True},
            {"type": "normalization", "name": "Normalize Data", "description": "Standardize format", "success": True},
            {"type": "validation", "name": "Validate Data", "description": "Check quality", "success": True}
        ]
        
        lineage_graph = lineage_tracker.track_cv_processing_lineage(cv_id, user_id, processing_steps)
        assert len(lineage_graph.nodes) == 3
        
        # Step 7: Catalog Registration
        assets = data_catalog.register_cv_assets(cv_id, user_id, cv_data)
        assert len(assets) > 1
        
        # Verify end-to-end pipeline success
        pipeline_success = (
            parsed_content.text is not None and
            normalized_result.is_valid and
            len(pii_result.pii_matches) >= 0 and
            validation_result.is_valid and
            quality_report.overall_score > 0 and
            len(lineage_graph.nodes) > 0 and
            len(assets) > 0
        )
        
        assert pipeline_success == True
    
    def test_error_handling_integration(self):
        """Test error handling across pipeline components."""
        # Test with invalid file
        with pytest.raises(Exception):
            document_parser.parse_document("/non/existent/file.pdf")
        
        # Test with invalid CV data
        invalid_cv_data = {}
        
        # Normalization should handle empty data gracefully
        normalized_result = data_normalizer.normalize_cv_data(invalid_cv_data)
        assert normalized_result is not None
        
        # Validation should report errors for empty data
        validation_result = data_validator.validate_cv_data(invalid_cv_data)
        assert validation_result.is_valid == False
        assert len(validation_result.errors) > 0
        
        # Quality assessment should handle empty data
        quality_report = data_quality_service.assess_cv_quality(invalid_cv_data)
        assert quality_report.overall_score < 0.5
    
    def test_performance_integration(self, sample_cv_file):
        """Test pipeline performance."""
        import time
        
        start_time = time.time()
        
        # Run complete pipeline
        parsed_content = document_parser.parse_document(sample_cv_file)
        sections = document_parser.extract_cv_sections(parsed_content)
        
        cv_data = {
            'personal': {'full_name': 'John Doe', 'email': '<EMAIL>'},
            'summary': sections.get('summary', ''),
            'experience': [{'job_title': 'Engineer', 'company': 'Tech Corp'}],
            'skills': [{'name': 'Python'}]
        }
        
        normalized_result = data_normalizer.normalize_cv_data(cv_data)
        pii_result = pii_detector.detect_pii(parsed_content.text)
        validation_result = data_validator.validate_cv_data(cv_data)
        quality_report = data_quality_service.assess_cv_quality(cv_data)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Pipeline should complete within reasonable time (< 5 seconds for test data)
        assert processing_time < 5.0
        
        # All components should succeed
        assert parsed_content.text is not None
        assert normalized_result.is_valid
        assert pii_result.sanitized_text is not None
        assert validation_result.score > 0
        assert quality_report.overall_score > 0
