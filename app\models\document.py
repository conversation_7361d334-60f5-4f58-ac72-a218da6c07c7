"""
Document Models
Document management and RAG pipeline support
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, Foreign<PERSON>ey, Integer, String, Text, JSON
from sqlalchemy.orm import relationship

from app.models.base import Base


class Document(Base):
    """
    Document model for file management and RAG pipeline.
    
    Stores:
    - Uploaded documents (PDFs, DOCX, etc.)
    - Document metadata and processing status
    - RAG pipeline integration
    """
    
    # Foreign key to user (optional for system documents)
    user_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
        doc="Reference to the document owner (null for system documents)"
    )
    
    # Document information
    filename = Column(
        String(255),
        nullable=False,
        doc="Original filename"
    )
    
    title = Column(
        String(200),
        nullable=True,
        doc="Document title (extracted or user-provided)"
    )
    
    description = Column(
        Text,
        nullable=True,
        doc="Document description"
    )
    
    # File details
    file_path = Column(
        String(500),
        nullable=False,
        doc="Path to the stored file"
    )
    
    file_size = Column(
        Integer,
        nullable=False,
        doc="File size in bytes"
    )
    
    file_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="File MIME type"
    )
    
    file_extension = Column(
        String(10),
        nullable=False,
        index=True,
        doc="File extension"
    )
    
    # Content and processing
    content_text = Column(
        Text,
        nullable=True,
        doc="Extracted text content"
    )
    
    content_html = Column(
        Text,
        nullable=True,
        doc="Extracted HTML content"
    )
    
    # Processing status
    processing_status = Column(
        String(20),
        default="pending",
        nullable=False,
        index=True,
        doc="Processing status: pending, processing, completed, failed"
    )
    
    processing_error = Column(
        Text,
        nullable=True,
        doc="Error message if processing failed"
    )
    
    processed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When document processing was completed"
    )
    
    # Document classification
    document_type = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Document type: cv, resume, cover_letter, template, example"
    )
    
    category = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Document category for organization"
    )
    
    tags = Column(
        JSON,
        nullable=True,
        doc="Document tags for search and organization (stored as JSON array)"
    )
    
    # RAG pipeline integration
    is_indexed = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether document is indexed in vector database"
    )
    
    embedding_model = Column(
        String(100),
        nullable=True,
        doc="Embedding model used for vectorization"
    )
    
    chunk_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of chunks created from this document"
    )
    
    # Document metadata
    language = Column(
        String(10),
        nullable=True,
        doc="Detected document language"
    )
    
    word_count = Column(
        Integer,
        nullable=True,
        doc="Estimated word count"
    )
    
    page_count = Column(
        Integer,
        nullable=True,
        doc="Number of pages (for PDFs)"
    )
    
    # Access control
    is_public = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether document is publicly accessible"
    )
    
    is_template = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether document is a template"
    )
    
    # Analytics
    download_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times document has been downloaded"
    )
    
    view_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times document has been viewed"
    )
    
    # Relationships
    user = relationship("User", backref="documents")
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")
    doc_metadata = relationship("DocumentMetadata", back_populates="document", uselist=False, cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        """String representation of the document."""
        return f"<Document(filename={self.filename}, type={self.document_type})>"
    
    @property
    def is_processed(self) -> bool:
        """Check if document processing is completed."""
        return self.processing_status == "completed"
    
    @property
    def is_failed(self) -> bool:
        """Check if document processing failed."""
        return self.processing_status == "failed"
    
    def mark_as_processed(self) -> None:
        """Mark document as successfully processed."""
        self.processing_status = "completed"
        self.processed_at = datetime.utcnow()
        self.processing_error = None
    
    def mark_as_failed(self, error_message: str) -> None:
        """Mark document processing as failed."""
        self.processing_status = "failed"
        self.processed_at = datetime.utcnow()
        self.processing_error = error_message


class DocumentChunk(Base):
    """
    Document chunk model for RAG pipeline.
    
    Stores text chunks created from documents for:
    - Vector embedding and similarity search
    - Context retrieval for AI generation
    - Efficient document processing
    """
    
    # Foreign key to document
    document_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("document.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the parent document"
    )
    
    # Chunk information
    chunk_index = Column(
        Integer,
        nullable=False,
        doc="Index of chunk within document (0-based)"
    )
    
    content = Column(
        Text,
        nullable=False,
        doc="Chunk text content"
    )
    
    # Chunk metadata
    start_position = Column(
        Integer,
        nullable=True,
        doc="Start position in original document"
    )
    
    end_position = Column(
        Integer,
        nullable=True,
        doc="End position in original document"
    )
    
    word_count = Column(
        Integer,
        nullable=False,
        doc="Number of words in chunk"
    )
    
    character_count = Column(
        Integer,
        nullable=False,
        doc="Number of characters in chunk"
    )
    
    # Vector embedding
    embedding_vector = Column(
        JSON,
        nullable=True,
        doc="Vector embedding of the chunk content (stored as JSON array)"
    )
    
    embedding_model = Column(
        String(100),
        nullable=True,
        doc="Model used to generate embedding"
    )
    
    embedding_created_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When embedding was created"
    )
    
    # Chunk context
    section_title = Column(
        String(200),
        nullable=True,
        doc="Section title or heading"
    )
    
    page_number = Column(
        Integer,
        nullable=True,
        doc="Page number in original document"
    )
    
    # Chunk quality metrics
    relevance_score = Column(
        Float,
        nullable=True,
        doc="Relevance score for ranking"
    )
    
    quality_score = Column(
        Float,
        nullable=True,
        doc="Content quality score"
    )
    
    # Additional metadata
    chunk_metadata = Column(
        JSON,
        nullable=True,
        doc="Additional chunk metadata as JSON"
    )
    
    # Relationships
    document = relationship("Document", back_populates="chunks")
    
    def __repr__(self) -> str:
        """String representation of the chunk."""
        return f"<DocumentChunk(document_id={self.document_id}, index={self.chunk_index})>"
    
    @property
    def has_embedding(self) -> bool:
        """Check if chunk has an embedding vector."""
        return self.embedding_vector is not None and len(self.embedding_vector) > 0
    
    def set_embedding(self, vector: List[float], model: str) -> None:
        """Set the embedding vector for this chunk."""
        self.embedding_vector = vector
        self.embedding_model = model
        self.embedding_created_at = datetime.utcnow()


class DocumentMetadata(Base):
    """
    Extended document metadata for detailed document information.
    """
    
    # Foreign key to document
    document_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("document.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        doc="Reference to the document"
    )
    
    # Extracted metadata
    author = Column(
        String(200),
        nullable=True,
        doc="Document author"
    )
    
    subject = Column(
        String(500),
        nullable=True,
        doc="Document subject"
    )
    
    keywords = Column(
        JSON,
        nullable=True,
        doc="Document keywords (stored as JSON array)"
    )
    
    creation_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Document creation date (from metadata)"
    )
    
    modification_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Document modification date (from metadata)"
    )
    
    # Technical metadata
    producer = Column(
        String(200),
        nullable=True,
        doc="Software that produced the document"
    )
    
    creator = Column(
        String(200),
        nullable=True,
        doc="Software that created the document"
    )
    
    # Content analysis
    sentiment_score = Column(
        Float,
        nullable=True,
        doc="Sentiment analysis score (-1 to 1)"
    )
    
    readability_score = Column(
        Float,
        nullable=True,
        doc="Readability score"
    )
    
    complexity_score = Column(
        Float,
        nullable=True,
        doc="Content complexity score"
    )
    
    # Extracted entities
    entities = Column(
        JSON,
        nullable=True,
        doc="Named entities extracted from document"
    )

    # Document structure
    sections = Column(
        JSON,
        nullable=True,
        doc="Document sections and structure"
    )

    # Custom metadata
    custom_metadata = Column(
        JSON,
        nullable=True,
        doc="Custom metadata fields"
    )
    
    # Relationships
    document = relationship("Document", back_populates="metadata")
    
    def __repr__(self) -> str:
        """String representation of the metadata."""
        return f"<DocumentMetadata(document_id={self.document_id})>"
