"""
LLM Service
Unified AI service supporting both Mistral 7B local and OpenAI with enterprise security and monitoring
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field

from app.core.config import get_settings
from app.services.ai_service import ai_service

logger = logging.getLogger(__name__)
settings = get_settings()


class LLMRequest(BaseModel):
    """LLM request model with validation."""
    
    prompt: str = Field(..., min_length=1, max_length=50000, description="Input prompt")
    system_prompt: Optional[str] = Field(None, max_length=10000, description="System prompt")
    model: str = Field(default="gpt-4o", description="Model to use")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Temperature for randomness")
    max_tokens: int = Field(default=2000, ge=1, le=4000, description="Maximum tokens to generate")
    user_id: Optional[str] = Field(None, description="User ID for tracking")
    request_id: Optional[str] = Field(None, description="Request correlation ID")


class LLMResponse(BaseModel):
    """LLM response model."""
    
    content: str = Field(..., description="Generated content")
    model: str = Field(..., description="Model used")
    usage: Dict[str, int] = Field(..., description="Token usage statistics")
    response_time_ms: float = Field(..., description="Response time in milliseconds")
    request_id: Optional[str] = Field(None, description="Request correlation ID")
    cost_estimate: Optional[float] = Field(None, description="Estimated cost in USD")


class LLMError(Exception):
    """Custom exception for LLM service errors."""
    
    def __init__(self, message: str, error_code: str = "LLM_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class LLMService:
    """
    Enterprise LLM service with security, monitoring, and reliability features.

    Features:
    - Unified AI provider support (Mistral 7B local / OpenAI)
    - Request/response validation
    - Rate limiting and retry logic
    - Cost tracking and monitoring
    - Comprehensive audit logging
    - Error handling and fallbacks
    """

    def __init__(self):
        """Initialize LLM service."""
        self.ai_service = ai_service
        self._initialized = False
        self._request_count = 0
        self._total_cost = 0.0
        self._rate_limit_remaining = 1000  # Default rate limit

        # Model pricing (per 1K tokens) - Updated for various models
        self.pricing = {
            "gpt-4o": {"input": 0.005, "output": 0.015},
            "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
            "mistral:7b": {"input": 0.0, "output": 0.0},  # Local model - no cost
        }
    
    async def initialize(self) -> None:
        """Initialize AI service with security configurations."""
        if self._initialized:
            return

        try:
            # Test AI service connection
            await self._test_connection()

            self._initialized = True
            logger.info(f"LLM service initialized successfully with provider: {settings.AI_PROVIDER}")

        except Exception as e:
            logger.error(f"Failed to initialize LLM service: {e}")
            raise LLMError(
                f"LLM service initialization failed: {str(e)}",
                error_code="INITIALIZATION_FAILED",
                details={"error": str(e)}
            )
    
    async def _test_connection(self) -> None:
        """Test AI service connection."""
        try:
            health_result = await self.ai_service.health_check()
            if health_result.get("status") != "healthy":
                raise Exception(f"AI service unhealthy: {health_result.get('error', 'Unknown error')}")
            logger.info(f"AI service connection test successful: {health_result.get('provider', 'unknown')}")
        except Exception as e:
            raise LLMError(
                f"AI service connection test failed: {str(e)}",
                error_code="CONNECTION_TEST_FAILED"
            )
    
    async def generate_completion(self, request: LLMRequest) -> LLMResponse:
        """
        Generate completion using unified AI service.

        Args:
            request: LLM request with prompt and parameters

        Returns:
            LLMResponse: Generated completion with metadata

        Raises:
            LLMError: If generation fails
        """
        if not self._initialized:
            await self.initialize()

        start_time = time.time()

        try:
            # Validate request
            await self._validate_request(request)

            # Check rate limits
            await self._check_rate_limits()

            # Generate completion using unified AI service
            content = await self.ai_service.generate_completion(
                prompt=request.prompt,
                system_prompt=request.system_prompt,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )

            # Calculate response time
            response_time_ms = (time.time() - start_time) * 1000

            # Create mock usage for compatibility (actual usage tracking depends on provider)
            usage = {
                "prompt_tokens": len(request.prompt.split()) * 1.3,  # Rough estimate
                "completion_tokens": len(content.split()) * 1.3,  # Rough estimate
                "total_tokens": 0
            }
            usage["total_tokens"] = int(usage["prompt_tokens"] + usage["completion_tokens"])

            # Calculate cost
            cost_estimate = self._calculate_cost(request.model, usage)
            self._total_cost += cost_estimate

            # Update counters
            self._request_count += 1

            # Create response
            llm_response = LLMResponse(
                content=content,
                model=request.model,
                usage=usage,
                response_time_ms=response_time_ms,
                request_id=request.request_id,
                cost_estimate=cost_estimate
            )

            # Log successful request
            await self._log_request(request, llm_response, success=True)

            return llm_response

        except Exception as e:
            error_msg = f"AI generation error: {str(e)}"
            logger.error(error_msg)
            await self._log_request(request, None, success=False, error=error_msg)
            raise LLMError(error_msg, error_code="AI_GENERATION_ERROR")
    
    async def _validate_request(self, request: LLMRequest) -> None:
        """Validate LLM request for security and compliance."""
        
        # Check for potential prompt injection
        dangerous_patterns = [
            "ignore previous instructions",
            "forget everything above",
            "system:",
            "assistant:",
            "\\n\\nHuman:",
            "\\n\\nAssistant:",
        ]
        
        prompt_lower = request.prompt.lower()
        for pattern in dangerous_patterns:
            if pattern in prompt_lower:
                raise LLMError(
                    f"Potential prompt injection detected: {pattern}",
                    error_code="PROMPT_INJECTION_DETECTED",
                    details={"pattern": pattern}
                )
        
        # Check model availability
        if request.model not in self.pricing:
            raise LLMError(
                f"Unsupported model: {request.model}",
                error_code="UNSUPPORTED_MODEL",
                details={"model": request.model}
            )
    
    async def _check_rate_limits(self) -> None:
        """Check and enforce rate limits."""
        if self._rate_limit_remaining <= 0:
            raise LLMError(
                "Rate limit exceeded, please try again later",
                error_code="RATE_LIMIT_EXCEEDED"
            )
    

    
    def _calculate_cost(self, model: str, usage: Dict[str, int]) -> float:
        """Calculate estimated cost for the request."""
        if model not in self.pricing:
            return 0.0
        
        pricing = self.pricing[model]
        input_cost = (usage.get("prompt_tokens", 0) / 1000) * pricing["input"]
        output_cost = (usage.get("completion_tokens", 0) / 1000) * pricing["output"]
        
        return round(input_cost + output_cost, 6)
    
    async def _log_request(
        self,
        request: LLMRequest,
        response: Optional[LLMResponse],
        success: bool,
        error: Optional[str] = None
    ) -> None:
        """Log LLM request for audit and monitoring."""
        try:
            event_data = {
                "model": request.model,
                "prompt_length": len(request.prompt),
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
                "success": success,
            }
            
            if response:
                event_data.update({
                    "response_length": len(response.content),
                    "usage": response.usage,
                    "response_time_ms": response.response_time_ms,
                    "cost_estimate": response.cost_estimate,
                })
            
            if error:
                event_data["error"] = error
            
            # Create audit log (implementation depends on your audit system)
            # This is a placeholder - you would implement actual logging
            logger.info(f"LLM request logged: {event_data}")
            
        except Exception as e:
            logger.error(f"Failed to log LLM request: {e}")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get service statistics for monitoring."""
        return {
            "initialized": self._initialized,
            "request_count": self._request_count,
            "total_cost": self._total_cost,
            "rate_limit_remaining": self._rate_limit_remaining,
            "supported_models": list(self.pricing.keys()),
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of the LLM service."""
        try:
            if not self._initialized:
                await self.initialize()

            # Use AI service health check
            ai_health = await self.ai_service.health_check()

            return {
                "status": ai_health.get("status", "unknown"),
                "provider": ai_health.get("provider", "unknown"),
                "configured_provider": ai_health.get("configured_provider", "unknown"),
                "rate_limit_remaining": self._rate_limit_remaining,
                "request_count": self._request_count,
                "total_cost": self._total_cost,
                "ai_service_details": ai_health
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "provider": "unknown",
            }


# Global service instance
llm_service = LLMService()
