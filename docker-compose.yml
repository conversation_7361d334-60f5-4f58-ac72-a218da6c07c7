version: '3.8'

services:
  # =============================================================================
  # DATABASES & STORAGE
  # =============================================================================
  
  postgresql:
    image: postgres:15-alpine
    container_name: impactcv-postgres
    environment:
      POSTGRES_DB: impactcv
      POSTGRES_USER: impactcv_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-dev_password_123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U impactcv_user -d impactcv"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - impactcv-network

  redis:
    image: redis:7-alpine
    container_name: impactcv-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-dev_redis_123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - impactcv-network

  # =============================================================================
  # MICROSERVICES
  # =============================================================================

  api-gateway:
    build:
      context: ./services/api_gateway
      dockerfile: Dockerfile
    container_name: impactcv-api-gateway
    environment:
      - DATABASE_URL=postgresql://impactcv_user:${POSTGRES_PASSWORD:-dev_password_123}@postgresql:5432/impactcv
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev_jwt_secret_key_very_long_and_secure}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    depends_on:
      postgresql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - impactcv-network

  auth-service:
    build:
      context: ./services/auth
      dockerfile: Dockerfile
    container_name: impactcv-auth
    environment:
      - DATABASE_URL=postgresql://impactcv_user:${POSTGRES_PASSWORD:-dev_password_123}@postgresql:5432/impactcv
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379/1
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev_jwt_secret_key_very_long_and_secure}
      - JWT_ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
    ports:
      - "8001:8000"
    depends_on:
      postgresql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - impactcv-network

  cv-generation-service:
    build:
      context: ./services/cv_generation
      dockerfile: Dockerfile
    container_name: impactcv-cv-generation
    environment:
      - DATABASE_URL=postgresql://impactcv_user:${POSTGRES_PASSWORD:-dev_password_123}@postgresql:5432/impactcv
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379/2
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - RAG_SERVICE_URL=http://rag-service:8000
      - DATA_SERVICE_URL=http://data-processing-service:8000
    ports:
      - "8002:8000"
    volumes:
      - cv_storage:/app/storage
    depends_on:
      postgresql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - impactcv-network

  rag-service:
    build:
      context: ./services/rag
      dockerfile: Dockerfile
    container_name: impactcv-rag
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - FAISS_INDEX_PATH=/app/faiss_index
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379/3
      - EMBEDDING_MODEL=text-embedding-3-large
      - LLM_MODEL=gpt-4o
    ports:
      - "8003:8000"
    volumes:
      - faiss_data:/app/faiss_index
      - document_storage:/app/documents
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - impactcv-network

  data-processing-service:
    build:
      context: ./services/data_processing
      dockerfile: Dockerfile
    container_name: impactcv-data-processing
    environment:
      - DATABASE_URL=postgresql://impactcv_user:${POSTGRES_PASSWORD:-dev_password_123}@postgresql:5432/impactcv
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379/4
      - UPLOAD_PATH=/app/uploads
      - MAX_FILE_SIZE=10485760  # 10MB
    ports:
      - "8004:8000"
    volumes:
      - document_storage:/app/uploads
    depends_on:
      postgresql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - impactcv-network

  # =============================================================================
  # LOAD BALANCER & REVERSE PROXY
  # =============================================================================

  nginx:
    image: nginx:1.24-alpine
    container_name: impactcv-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api-gateway
      - auth-service
      - cv-generation-service
      - rag-service
      - data-processing-service
    restart: unless-stopped
    networks:
      - impactcv-network

  # =============================================================================
  # MONITORING & OBSERVABILITY
  # =============================================================================

  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: impactcv-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped
    networks:
      - impactcv-network

  grafana:
    image: grafana/grafana:10.2.0
    container_name: impactcv-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - impactcv-network

  # =============================================================================
  # LOGGING STACK (ELK)
  # =============================================================================

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: impactcv-elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped
    networks:
      - impactcv-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: impactcv-logstash
    volumes:
      - ./config/logstash/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - impactcv-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: impactcv-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - impactcv-network

  # =============================================================================
  # DISTRIBUTED TRACING
  # =============================================================================

  jaeger:
    image: jaegertracing/all-in-one:1.50
    container_name: impactcv-jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
      - "14250:14250"  # Jaeger collector gRPC
    restart: unless-stopped
    networks:
      - impactcv-network

# =============================================================================
# VOLUMES
# =============================================================================

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  cv_storage:
    driver: local
  faiss_data:
    driver: local
  document_storage:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

# =============================================================================
# NETWORKS
# =============================================================================

networks:
  impactcv-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
