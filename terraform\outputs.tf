# ImpactCV AI-Powered CV Generation System
# Terraform Outputs Configuration

# ============================================================================
# GENERAL OUTPUTS
# ============================================================================
output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "aws_region" {
  description = "AWS region"
  value       = var.aws_region
}

output "account_id" {
  description = "AWS Account ID"
  value       = data.aws_caller_identity.current.account_id
}

# ============================================================================
# NETWORKING OUTPUTS
# ============================================================================
output "vpc_id" {
  description = "VPC ID"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "VPC CIDR block"
  value       = module.vpc.vpc_cidr_block
}

output "private_subnets" {
  description = "Private subnet IDs"
  value       = module.vpc.private_subnets
}

output "public_subnets" {
  description = "Public subnet IDs"
  value       = module.vpc.public_subnets
}

output "nat_gateway_ips" {
  description = "NAT Gateway public IPs"
  value       = module.vpc.nat_public_ips
}

# ============================================================================
# EKS CLUSTER OUTPUTS
# ============================================================================
output "cluster_name" {
  description = "EKS cluster name"
  value       = module.eks.cluster_name
}

output "cluster_endpoint" {
  description = "EKS cluster endpoint"
  value       = module.eks.cluster_endpoint
  sensitive   = true
}

output "cluster_version" {
  description = "EKS cluster version"
  value       = module.eks.cluster_version
}

output "cluster_security_group_id" {
  description = "EKS cluster security group ID"
  value       = module.eks.cluster_security_group_id
}

output "cluster_iam_role_arn" {
  description = "EKS cluster IAM role ARN"
  value       = module.eks.cluster_iam_role_arn
}

output "cluster_certificate_authority_data" {
  description = "EKS cluster certificate authority data"
  value       = module.eks.cluster_certificate_authority_data
  sensitive   = true
}

output "cluster_oidc_issuer_url" {
  description = "EKS cluster OIDC issuer URL"
  value       = module.eks.cluster_oidc_issuer_url
}

output "node_groups" {
  description = "EKS node group information"
  value       = module.eks.node_groups
}

# ============================================================================
# DATABASE OUTPUTS
# ============================================================================
output "database_endpoint" {
  description = "RDS instance endpoint"
  value       = module.database.endpoint
  sensitive   = true
}

output "database_port" {
  description = "RDS instance port"
  value       = module.database.port
}

output "database_name" {
  description = "Database name"
  value       = module.database.database_name
}

output "database_username" {
  description = "Database master username"
  value       = module.database.username
  sensitive   = true
}

output "database_connection_string" {
  description = "Database connection string"
  value       = module.database.connection_string
  sensitive   = true
}

# ============================================================================
# CACHE OUTPUTS
# ============================================================================
output "redis_endpoint" {
  description = "Redis cluster endpoint"
  value       = module.cache.endpoint
  sensitive   = true
}

output "redis_port" {
  description = "Redis port"
  value       = module.cache.port
}

output "redis_connection_string" {
  description = "Redis connection string"
  value       = module.cache.connection_string
  sensitive   = true
}

# ============================================================================
# LOAD BALANCER OUTPUTS
# ============================================================================
output "load_balancer_dns_name" {
  description = "Load balancer DNS name"
  value       = module.load_balancer.dns_name
}

output "load_balancer_zone_id" {
  description = "Load balancer zone ID"
  value       = module.load_balancer.zone_id
}

output "load_balancer_arn" {
  description = "Load balancer ARN"
  value       = module.load_balancer.arn
}

# ============================================================================
# SECURITY OUTPUTS
# ============================================================================
output "certificate_arn" {
  description = "ACM certificate ARN"
  value       = module.security.acm_certificate_arn
}

output "waf_web_acl_arn" {
  description = "WAF Web ACL ARN"
  value       = module.security.waf_web_acl_arn
}

output "kms_key_ids" {
  description = "KMS key IDs"
  value = {
    database = module.security.database_kms_key_id
    s3       = module.security.s3_kms_key_id
    secrets  = module.security.secrets_kms_key_id
  }
  sensitive = true
}

# ============================================================================
# STORAGE OUTPUTS
# ============================================================================
output "s3_buckets" {
  description = "S3 bucket information"
  value = {
    app_bucket    = module.storage.app_bucket_name
    backup_bucket = module.storage.backup_bucket_name
    logs_bucket   = module.storage.logs_bucket_name
  }
}

# ============================================================================
# MONITORING OUTPUTS
# ============================================================================
output "cloudwatch_log_groups" {
  description = "CloudWatch log group names"
  value       = module.monitoring.log_group_names
}

output "prometheus_endpoint" {
  description = "Prometheus endpoint"
  value       = module.monitoring.prometheus_endpoint
}

output "grafana_endpoint" {
  description = "Grafana endpoint"
  value       = module.monitoring.grafana_endpoint
}

# ============================================================================
# SECRETS OUTPUTS
# ============================================================================
output "secrets_manager_arn" {
  description = "Secrets Manager secret ARN"
  value       = aws_secretsmanager_secret.app_secrets.arn
}

output "secrets_manager_name" {
  description = "Secrets Manager secret name"
  value       = aws_secretsmanager_secret.app_secrets.name
}

# ============================================================================
# KUBERNETES OUTPUTS
# ============================================================================
output "kubernetes_namespace" {
  description = "Kubernetes namespace"
  value       = kubernetes_namespace.impactcv.metadata[0].name
}

output "ingress_controller_endpoint" {
  description = "Ingress controller endpoint"
  value       = helm_release.nginx_ingress.status[0].load_balancer[0].ingress[0].hostname
}

# ============================================================================
# CONNECTION INFORMATION
# ============================================================================
output "kubectl_config_command" {
  description = "Command to configure kubectl"
  value       = "aws eks update-kubeconfig --region ${var.aws_region} --name ${module.eks.cluster_name}"
}

output "application_urls" {
  description = "Application URLs"
  value = {
    api_endpoint     = "https://${module.load_balancer.dns_name}/api"
    docs_endpoint    = "https://${module.load_balancer.dns_name}/docs"
    health_endpoint  = "https://${module.load_balancer.dns_name}/health"
    metrics_endpoint = "https://${module.load_balancer.dns_name}/metrics"
  }
}

# ============================================================================
# DEPLOYMENT INFORMATION
# ============================================================================
output "deployment_info" {
  description = "Deployment information"
  value = {
    cluster_name     = module.eks.cluster_name
    namespace        = kubernetes_namespace.impactcv.metadata[0].name
    database_host    = module.database.endpoint
    redis_host       = module.cache.endpoint
    secrets_name     = aws_secretsmanager_secret.app_secrets.name
    load_balancer    = module.load_balancer.dns_name
  }
  sensitive = true
}

# ============================================================================
# COST INFORMATION
# ============================================================================
output "estimated_monthly_cost" {
  description = "Estimated monthly cost breakdown"
  value = {
    eks_cluster      = "~$73/month (cluster) + node costs"
    rds_database     = "~$15-200/month (depends on instance type)"
    elasticache      = "~$15-100/month (depends on node type)"
    load_balancer    = "~$23/month"
    nat_gateway      = "~$45/month"
    data_transfer    = "Variable based on usage"
    storage          = "~$5-20/month"
    monitoring       = "~$10-30/month"
    total_estimate   = "~$200-500/month for ${var.environment} environment"
  }
}

# ============================================================================
# SECURITY INFORMATION
# ============================================================================
output "security_info" {
  description = "Security configuration information"
  value = {
    vpc_flow_logs_enabled    = true
    encryption_at_rest       = true
    encryption_in_transit    = true
    waf_enabled             = var.enable_waf
    certificate_validation  = "DNS"
    secrets_encryption      = "KMS"
    database_encryption     = "KMS"
    backup_encryption       = "KMS"
  }
}

# ============================================================================
# COMPLIANCE INFORMATION
# ============================================================================
output "compliance_info" {
  description = "Compliance and governance information"
  value = {
    gdpr_compliant          = true
    hipaa_eligible          = true
    soc2_controls          = true
    audit_logging          = true
    data_residency         = var.aws_region
    backup_retention       = "${var.db_backup_retention_period} days"
    log_retention          = "${var.log_retention_days} days"
  }
}
