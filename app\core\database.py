"""
Database Configuration and Session Management
PostgreSQL connection with security and performance optimizations
"""

import logging
from contextlib import asynccontextmanager, contextmanager
from typing import As<PERSON><PERSON><PERSON>ator, Generator, Optional

from sqlalchemy import create_engine, event, pool
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import QueuePool

from app.core.config import settings
from app.models.base import Base

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Database manager with connection pooling and session management.
    
    Features:
    - Connection pooling with configurable limits
    - Async and sync session support
    - Health monitoring
    - Security configurations
    """
    
    def __init__(self):
        """Initialize database manager."""
        self._engine = None
        self._async_engine = None
        self._session_factory = None
        self._async_session_factory = None
        self._initialized = False
    
    def initialize(self) -> None:
        """Initialize database connections and session factories."""
        if self._initialized:
            return
        
        # Create synchronous engine
        self._engine = create_engine(
            settings.get_database_url(),
            poolclass=QueuePool,
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            pool_timeout=settings.DATABASE_POOL_TIMEOUT,
            pool_pre_ping=True,  # Validate connections before use
            pool_recycle=3600,   # Recycle connections every hour
            echo=settings.DEBUG,  # Log SQL queries in debug mode
            # Security: Disable autocommit for explicit transaction control
            isolation_level="READ_COMMITTED",
            # Performance optimizations
            connect_args={
                "application_name": "ImpactCV",
                "connect_timeout": 10,
                "command_timeout": 30,
                # Security: Use SSL in production
                "sslmode": "prefer" if not settings.is_development() else "disable",
            }
        )
        
        # Create asynchronous engine
        database_url = settings.get_database_url()

        if database_url.startswith("sqlite"):
            # SQLite configuration
            async_database_url = database_url.replace("sqlite://", "sqlite+aiosqlite://")
            self._async_engine = create_async_engine(
                async_database_url,
                echo=settings.DEBUG,
                connect_args={"check_same_thread": False}
            )
        else:
            # PostgreSQL configuration
            async_database_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
            self._async_engine = create_async_engine(
                async_database_url,
                # Don't specify poolclass for async engines - SQLAlchemy will use the appropriate one
                pool_size=getattr(settings, 'DATABASE_POOL_SIZE', 20),
                max_overflow=getattr(settings, 'DATABASE_MAX_OVERFLOW', 30),
                pool_timeout=getattr(settings, 'DATABASE_POOL_TIMEOUT', 30),
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=settings.DEBUG,
                # Async-specific configurations for asyncpg
                connect_args={
                    "command_timeout": 30,
                    "server_settings": {
                        "jit": "off",  # Disable JIT for better connection performance
                    }
                }
            )
        
        # Create session factories
        self._session_factory = sessionmaker(
            bind=self._engine,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False
        )
        
        self._async_session_factory = async_sessionmaker(
            bind=self._async_engine,
            class_=AsyncSession,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False
        )
        
        # Set up event listeners for security and monitoring
        self._setup_event_listeners()
        
        self._initialized = True
        logger.info("Database manager initialized successfully")
    
    def _setup_event_listeners(self) -> None:
        """Set up SQLAlchemy event listeners for monitoring and security."""
        
        @event.listens_for(self._engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """Set database-specific configurations on connect."""
            if hasattr(dbapi_connection, "execute"):
                # PostgreSQL-specific settings
                cursor = dbapi_connection.cursor()
                
                # Security settings
                cursor.execute("SET statement_timeout = '30s'")  # Prevent long-running queries
                cursor.execute("SET lock_timeout = '10s'")       # Prevent lock waits
                cursor.execute("SET idle_in_transaction_session_timeout = '5min'")
                
                # Performance settings
                cursor.execute("SET work_mem = '4MB'")
                cursor.execute("SET maintenance_work_mem = '64MB'")
                
                cursor.close()
        
        @event.listens_for(self._engine, "before_cursor_execute")
        def log_slow_queries(conn, cursor, statement, parameters, context, executemany):
            """Log slow queries for performance monitoring."""
            import time
            context._query_start_time = time.time()
        
        @event.listens_for(self._engine, "after_cursor_execute")
        def log_slow_queries_after(conn, cursor, statement, parameters, context, executemany):
            """Log queries that take longer than threshold."""
            import time
            total = time.time() - context._query_start_time
            
            # Log queries slower than 1 second
            if total > 1.0:
                logger.warning(
                    f"Slow query detected: {total:.2f}s - {statement[:100]}..."
                )
    
    @property
    def engine(self):
        """Get synchronous database engine."""
        if not self._initialized:
            self.initialize()
        return self._engine
    
    @property
    def async_engine(self):
        """Get asynchronous database engine."""
        if not self._initialized:
            self.initialize()
        return self._async_engine
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """
        Get a synchronous database session with automatic cleanup.
        
        Yields:
            Session: SQLAlchemy session
        """
        if not self._initialized:
            self.initialize()
        
        session = self._session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get an asynchronous database session with automatic cleanup.
        
        Yields:
            AsyncSession: SQLAlchemy async session
        """
        if not self._initialized:
            self.initialize()
        
        session = self._async_session_factory()
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
    
    async def create_tables(self) -> None:
        """Create all database tables."""
        if not self._initialized:
            self.initialize()
        
        async with self._async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created successfully")
    
    async def drop_tables(self) -> None:
        """Drop all database tables (use with caution!)."""
        if not self._initialized:
            self.initialize()
        
        async with self._async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        logger.warning("Database tables dropped")
    
    async def check_connection(self) -> bool:
        """
        Check database connection health.
        
        Returns:
            bool: True if connection is healthy
        """
        try:
            if not self._initialized:
                self.initialize()
            
            async with self.get_async_session() as session:
                result = await session.execute("SELECT 1")
                return result.scalar() == 1
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    def get_connection_info(self) -> dict:
        """
        Get database connection information for monitoring.
        
        Returns:
            dict: Connection information
        """
        if not self._initialized:
            return {"status": "not_initialized"}
        
        pool = self._engine.pool
        return {
            "status": "initialized",
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
        }
    
    async def close(self) -> None:
        """Close database connections."""
        if self._engine:
            self._engine.dispose()
        
        if self._async_engine:
            await self._async_engine.dispose()
        
        self._initialized = False
        logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


# Dependency functions for FastAPI
def get_db() -> Generator[Session, None, None]:
    """
    FastAPI dependency for synchronous database sessions.
    
    Yields:
        Session: Database session
    """
    with db_manager.get_session() as session:
        yield session


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency for asynchronous database sessions.
    
    Yields:
        AsyncSession: Async database session
    """
    async with db_manager.get_async_session() as session:
        yield session


# Utility functions
async def init_db() -> None:
    """Initialize database and create tables."""
    logger.info("Initializing database...")
    
    # Initialize database manager
    db_manager.initialize()
    
    # Create tables
    await db_manager.create_tables()
    
    logger.info("Database initialization completed")


async def close_db() -> None:
    """Close database connections."""
    await db_manager.close()


# Database health check function
async def check_db_health() -> dict:
    """
    Comprehensive database health check.
    
    Returns:
        dict: Health check results
    """
    health_info = {
        "status": "unknown",
        "connection": False,
        "response_time_ms": None,
        "pool_info": {},
        "error": None
    }
    
    try:
        import time
        start_time = time.time()
        
        # Check connection
        connection_ok = await db_manager.check_connection()
        response_time = (time.time() - start_time) * 1000
        
        health_info.update({
            "status": "healthy" if connection_ok else "unhealthy",
            "connection": connection_ok,
            "response_time_ms": round(response_time, 2),
            "pool_info": db_manager.get_connection_info()
        })
        
    except Exception as e:
        health_info.update({
            "status": "error",
            "error": str(e)
        })
        logger.error(f"Database health check failed: {e}")
    
    return health_info
