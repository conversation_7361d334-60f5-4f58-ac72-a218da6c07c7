"""
Data Lineage Tracking Service
Complete audit trail from input to output with DAMA-DMBOK compliance
"""

import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class LineageNode(BaseModel):
    """Data lineage node model."""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique node ID")
    name: str = Field(..., description="Node name")
    type: str = Field(..., description="Node type: source, transformation, destination")
    description: str = Field(..., description="Node description")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Node metadata")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")


class LineageEdge(BaseModel):
    """Data lineage edge model."""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Unique edge ID")
    source_node_id: str = Field(..., description="Source node ID")
    target_node_id: str = Field(..., description="Target node ID")
    transformation_type: str = Field(..., description="Type of transformation")
    transformation_details: Dict[str, Any] = Field(default_factory=dict, description="Transformation details")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")


class LineageGraph(BaseModel):
    """Complete data lineage graph."""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Graph ID")
    name: str = Field(..., description="Graph name")
    nodes: List[LineageNode] = Field(default_factory=list, description="Graph nodes")
    edges: List[LineageEdge] = Field(default_factory=list, description="Graph edges")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Graph metadata")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")


class LineageTrackingError(Exception):
    """Custom exception for lineage tracking errors."""
    
    def __init__(self, message: str, error_code: str = "LINEAGE_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class LineageTracker:
    """
    Enterprise data lineage tracking service.
    
    Features:
    - Complete data flow tracking
    - Transformation documentation
    - Impact analysis
    - Compliance reporting
    - Visual lineage graphs
    """
    
    def __init__(self):
        """Initialize lineage tracker."""
        self.active_graphs = {}  # graph_id -> LineageGraph
        self.node_registry = {}  # node_id -> LineageNode
        self.edge_registry = {}  # edge_id -> LineageEdge
    
    def create_lineage_graph(self, name: str, metadata: Optional[Dict[str, Any]] = None) -> LineageGraph:
        """
        Create a new lineage graph.
        
        Args:
            name: Graph name
            metadata: Optional metadata
            
        Returns:
            LineageGraph: Created lineage graph
        """
        graph = LineageGraph(
            name=name,
            metadata=metadata or {}
        )
        
        self.active_graphs[graph.id] = graph
        logger.info(f"Created lineage graph: {name} ({graph.id})")
        
        return graph
    
    def add_source_node(
        self,
        graph_id: str,
        name: str,
        description: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> LineageNode:
        """Add a source node to the lineage graph."""
        return self._add_node(graph_id, name, "source", description, metadata)
    
    def add_transformation_node(
        self,
        graph_id: str,
        name: str,
        description: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> LineageNode:
        """Add a transformation node to the lineage graph."""
        return self._add_node(graph_id, name, "transformation", description, metadata)
    
    def add_destination_node(
        self,
        graph_id: str,
        name: str,
        description: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> LineageNode:
        """Add a destination node to the lineage graph."""
        return self._add_node(graph_id, name, "destination", description, metadata)
    
    def _add_node(
        self,
        graph_id: str,
        name: str,
        node_type: str,
        description: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> LineageNode:
        """Add a node to the lineage graph."""
        if graph_id not in self.active_graphs:
            raise LineageTrackingError(f"Graph not found: {graph_id}")
        
        node = LineageNode(
            name=name,
            type=node_type,
            description=description,
            metadata=metadata or {}
        )
        
        graph = self.active_graphs[graph_id]
        graph.nodes.append(node)
        self.node_registry[node.id] = node
        
        logger.debug(f"Added {node_type} node: {name} to graph {graph_id}")
        return node
    
    def add_transformation_edge(
        self,
        graph_id: str,
        source_node_id: str,
        target_node_id: str,
        transformation_type: str,
        transformation_details: Optional[Dict[str, Any]] = None
    ) -> LineageEdge:
        """
        Add a transformation edge between nodes.
        
        Args:
            graph_id: Graph ID
            source_node_id: Source node ID
            target_node_id: Target node ID
            transformation_type: Type of transformation
            transformation_details: Transformation details
            
        Returns:
            LineageEdge: Created edge
        """
        if graph_id not in self.active_graphs:
            raise LineageTrackingError(f"Graph not found: {graph_id}")
        
        # Validate nodes exist
        if source_node_id not in self.node_registry:
            raise LineageTrackingError(f"Source node not found: {source_node_id}")
        
        if target_node_id not in self.node_registry:
            raise LineageTrackingError(f"Target node not found: {target_node_id}")
        
        edge = LineageEdge(
            source_node_id=source_node_id,
            target_node_id=target_node_id,
            transformation_type=transformation_type,
            transformation_details=transformation_details or {}
        )
        
        graph = self.active_graphs[graph_id]
        graph.edges.append(edge)
        self.edge_registry[edge.id] = edge
        
        logger.debug(f"Added transformation edge: {transformation_type} from {source_node_id} to {target_node_id}")
        return edge
    
    def track_cv_processing_lineage(
        self,
        cv_id: str,
        user_id: str,
        processing_steps: List[Dict[str, Any]]
    ) -> LineageGraph:
        """
        Track complete CV processing lineage.
        
        Args:
            cv_id: CV identifier
            user_id: User identifier
            processing_steps: List of processing steps
            
        Returns:
            LineageGraph: Complete lineage graph
        """
        try:
            # Create lineage graph for CV processing
            graph = self.create_lineage_graph(
                name=f"CV_Processing_{cv_id}",
                metadata={
                    "cv_id": cv_id,
                    "user_id": user_id,
                    "processing_type": "cv_generation",
                    "started_at": datetime.utcnow().isoformat()
                }
            )
            
            previous_node = None
            
            for i, step in enumerate(processing_steps):
                step_type = step.get("type", "unknown")
                step_name = step.get("name", f"Step_{i+1}")
                step_description = step.get("description", "")
                step_metadata = step.get("metadata", {})
                
                # Determine node type
                if i == 0:
                    node_type = "source"
                elif i == len(processing_steps) - 1:
                    node_type = "destination"
                else:
                    node_type = "transformation"
                
                # Add node
                node = self._add_node(
                    graph.id,
                    step_name,
                    node_type,
                    step_description,
                    {
                        **step_metadata,
                        "step_index": i,
                        "step_type": step_type,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )
                
                # Add edge from previous node
                if previous_node:
                    self.add_transformation_edge(
                        graph.id,
                        previous_node.id,
                        node.id,
                        step_type,
                        {
                            "input_data": step.get("input_data", {}),
                            "output_data": step.get("output_data", {}),
                            "parameters": step.get("parameters", {}),
                            "duration_ms": step.get("duration_ms", 0),
                            "success": step.get("success", True),
                            "error": step.get("error")
                        }
                    )
                
                previous_node = node
            
            logger.info(f"Tracked CV processing lineage: {cv_id} with {len(processing_steps)} steps")
            return graph
            
        except Exception as e:
            logger.error(f"Failed to track CV processing lineage: {e}")
            raise LineageTrackingError(
                f"Failed to track CV processing lineage: {str(e)}",
                error_code="LINEAGE_TRACKING_FAILED"
            )
    
    def get_lineage_graph(self, graph_id: str) -> Optional[LineageGraph]:
        """Get lineage graph by ID."""
        return self.active_graphs.get(graph_id)
    
    def get_node_lineage(self, node_id: str) -> Dict[str, Any]:
        """
        Get complete lineage for a specific node.
        
        Args:
            node_id: Node ID
            
        Returns:
            Dict: Node lineage information
        """
        if node_id not in self.node_registry:
            raise LineageTrackingError(f"Node not found: {node_id}")
        
        node = self.node_registry[node_id]
        
        # Find upstream and downstream nodes
        upstream_nodes = []
        downstream_nodes = []
        
        for edge in self.edge_registry.values():
            if edge.target_node_id == node_id:
                upstream_nodes.append({
                    "node": self.node_registry.get(edge.source_node_id),
                    "edge": edge
                })
            elif edge.source_node_id == node_id:
                downstream_nodes.append({
                    "node": self.node_registry.get(edge.target_node_id),
                    "edge": edge
                })
        
        return {
            "node": node,
            "upstream": upstream_nodes,
            "downstream": downstream_nodes,
            "lineage_depth": self._calculate_lineage_depth(node_id)
        }
    
    def _calculate_lineage_depth(self, node_id: str) -> Dict[str, int]:
        """Calculate lineage depth (upstream and downstream)."""
        upstream_depth = 0
        downstream_depth = 0
        
        # Calculate upstream depth
        visited = set()
        current_nodes = [node_id]
        
        while current_nodes:
            next_nodes = []
            for current_node in current_nodes:
                if current_node in visited:
                    continue
                visited.add(current_node)
                
                for edge in self.edge_registry.values():
                    if edge.target_node_id == current_node and edge.source_node_id not in visited:
                        next_nodes.append(edge.source_node_id)
            
            if next_nodes:
                upstream_depth += 1
                current_nodes = next_nodes
            else:
                break
        
        # Calculate downstream depth
        visited = set()
        current_nodes = [node_id]
        
        while current_nodes:
            next_nodes = []
            for current_node in current_nodes:
                if current_node in visited:
                    continue
                visited.add(current_node)
                
                for edge in self.edge_registry.values():
                    if edge.source_node_id == current_node and edge.target_node_id not in visited:
                        next_nodes.append(edge.target_node_id)
            
            if next_nodes:
                downstream_depth += 1
                current_nodes = next_nodes
            else:
                break
        
        return {
            "upstream_depth": upstream_depth,
            "downstream_depth": downstream_depth
        }
    
    def generate_impact_analysis(self, node_id: str) -> Dict[str, Any]:
        """
        Generate impact analysis for a node.
        
        Args:
            node_id: Node ID to analyze
            
        Returns:
            Dict: Impact analysis results
        """
        lineage = self.get_node_lineage(node_id)
        
        # Count affected nodes
        affected_downstream = len(lineage["downstream"])
        affected_upstream = len(lineage["upstream"])
        
        # Analyze transformation types
        transformation_types = set()
        for downstream in lineage["downstream"]:
            transformation_types.add(downstream["edge"].transformation_type)
        
        return {
            "node_id": node_id,
            "node_name": lineage["node"].name,
            "impact_scope": {
                "upstream_dependencies": affected_upstream,
                "downstream_impacts": affected_downstream,
                "total_affected": affected_upstream + affected_downstream
            },
            "transformation_types": list(transformation_types),
            "lineage_depth": lineage["lineage_depth"],
            "risk_level": self._assess_impact_risk(affected_downstream, lineage["lineage_depth"])
        }
    
    def _assess_impact_risk(self, downstream_count: int, depth_info: Dict[str, int]) -> str:
        """Assess impact risk level."""
        total_depth = depth_info["upstream_depth"] + depth_info["downstream_depth"]
        
        if downstream_count > 10 or total_depth > 5:
            return "high"
        elif downstream_count > 5 or total_depth > 3:
            return "medium"
        else:
            return "low"
    
    def export_lineage_graph(self, graph_id: str) -> Dict[str, Any]:
        """Export lineage graph for visualization."""
        if graph_id not in self.active_graphs:
            raise LineageTrackingError(f"Graph not found: {graph_id}")
        
        graph = self.active_graphs[graph_id]
        
        return {
            "graph": graph.model_dump(),
            "visualization_data": {
                "nodes": [
                    {
                        "id": node.id,
                        "label": node.name,
                        "type": node.type,
                        "metadata": node.metadata
                    }
                    for node in graph.nodes
                ],
                "edges": [
                    {
                        "id": edge.id,
                        "source": edge.source_node_id,
                        "target": edge.target_node_id,
                        "label": edge.transformation_type,
                        "metadata": edge.transformation_details
                    }
                    for edge in graph.edges
                ]
            }
        }


# Global lineage tracker instance
lineage_tracker = LineageTracker()
