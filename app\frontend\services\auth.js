// Authentication service
import config from '../config.js';
import { measurePerformance } from '../utils.js';
import apiService from './api.js';
import cacheService from './cache.js';

class AuthService {
    constructor() {
        this.token = null;
        this.user = null;
        this.subscribers = new Set();
        this.tokenKey = 'auth_token';
        this.userKey = 'auth_user';
    }

    /**
     * Subscribe to auth state changes
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of auth state changes
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in auth subscriber:', error);
            }
        });
    }

    /**
     * Initialize the auth service
     */
    async initialize() {
        // Load token and user from cache
        this.token = await cacheService.get(this.tokenKey);
        this.user = await cacheService.get(this.userKey);

        if (this.token) {
            // Validate token
            try {
                await this.validateToken();
            } catch (error) {
                await this.logout();
            }
        }
    }

    /**
     * Login with credentials
     * @param {string} email - The user's email
     * @param {string} password - The user's password
     * @returns {Promise<Object>} The user object
     */
    async login(email, password) {
        return measurePerformance('auth_login', async () => {
            try {
                const response = await apiService.request('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({ email, password }),
                });

                this.token = response.token;
                this.user = response.user;

                // Cache auth data
                await cacheService.set(this.tokenKey, this.token);
                await cacheService.set(this.userKey, this.user);

                // Notify subscribers
                this.notifySubscribers('login', this.user);

                return this.user;
            } catch (error) {
                throw new Error('Invalid credentials');
            }
        });
    }

    /**
     * Logout the current user
     */
    async logout() {
        if (this.token) {
            try {
                await apiService.request('/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                    },
                });
            } catch (error) {
                console.error('Error during logout:', error);
            }
        }

        // Clear auth data
        this.token = null;
        this.user = null;
        await cacheService.remove(this.tokenKey);
        await cacheService.remove(this.userKey);

        // Notify subscribers
        this.notifySubscribers('logout', null);
    }

    /**
     * Register a new user
     * @param {Object} userData - The user data
     * @returns {Promise<Object>} The user object
     */
    async register(userData) {
        return measurePerformance('auth_register', async () => {
            try {
                const response = await apiService.request('/auth/register', {
                    method: 'POST',
                    body: JSON.stringify(userData),
                });

                this.token = response.token;
                this.user = response.user;

                // Cache auth data
                await cacheService.set(this.tokenKey, this.token);
                await cacheService.set(this.userKey, this.user);

                // Notify subscribers
                this.notifySubscribers('register', this.user);

                return this.user;
            } catch (error) {
                throw new Error('Registration failed');
            }
        });
    }

    /**
     * Validate the current token
     * @returns {Promise<boolean>} Whether the token is valid
     */
    async validateToken() {
        if (!this.token) return false;

        try {
            const response = await apiService.request('/auth/validate', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                },
            });

            this.user = response.user;
            await cacheService.set(this.userKey, this.user);

            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Get the current user
     * @returns {Object|null} The current user
     */
    getUser() {
        return this.user;
    }

    /**
     * Get the current token
     * @returns {string|null} The current token
     */
    getToken() {
        return this.token;
    }

    /**
     * Check if the user is authenticated
     * @returns {boolean} Whether the user is authenticated
     */
    isAuthenticated() {
        return !!this.token && !!this.user;
    }

    /**
     * Check if the user has a specific role
     * @param {string} role - The role to check
     * @returns {boolean} Whether the user has the role
     */
    hasRole(role) {
        return this.user?.roles?.includes(role) || false;
    }

    /**
     * Check if the user has a specific permission
     * @param {string} permission - The permission to check
     * @returns {boolean} Whether the user has the permission
     */
    hasPermission(permission) {
        return this.user?.permissions?.includes(permission) || false;
    }

    /**
     * Update the user's profile
     * @param {Object} userData - The updated user data
     * @returns {Promise<Object>} The updated user object
     */
    async updateProfile(userData) {
        if (!this.isAuthenticated()) {
            throw new Error('Not authenticated');
        }

        try {
            const response = await apiService.request('/auth/profile', {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                },
                body: JSON.stringify(userData),
            });

            this.user = response.user;
            await cacheService.set(this.userKey, this.user);

            // Notify subscribers
            this.notifySubscribers('profile_update', this.user);

            return this.user;
        } catch (error) {
            throw new Error('Profile update failed');
        }
    }

    /**
     * Change the user's password
     * @param {string} currentPassword - The current password
     * @param {string} newPassword - The new password
     * @returns {Promise<void>}
     */
    async changePassword(currentPassword, newPassword) {
        if (!this.isAuthenticated()) {
            throw new Error('Not authenticated');
        }

        try {
            await apiService.request('/auth/change-password', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                },
                body: JSON.stringify({
                    currentPassword,
                    newPassword,
                }),
            });

            // Notify subscribers
            this.notifySubscribers('password_change', null);
        } catch (error) {
            throw new Error('Password change failed');
        }
    }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService; 