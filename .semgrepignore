# ImpactCV Semgrep Ignore Configuration
# Files and directories to exclude from Semgrep security scanning

# ============================================================================
# DEVELOPMENT DIRECTORIES
# ============================================================================
# Virtual environments
venv/
.venv/
env/
.env/
virtualenv/

# Node modules
node_modules/

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# Build directories
build/
dist/
*.egg-info/
.eggs/

# ============================================================================
# TEST DIRECTORIES
# ============================================================================
# Test files and directories
tests/
test/
*_test.py
test_*.py
**/test_*
**/tests/*

# Test artifacts
.pytest_cache/
.coverage
htmlcov/
coverage-reports/
test-results/
test-reports/

# ============================================================================
# DOCUMENTATION
# ============================================================================
docs/
*.md
*.rst
*.txt
LICENSE
CHANGELOG*
README*

# ============================================================================
# CONFIGURATION FILES
# ============================================================================
# Configuration that may contain patterns that trigger false positives
*.ini
*.cfg
*.conf
*.yaml
*.yml
*.json
*.toml

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
Jenkinsfile

# ============================================================================
# DATABASE MIGRATIONS
# ============================================================================
# Database migration files often contain SQL that triggers false positives
migrations/
alembic/versions/
**/migrations/*
**/alembic/versions/*

# ============================================================================
# STATIC FILES
# ============================================================================
# Static assets
static/
assets/
media/
uploads/

# Frontend files
*.css
*.scss
*.sass
*.less
*.js
*.ts
*.jsx
*.tsx
*.vue
*.html
*.htm

# ============================================================================
# GENERATED FILES
# ============================================================================
# Auto-generated files
*_pb2.py
*_pb2_grpc.py
*.proto

# Generated API clients
**/generated/*
**/gen/*
**/auto_generated/*

# ============================================================================
# THIRD-PARTY CODE
# ============================================================================
# Vendor directories
vendor/
third_party/
external/
lib/
libs/

# Downloaded dependencies
requirements/
pip-wheel-metadata/

# ============================================================================
# LOGS AND TEMPORARY FILES
# ============================================================================
# Log files
*.log
logs/
log/

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.swp
*.swo
*~

# ============================================================================
# VERSION CONTROL
# ============================================================================
.git/
.svn/
.hg/
.bzr/

# ============================================================================
# IDE AND EDITOR FILES
# ============================================================================
# IDEs
.vscode/
.idea/
*.sublime-*
.eclipse/

# Editor files
*.vim
.netrwhist

# ============================================================================
# SECURITY SCAN RESULTS
# ============================================================================
# Security scan outputs
bandit-report.*
semgrep-report.*
safety-report.*
security-reports/
scan-results/

# ============================================================================
# CERTIFICATES AND KEYS
# ============================================================================
# Certificate files (these should be scanned but may contain patterns)
# that trigger false positives in test certificates
*.pem
*.key
*.crt
*.cer
*.p12
*.pfx

# ============================================================================
# DATA FILES
# ============================================================================
# Data files that may contain patterns triggering false positives
*.csv
*.tsv
*.xlsx
*.xls
*.pdf
*.doc
*.docx

# Sample data
sample_data/
test_data/
fixtures/
mock_data/

# ============================================================================
# BACKUP FILES
# ============================================================================
*.bak
*.backup
*.old
backup_*/

# ============================================================================
# SPECIFIC FILE PATTERNS
# ============================================================================
# Files that commonly have false positives

# OpenAPI/Swagger specs (contain example data)
openapi.json
swagger.json
api-spec.json

# Example configuration files
example.*
sample.*
template.*
*.example
*.sample
*.template

# ============================================================================
# ENVIRONMENT-SPECIFIC IGNORES
# ============================================================================

# Development environment files
.env.local
.env.development
.env.dev

# Local configuration overrides
local_settings.py
settings_local.py
config_local.py

# ============================================================================
# PERFORMANCE OPTIMIZATION
# ============================================================================
# Large files that slow down scanning
*.min.js
*.min.css
*.bundle.js
*.bundle.css

# Binary files
*.exe
*.dll
*.so
*.dylib
*.bin
*.dat

# Image files
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.svg
*.ico
*.webp

# Archive files
*.zip
*.tar
*.gz
*.bz2
*.xz
*.7z
*.rar

# ============================================================================
# CUSTOM EXCLUSIONS
# ============================================================================
# Add project-specific exclusions here

# ImpactCV specific exclusions
# (Add any project-specific files or patterns that should be ignored)
