// Notification service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class NotificationService {
    constructor() {
        this.notifications = [];
        this.subscribers = new Set();
        this.maxNotifications = config.notifications?.maxNotifications || 5;
        this.defaultDuration = config.notifications?.defaultDuration || 5000;
    }

    /**
     * Subscribe to notification changes
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of notification changes
     */
    notifySubscribers() {
        this.subscribers.forEach(callback => {
            try {
                callback(this.notifications);
            } catch (error) {
                console.error('Error in notification subscriber:', error);
            }
        });
    }

    /**
     * Add a notification
     * @param {Object} notification - The notification object
     * @param {string} notification.type - The notification type (success, error, warning, info)
     * @param {string} notification.message - The notification message
     * @param {number} [notification.duration] - The notification duration in milliseconds
     * @returns {string} The notification ID
     */
    add(notification) {
        return measurePerformance('notification_add', () => {
            const id = Date.now().toString();
            const duration = notification.duration || this.defaultDuration;

            const newNotification = {
                id,
                type: notification.type || 'info',
                message: notification.message,
                duration,
                timestamp: Date.now(),
            };

            // Add notification to the beginning of the array
            this.notifications.unshift(newNotification);

            // Limit the number of notifications
            if (this.notifications.length > this.maxNotifications) {
                this.notifications.pop();
            }

            // Notify subscribers
            this.notifySubscribers();

            // Auto-remove notification after duration
            if (duration > 0) {
                setTimeout(() => {
                    this.remove(id);
                }, duration);
            }

            return id;
        });
    }

    /**
     * Remove a notification
     * @param {string} id - The notification ID
     */
    remove(id) {
        const index = this.notifications.findIndex(n => n.id === id);
        if (index !== -1) {
            this.notifications.splice(index, 1);
            this.notifySubscribers();
        }
    }

    /**
     * Clear all notifications
     */
    clear() {
        this.notifications = [];
        this.notifySubscribers();
    }

    /**
     * Get all notifications
     * @returns {Array} The notifications array
     */
    getAll() {
        return this.notifications;
    }

    /**
     * Add a success notification
     * @param {string} message - The notification message
     * @param {number} [duration] - The notification duration
     * @returns {string} The notification ID
     */
    success(message, duration) {
        return this.add({
            type: 'success',
            message,
            duration,
        });
    }

    /**
     * Add an error notification
     * @param {string} message - The notification message
     * @param {number} [duration] - The notification duration
     * @returns {string} The notification ID
     */
    error(message, duration) {
        return this.add({
            type: 'error',
            message,
            duration,
        });
    }

    /**
     * Add a warning notification
     * @param {string} message - The notification message
     * @param {number} [duration] - The notification duration
     * @returns {string} The notification ID
     */
    warning(message, duration) {
        return this.add({
            type: 'warning',
            message,
            duration,
        });
    }

    /**
     * Add an info notification
     * @param {string} message - The notification message
     * @param {number} [duration] - The notification duration
     * @returns {string} The notification ID
     */
    info(message, duration) {
        return this.add({
            type: 'info',
            message,
            duration,
        });
    }

    /**
     * Get notifications by type
     * @param {string} type - The notification type
     * @returns {Array} The filtered notifications
     */
    getByType(type) {
        return this.notifications.filter(n => n.type === type);
    }

    /**
     * Get the number of notifications
     * @returns {number} The number of notifications
     */
    getCount() {
        return this.notifications.length;
    }

    /**
     * Get the number of notifications by type
     * @param {string} type - The notification type
     * @returns {number} The number of notifications of the specified type
     */
    getCountByType(type) {
        return this.notifications.filter(n => n.type === type).length;
    }
}

// Create and export a singleton instance
const notificationService = new NotificationService();
export default notificationService; 