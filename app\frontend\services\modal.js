// Modal service
import config from '../config.js';
import { measurePerformance } from '../utils.js';
import notificationService from './notification.js';

class ModalService {
    constructor() {
        this.modals = new Map();
        this.activeModal = null;
        this.subscribers = new Set();
        this.backdrop = null;
        this.initializeBackdrop();
    }

    /**
     * Initialize the backdrop element
     */
    initializeBackdrop() {
        this.backdrop = document.createElement('div');
        this.backdrop.className = 'modal-backdrop';
        this.backdrop.style.display = 'none';
        document.body.appendChild(this.backdrop);
    }

    /**
     * Subscribe to modal events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of modal events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in modal subscriber:', error);
            }
        });
    }

    /**
     * Register a modal
     * @param {string} id - The modal ID
     * @param {Object} options - The modal options
     */
    register(id, options = {}) {
        this.modals.set(id, {
            ...options,
            element: document.getElementById(id),
        });
    }

    /**
     * Show a modal
     * @param {string} id - The modal ID
     * @param {Object} [data] - The modal data
     */
    show(id, data = {}) {
        return measurePerformance('modal_show', () => {
            const modal = this.modals.get(id);
            if (!modal) {
                console.error(`Modal ${id} not found`);
                return;
            }

            if (this.activeModal) {
                this.hide(this.activeModal);
            }

            modal.element.style.display = 'block';
            this.backdrop.style.display = 'block';
            this.activeModal = id;

            if (modal.onShow) {
                modal.onShow(data);
            }

            this.notifySubscribers('show', { id, data });
        });
    }

    /**
     * Hide a modal
     * @param {string} id - The modal ID
     */
    hide(id) {
        return measurePerformance('modal_hide', () => {
            const modal = this.modals.get(id);
            if (!modal) {
                console.error(`Modal ${id} not found`);
                return;
            }

            modal.element.style.display = 'none';
            this.backdrop.style.display = 'none';
            this.activeModal = null;

            if (modal.onHide) {
                modal.onHide();
            }

            this.notifySubscribers('hide', { id });
        });
    }

    /**
     * Toggle a modal
     * @param {string} id - The modal ID
     * @param {Object} [data] - The modal data
     */
    toggle(id, data = {}) {
        if (this.activeModal === id) {
            this.hide(id);
        } else {
            this.show(id, data);
        }
    }

    /**
     * Get the active modal
     * @returns {string|null} The active modal ID
     */
    getActiveModal() {
        return this.activeModal;
    }

    /**
     * Check if a modal is active
     * @param {string} id - The modal ID
     * @returns {boolean} Whether the modal is active
     */
    isActive(id) {
        return this.activeModal === id;
    }

    /**
     * Get modal data
     * @param {string} id - The modal ID
     * @returns {Object} The modal data
     */
    getModalData(id) {
        return this.modals.get(id);
    }

    /**
     * Update modal data
     * @param {string} id - The modal ID
     * @param {Object} data - The new modal data
     */
    updateModalData(id, data) {
        const modal = this.modals.get(id);
        if (modal) {
            Object.assign(modal, data);
        }
    }

    /**
     * Initialize all modals
     */
    initialize() {
        this.modals.forEach((modal, id) => {
            if (modal.element) {
                modal.element.style.display = 'none';
            }
        });
    }
}

// Create and export a singleton instance
const modalService = new ModalService();
export default modalService; 