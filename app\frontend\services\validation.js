// Form validation service
import config from '../config.js';
import { sanitizeInput } from '../utils.js';

class ValidationService {
    constructor() {
        this.maxLengths = config.ui.maxInputLength;
        this.rules = {
            required: value => !!value || 'This field is required',
            maxLength: (value, max) => value.length <= max || `Maximum length is ${max} characters`,
            minLength: (value, min) => value.length >= min || `Minimum length is ${min} characters`,
            email: value => {
                const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return !value || pattern.test(value) || 'Invalid email address';
            },
            url: value => {
                const pattern = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
                return !value || pattern.test(value) || 'Invalid URL';
            },
            number: value => !isNaN(value) || 'Must be a number',
            integer: value => Number.isInteger(Number(value)) || 'Must be an integer',
            positive: value => Number(value) > 0 || 'Must be a positive number',
            date: value => {
                const date = new Date(value);
                return !value || !isNaN(date.getTime()) || 'Invalid date';
            },
            pattern: (value, pattern) => {
                const regex = new RegExp(pattern);
                return !value || regex.test(value) || 'Invalid format';
            },
        };
    }

    /**
     * Validate a field value
     * @param {string} field - The field name
     * @param {any} value - The value to validate
     * @param {Object} rules - The validation rules
     * @returns {string|null} Error message or null if valid
     */
    validateField(field, value, rules = {}) {
        // Apply default rules based on field type
        const defaultRules = this.getDefaultRules(field);
        const allRules = { ...defaultRules, ...rules };

        // Check each rule
        for (const [rule, params] of Object.entries(allRules)) {
            if (this.rules[rule]) {
                const error = this.rules[rule](value, params);
                if (error !== true) {
                    return error;
                }
            }
        }

        return null;
    }

    /**
     * Validate a form
     * @param {Object} formData - The form data
     * @param {Object} rules - The validation rules
     * @returns {Object} Validation results
     */
    validateForm(formData, rules = {}) {
        const errors = {};
        let isValid = true;

        for (const [field, value] of Object.entries(formData)) {
            const fieldRules = rules[field] || {};
            const error = this.validateField(field, value, fieldRules);
            
            if (error) {
                errors[field] = error;
                isValid = false;
            }
        }

        return {
            isValid,
            errors,
        };
    }

    /**
     * Get default validation rules for a field
     * @param {string} field - The field name
     * @returns {Object} Default validation rules
     */
    getDefaultRules(field) {
        const rules = {
            required: true,
        };

        // Add max length rule if configured
        if (this.maxLengths[field]) {
            rules.maxLength = this.maxLengths[field];
        }

        // Add type-specific rules
        switch (field) {
            case 'email':
                rules.email = true;
                break;
            case 'url':
                rules.url = true;
                break;
            case 'age':
            case 'years':
                rules.number = true;
                rules.positive = true;
                rules.integer = true;
                break;
            case 'date':
                rules.date = true;
                break;
        }

        return rules;
    }

    /**
     * Sanitize form data
     * @param {Object} formData - The form data to sanitize
     * @returns {Object} Sanitized form data
     */
    sanitizeFormData(formData) {
        const sanitized = {};

        for (const [field, value] of Object.entries(formData)) {
            if (typeof value === 'string') {
                sanitized[field] = sanitizeInput(value);
            } else {
                sanitized[field] = value;
            }
        }

        return sanitized;
    }

    /**
     * Add a custom validation rule
     * @param {string} name - The rule name
     * @param {Function} validator - The validation function
     */
    addRule(name, validator) {
        this.rules[name] = validator;
    }

    /**
     * Remove a validation rule
     * @param {string} name - The rule name
     */
    removeRule(name) {
        delete this.rules[name];
    }
}

// Create and export a singleton instance
const validationService = new ValidationService();
export default validationService; 