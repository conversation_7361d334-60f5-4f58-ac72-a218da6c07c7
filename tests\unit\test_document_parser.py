"""
Unit tests for Document Parser Service
"""

import os
import tempfile
import pytest
from unittest.mock import Mock, patch, mock_open

from app.services.document_parser import (
    DocumentParser,
    DocumentContent,
    DocumentParsingError,
    document_parser
)


class TestDocumentParser:
    """Test cases for DocumentParser class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.parser = DocumentParser()
    
    def test_init(self):
        """Test parser initialization."""
        assert self.parser.max_file_size == 50 * 1024 * 1024
        assert self.parser.allowed_extensions == {'.pdf', '.docx', '.doc', '.txt'}
        assert self.parser.max_pages == 1000
        assert self.parser.max_text_length == 10 * 1024 * 1024
    
    def test_detect_file_type_pdf(self):
        """Test PDF file type detection."""
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            file_type = self.parser._detect_file_type(tmp_path)
            assert file_type == 'application/pdf'
        finally:
            os.unlink(tmp_path)
    
    def test_detect_file_type_docx(self):
        """Test DOCX file type detection."""
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            file_type = self.parser._detect_file_type(tmp_path)
            assert file_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        finally:
            os.unlink(tmp_path)
    
    def test_detect_file_type_txt(self):
        """Test TXT file type detection."""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            file_type = self.parser._detect_file_type(tmp_path)
            assert file_type == 'text/plain'
        finally:
            os.unlink(tmp_path)
    
    def test_validate_file_not_found(self):
        """Test validation with non-existent file."""
        with pytest.raises(DocumentParsingError) as exc_info:
            self.parser._validate_file('/non/existent/file.pdf')
        
        assert exc_info.value.error_code == "FILE_NOT_FOUND"
        assert "File not found" in str(exc_info.value)
    
    def test_validate_file_too_large(self):
        """Test validation with oversized file."""
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            # Write data larger than max_file_size
            large_data = b'x' * (self.parser.max_file_size + 1)
            tmp.write(large_data)
            tmp_path = tmp.name
        
        try:
            with pytest.raises(DocumentParsingError) as exc_info:
                self.parser._validate_file(tmp_path)
            
            assert exc_info.value.error_code == "FILE_TOO_LARGE"
            assert "File too large" in str(exc_info.value)
        finally:
            os.unlink(tmp_path)
    
    def test_validate_file_invalid_extension(self):
        """Test validation with invalid file extension."""
        with tempfile.NamedTemporaryFile(suffix='.exe', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            with pytest.raises(DocumentParsingError) as exc_info:
                self.parser._validate_file(tmp_path)
            
            assert exc_info.value.error_code == "EXTENSION_NOT_ALLOWED"
            assert "File extension not allowed" in str(exc_info.value)
        finally:
            os.unlink(tmp_path)
    
    def test_parse_text_file(self):
        """Test parsing plain text file."""
        test_content = "This is a test CV.\nName: John Doe\nExperience: Software Engineer"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
            tmp.write(test_content)
            tmp_path = tmp.name
        
        try:
            result = self.parser._parse_text(tmp_path)
            
            assert isinstance(result, DocumentContent)
            assert result.text == test_content
            assert len(result.pages) == 1
            assert result.pages[0]['page_number'] == 1
            assert result.pages[0]['word_count'] == len(test_content.split())
            assert result.formatting['text_length'] == len(test_content)
            assert result.metadata['encoding'] == 'utf-8'
        finally:
            os.unlink(tmp_path)
    
    def test_parse_text_file_too_long(self):
        """Test parsing text file that exceeds max length."""
        # Create text longer than max_text_length
        long_text = 'x' * (self.parser.max_text_length + 1000)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
            tmp.write(long_text)
            tmp_path = tmp.name
        
        try:
            result = self.parser._parse_text(tmp_path)
            
            # Should be truncated to max_text_length
            assert len(result.text) == self.parser.max_text_length
        finally:
            os.unlink(tmp_path)
    
    @patch('fitz.open')
    def test_parse_pdf_success(self, mock_fitz_open):
        """Test successful PDF parsing."""
        # Mock PDF document
        mock_doc = Mock()
        mock_doc.page_count = 2
        mock_doc.is_encrypted = False
        mock_doc.pdf_version.return_value = (1, 7)
        mock_doc.metadata = {
            'title': 'Test CV',
            'author': 'John Doe',
            'subject': 'Resume',
            'creator': 'Test Creator',
            'producer': 'Test Producer',
            'creationDate': '2023-01-01',
            'modDate': '2023-01-02'
        }
        
        # Mock pages
        mock_page1 = Mock()
        # Configure get_text to return different values based on argument
        def get_text_page1(format_type=None):
            if format_type == "dict":
                return {"blocks": [{"lines": [{"spans": [{"text": "Page 1 content"}]}]}]}
            return "Page 1 content"
        mock_page1.get_text.side_effect = get_text_page1
        mock_page1.get_images.return_value = []
        mock_page1.rect = [0, 0, 612, 792]
        mock_page1.rotation = 0

        mock_page2 = Mock()
        # Configure get_text to return different values based on argument
        def get_text_page2(format_type=None):
            if format_type == "dict":
                return {"blocks": [{"lines": [{"spans": [{"text": "Page 2 content"}]}]}]}
            return "Page 2 content"
        mock_page2.get_text.side_effect = get_text_page2
        mock_page2.get_images.return_value = [(1, 0, 100, 100, 8, 'DeviceRGB', '', 'Im1', 'DCTDecode')]
        mock_page2.rect = [0, 0, 612, 792]
        mock_page2.rotation = 0
        
        # Configure mock to support indexing and iteration
        mock_doc.__getitem__ = Mock(side_effect=[mock_page1, mock_page2])
        mock_doc.__iter__ = Mock(return_value=iter([mock_page1, mock_page2]))
        mock_doc.close = Mock()
        
        mock_fitz_open.return_value = mock_doc
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            result = self.parser._parse_pdf(tmp_path)
            
            assert isinstance(result, DocumentContent)
            assert "Page 1 content" in result.text
            assert "Page 2 content" in result.text
            assert len(result.pages) == 2
            assert result.metadata['page_count'] == 2
            assert result.metadata['title'] == 'Test CV'
            assert result.metadata['author'] == 'John Doe'
            assert result.formatting['has_images'] == True
            assert result.formatting['image_count'] == 1
            assert len(result.images) == 1
            
            mock_doc.close.assert_called_once()
        finally:
            os.unlink(tmp_path)
    
    @patch('fitz.open')
    def test_parse_pdf_too_many_pages(self, mock_fitz_open):
        """Test PDF with too many pages."""
        mock_doc = Mock()
        mock_doc.page_count = self.parser.max_pages + 1
        mock_fitz_open.return_value = mock_doc
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            with pytest.raises(DocumentParsingError) as exc_info:
                self.parser._parse_pdf(tmp_path)
            
            assert exc_info.value.error_code == "TOO_MANY_PAGES"
            assert "too many pages" in str(exc_info.value)
        finally:
            os.unlink(tmp_path)
    
    @patch('docx.Document')
    def test_parse_docx_success(self, mock_docx_document):
        """Test successful DOCX parsing."""
        # Mock document
        mock_doc = Mock()
        
        # Mock paragraphs
        mock_para1 = Mock()
        mock_para1.text = "John Doe"
        mock_para1.style.name = "Heading 1"
        
        mock_para2 = Mock()
        mock_para2.text = "Software Engineer with 5 years experience"
        mock_para2.style.name = "Normal"
        
        mock_doc.paragraphs = [mock_para1, mock_para2]
        
        # Mock tables
        mock_table = Mock()
        mock_row = Mock()
        mock_cell1 = Mock()
        mock_cell1.text = "Experience"
        mock_cell2 = Mock()
        mock_cell2.text = "5 years"
        mock_row.cells = [mock_cell1, mock_cell2]
        mock_table.rows = [mock_row]
        mock_doc.tables = [mock_table]
        
        # Mock core properties
        mock_props = Mock()
        mock_props.title = "John Doe CV"
        mock_props.author = "John Doe"
        mock_props.subject = "Resume"
        mock_props.keywords = "software, engineer"
        mock_props.created = None
        mock_props.modified = None
        mock_props.last_modified_by = "John"
        mock_doc.core_properties = mock_props
        
        mock_docx_document.return_value = mock_doc
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            result = self.parser._parse_docx(tmp_path)
            
            assert isinstance(result, DocumentContent)
            assert "John Doe" in result.text
            assert "Software Engineer" in result.text
            assert "Experience" in result.text
            assert len(result.pages) == 1
            assert result.metadata['title'] == "John Doe CV"
            assert result.metadata['author'] == "John Doe"
            assert result.metadata['table_count'] == 1
            assert result.formatting['has_tables'] == True
        finally:
            os.unlink(tmp_path)
    
    def test_parse_from_bytes(self):
        """Test parsing document from bytes."""
        test_content = "Test CV content"
        test_bytes = test_content.encode('utf-8')
        
        with patch.object(self.parser, 'parse_document') as mock_parse:
            mock_result = DocumentContent(
                text=test_content,
                metadata={},
                pages=[],
                formatting={},
                images=[]
            )
            mock_parse.return_value = mock_result
            
            result = self.parser.parse_from_bytes(test_bytes, "test.txt")
            
            assert result == mock_result
            mock_parse.assert_called_once()
    
    def test_extract_cv_sections(self):
        """Test CV section extraction."""
        cv_text = """
        John Doe
        Software Engineer
        
        SUMMARY
        Experienced software engineer with 5 years in web development.
        
        EXPERIENCE
        Senior Developer at Tech Corp (2020-2023)
        - Led development team
        - Built scalable applications
        
        EDUCATION
        BS Computer Science, University (2016-2020)
        
        SKILLS
        Python, JavaScript, React, Node.js
        
        CONTACT
        <EMAIL>
        (*************
        """
        
        content = DocumentContent(
            text=cv_text,
            metadata={},
            pages=[],
            formatting={},
            images=[]
        )
        
        sections = self.parser.extract_cv_sections(content)
        
        assert 'summary' in sections
        assert 'experience' in sections
        assert 'education' in sections
        assert 'skills' in sections
        assert 'contact' in sections
        
        assert "Experienced software engineer" in sections['summary']
        assert "Senior Developer" in sections['experience']
        assert "BS Computer Science" in sections['education']
        assert "Python, JavaScript" in sections['skills']
        assert "<EMAIL>" in sections['contact']
    
    def test_get_document_stats(self):
        """Test document statistics calculation."""
        content = DocumentContent(
            text="Test content with multiple words",
            metadata={'title': 'Test'},
            pages=[
                {'word_count': 5, 'page_number': 1},
                {'word_count': 3, 'page_number': 2}
            ],
            formatting={'has_tables': True},
            images=[{'page': 1, 'index': 0}]
        )
        
        stats = self.parser.get_document_stats(content)
        
        assert stats['total_pages'] == 2
        assert stats['total_words'] == 8
        assert stats['total_chars'] == len(content.text)
        assert stats['has_images'] == True
        assert stats['image_count'] == 1
        assert stats['metadata_fields'] == 1
        assert stats['formatting_preserved'] == True
    
    def test_parse_document_unsupported_format(self):
        """Test parsing unsupported file format."""
        with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as tmp:
            tmp_path = tmp.name
        
        try:
            with pytest.raises(DocumentParsingError) as exc_info:
                self.parser.parse_document(tmp_path, 'application/xyz')
            
            assert exc_info.value.error_code == "UNSUPPORTED_FORMAT"
            assert "Unsupported file type" in str(exc_info.value)
        finally:
            os.unlink(tmp_path)
    
    def test_global_parser_instance(self):
        """Test global parser instance."""
        assert document_parser is not None
        assert isinstance(document_parser, DocumentParser)


class TestDocumentContent:
    """Test cases for DocumentContent model."""
    
    def test_document_content_creation(self):
        """Test DocumentContent model creation."""
        content = DocumentContent(
            text="Test content",
            metadata={"title": "Test"},
            pages=[{"page_number": 1}],
            formatting={"has_images": False},
            images=[]
        )
        
        assert content.text == "Test content"
        assert content.metadata["title"] == "Test"
        assert len(content.pages) == 1
        assert content.formatting["has_images"] == False
        assert len(content.images) == 0
    
    def test_document_content_defaults(self):
        """Test DocumentContent model with defaults."""
        content = DocumentContent(text="Test")
        
        assert content.text == "Test"
        assert content.metadata == {}
        assert content.pages == []
        assert content.formatting == {}
        assert content.images == []


class TestDocumentParsingError:
    """Test cases for DocumentParsingError exception."""
    
    def test_error_creation(self):
        """Test error creation with all parameters."""
        error = DocumentParsingError(
            message="Test error",
            error_code="TEST_ERROR",
            details={"file": "test.pdf"}
        )
        
        assert str(error) == "Test error"
        assert error.error_code == "TEST_ERROR"
        assert error.details == {"file": "test.pdf"}
    
    def test_error_creation_minimal(self):
        """Test error creation with minimal parameters."""
        error = DocumentParsingError("Test error")
        
        assert str(error) == "Test error"
        assert error.error_code == "PARSING_ERROR"
        assert error.details == {}


# Integration tests
class TestDocumentParserIntegration:
    """Integration tests for document parser."""
    
    def test_parse_real_text_file(self):
        """Test parsing a real text file."""
        test_content = """John Doe
Software Engineer

SUMMARY
Experienced developer with expertise in Python and web technologies.

EXPERIENCE
Senior Software Engineer - Tech Corp (2020-2023)
- Developed scalable web applications
- Led team of 5 developers

Software Engineer - StartupXYZ (2018-2020)
- Built REST APIs using Python/Django
- Implemented CI/CD pipelines

EDUCATION
Bachelor of Science in Computer Science
University of Technology (2014-2018)
GPA: 3.8/4.0

SKILLS
Programming: Python, JavaScript, Java, Go
Web: React, Django, FastAPI, Node.js
Databases: PostgreSQL, MongoDB, Redis
Tools: Docker, Kubernetes, Git, Jenkins

CONTACT
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/johndoe
GitHub: github.com/johndoe
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as tmp:
            tmp.write(test_content)
            tmp_path = tmp.name
        
        try:
            parser = DocumentParser()
            result = parser.parse_document(tmp_path)
            
            # Verify basic parsing
            assert isinstance(result, DocumentContent)
            assert result.text == test_content
            assert len(result.pages) == 1
            
            # Verify section extraction
            sections = parser.extract_cv_sections(result)
            assert 'summary' in sections
            assert 'experience' in sections
            assert 'education' in sections
            assert 'skills' in sections
            assert 'contact' in sections
            
            # Verify content
            assert "Experienced developer" in sections['summary']
            assert "Senior Software Engineer" in sections['experience']
            assert "Bachelor of Science" in sections['education']
            assert "Python, JavaScript" in sections['skills']
            assert "<EMAIL>" in sections['contact']
            
            # Verify statistics
            stats = parser.get_document_stats(result)
            assert stats['total_pages'] == 1
            assert stats['total_words'] > 50
            assert stats['has_images'] == False
            
        finally:
            os.unlink(tmp_path)
