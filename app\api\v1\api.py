"""
ImpactCV API v1 Router
Main API router with all endpoint collections
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, health, rag, ai_test

# Create main API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(health.router, prefix="/health", tags=["Health"])
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(rag.router, prefix="/rag", tags=["rag"])
api_router.include_router(ai_test.router, prefix="/ai-test", tags=["AI Testing"])

# Additional routers will be added here as we implement them:
# api_router.include_router(cv.router, prefix="/cv", tags=["CV Generation"])
# api_router.include_router(users.router, prefix="/users", tags=["Users"])
# api_router.include_router(rag.router, prefix="/rag", tags=["RAG Pipeline"])
