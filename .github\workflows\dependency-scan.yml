name: 🔍 Dependency Vulnerability Scanning

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'requirements*.txt'
      - 'pyproject.toml'
      - 'setup.py'
      - 'Pipfile'
      - 'poetry.lock'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'requirements*.txt'
      - 'pyproject.toml'
      - 'setup.py'
      - 'Pipfile'
      - 'poetry.lock'
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.11'

jobs:
  # ============================================================================
  # DEPENDENCY VULNERABILITY SCANNING
  # ============================================================================
  dependency-scan:
    name: 🛡️ Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    permissions:
      security-events: write
      contents: read
      actions: read
    
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
      
      - name: 📦 Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install safety pip-audit cyclonedx-bom
      
      # ========================================================================
      # SAFETY VULNERABILITY SCANNING
      # ========================================================================
      - name: 🔍 Safety Vulnerability Scan
        run: |
          safety check --json --output safety-report.json || true
          safety check --short-report
        continue-on-error: true
      
      - name: 📤 Upload Safety Report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: safety-vulnerability-report
          path: safety-report.json
      
      # ========================================================================
      # PIP-AUDIT VULNERABILITY SCANNING
      # ========================================================================
      - name: 🔍 Pip-Audit Vulnerability Scan
        run: |
          pip-audit --format=sarif --output=pip-audit-results.sarif || true
          pip-audit --desc --format=json --output=pip-audit-report.json || true
          pip-audit --desc
        continue-on-error: true
      
      - name: 📤 Upload Pip-Audit SARIF Results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: pip-audit-results.sarif
          category: pip-audit
      
      - name: 📤 Upload Pip-Audit Report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: pip-audit-vulnerability-report
          path: pip-audit-report.json
      
      # ========================================================================
      # SNYK VULNERABILITY SCANNING
      # ========================================================================
      - name: 🔍 Snyk Vulnerability Scan
        uses: snyk/actions/python@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high --json-file-output=snyk-report.json
        continue-on-error: true
      
      - name: 📤 Upload Snyk Report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: snyk-vulnerability-report
          path: snyk-report.json
      
      # ========================================================================
      # CYCLONEDX SBOM GENERATION
      # ========================================================================
      - name: 📋 Generate Software Bill of Materials (SBOM)
        run: |
          cyclonedx-py -o sbom.json
          cyclonedx-py -o sbom.xml --format xml
        continue-on-error: true
      
      - name: 📤 Upload SBOM
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: software-bill-of-materials
          path: |
            sbom.json
            sbom.xml
      
      # ========================================================================
      # DEPENDENCY ANALYSIS
      # ========================================================================
      - name: 📊 Analyze Dependencies
        run: |
          pip install pipdeptree
          pipdeptree --json-tree > dependency-tree.json
          pipdeptree --graph-output png > dependency-graph.png || true
          
          # Generate dependency summary
          python -c "
          import json
          import sys
          
          with open('dependency-tree.json', 'r') as f:
              deps = json.load(f)
          
          total_deps = len(deps)
          direct_deps = sum(1 for dep in deps if dep.get('package', {}).get('installed_version'))
          
          print(f'📊 Dependency Analysis Summary:')
          print(f'  Total Dependencies: {total_deps}')
          print(f'  Direct Dependencies: {direct_deps}')
          print(f'  Transitive Dependencies: {total_deps - direct_deps}')
          
          # Check for outdated packages
          import subprocess
          try:
              result = subprocess.run(['pip', 'list', '--outdated', '--format=json'], 
                                    capture_output=True, text=True)
              if result.returncode == 0:
                  outdated = json.loads(result.stdout)
                  print(f'  Outdated Packages: {len(outdated)}')
                  if outdated:
                      print('  Outdated packages:')
                      for pkg in outdated[:5]:  # Show first 5
                          print(f'    - {pkg[\"name\"]}: {pkg[\"version\"]} -> {pkg[\"latest_version\"]}')
          except:
              pass
          "
      
      - name: 📤 Upload Dependency Analysis
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: dependency-analysis
          path: |
            dependency-tree.json
            dependency-graph.png
      
      # ========================================================================
      # LICENSE COMPLIANCE CHECK
      # ========================================================================
      - name: 📄 License Compliance Check
        run: |
          pip install pip-licenses
          pip-licenses --format=json --output-file=licenses-report.json
          pip-licenses --format=csv --output-file=licenses-report.csv
          
          # Check for problematic licenses
          python -c "
          import json
          
          # Define license categories
          APPROVED_LICENSES = {
              'MIT', 'Apache Software License', 'Apache 2.0', 'Apache License 2.0',
              'BSD License', 'BSD', '3-Clause BSD License', '2-Clause BSD License',
              'ISC License', 'Python Software Foundation License', 'Mozilla Public License 2.0 (MPL 2.0)',
              'MIT License'
          }
          
          RESTRICTED_LICENSES = {
              'GNU General Public License v3 (GPLv3)', 'GNU General Public License v2 (GPLv2)',
              'GNU Affero General Public License v3', 'AGPL-3.0'
          }
          
          with open('licenses-report.json', 'r') as f:
              licenses = json.load(f)
          
          approved = []
          restricted = []
          unknown = []
          
          for pkg in licenses:
              license_name = pkg.get('License', 'Unknown')
              if license_name in APPROVED_LICENSES:
                  approved.append(pkg)
              elif license_name in RESTRICTED_LICENSES:
                  restricted.append(pkg)
              else:
                  unknown.append(pkg)
          
          print(f'📄 License Compliance Summary:')
          print(f'  Approved Licenses: {len(approved)}')
          print(f'  Restricted Licenses: {len(restricted)}')
          print(f'  Unknown/Review Required: {len(unknown)}')
          
          if restricted:
              print(f'  ⚠️ Packages with restricted licenses:')
              for pkg in restricted:
                  print(f'    - {pkg[\"Name\"]}: {pkg[\"License\"]}')
          
          if unknown:
              print(f'  ❓ Packages requiring license review:')
              for pkg in unknown[:5]:  # Show first 5
                  print(f'    - {pkg[\"Name\"]}: {pkg[\"License\"]}')
          "
      
      - name: 📤 Upload License Report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: license-compliance-report
          path: |
            licenses-report.json
            licenses-report.csv
      
      # ========================================================================
      # COMPREHENSIVE REPORT GENERATION
      # ========================================================================
      - name: 📊 Generate Comprehensive Vulnerability Report
        run: |
          python -c "
          import json
          import os
          from datetime import datetime
          
          # Initialize report
          report = {
              'scan_metadata': {
                  'timestamp': datetime.now().isoformat(),
                  'scan_type': 'dependency_vulnerability_scan',
                  'python_version': '${{ env.PYTHON_VERSION }}',
                  'repository': '${{ github.repository }}',
                  'commit_sha': '${{ github.sha }}',
                  'workflow_run_id': '${{ github.run_id }}'
              },
              'vulnerability_summary': {
                  'total_vulnerabilities': 0,
                  'critical': 0,
                  'high': 0,
                  'medium': 0,
                  'low': 0
              },
              'tools_used': [],
              'recommendations': []
          }
          
          # Process Safety report
          if os.path.exists('safety-report.json'):
              try:
                  with open('safety-report.json', 'r') as f:
                      safety_data = json.load(f)
                  
                  if isinstance(safety_data, list):
                      safety_vulns = len(safety_data)
                      report['vulnerability_summary']['total_vulnerabilities'] += safety_vulns
                      report['vulnerability_summary']['medium'] += safety_vulns  # Safety doesn't provide severity
                      report['tools_used'].append({
                          'name': 'Safety',
                          'vulnerabilities_found': safety_vulns,
                          'status': 'completed'
                      })
                      
                      if safety_vulns > 0:
                          report['recommendations'].append(f'Update {safety_vulns} vulnerable packages identified by Safety')
              except:
                  report['tools_used'].append({'name': 'Safety', 'status': 'failed'})
          
          # Process Pip-Audit report
          if os.path.exists('pip-audit-report.json'):
              try:
                  with open('pip-audit-report.json', 'r') as f:
                      pipaudit_data = json.load(f)
                  
                  pipaudit_vulns = len(pipaudit_data.get('vulnerabilities', []))
                  report['vulnerability_summary']['total_vulnerabilities'] += pipaudit_vulns
                  report['vulnerability_summary']['high'] += pipaudit_vulns  # Pip-audit finds high severity issues
                  report['tools_used'].append({
                      'name': 'Pip-Audit',
                      'vulnerabilities_found': pipaudit_vulns,
                      'status': 'completed'
                  })
                  
                  if pipaudit_vulns > 0:
                      report['recommendations'].append(f'Address {pipaudit_vulns} vulnerabilities found by Pip-Audit')
              except:
                  report['tools_used'].append({'name': 'Pip-Audit', 'status': 'failed'})
          
          # Process Snyk report
          if os.path.exists('snyk-report.json'):
              try:
                  with open('snyk-report.json', 'r') as f:
                      snyk_data = json.load(f)
                  
                  snyk_vulns = len(snyk_data.get('vulnerabilities', []))
                  report['vulnerability_summary']['total_vulnerabilities'] += snyk_vulns
                  report['vulnerability_summary']['high'] += snyk_vulns
                  report['tools_used'].append({
                      'name': 'Snyk',
                      'vulnerabilities_found': snyk_vulns,
                      'status': 'completed'
                  })
                  
                  if snyk_vulns > 0:
                      report['recommendations'].append(f'Fix {snyk_vulns} security issues identified by Snyk')
              except:
                  report['tools_used'].append({'name': 'Snyk', 'status': 'failed'})
          
          # Add general recommendations
          if report['vulnerability_summary']['total_vulnerabilities'] == 0:
              report['recommendations'].append('No vulnerabilities found. Continue monitoring dependencies.')
          else:
              report['recommendations'].extend([
                  'Review and update vulnerable dependencies immediately',
                  'Consider using dependency pinning for critical packages',
                  'Set up automated dependency updates with testing',
                  'Monitor security advisories for used packages'
              ])
          
          # Calculate risk level
          total_vulns = report['vulnerability_summary']['total_vulnerabilities']
          if total_vulns == 0:
              risk_level = 'LOW'
          elif total_vulns <= 5:
              risk_level = 'MEDIUM'
          elif total_vulns <= 15:
              risk_level = 'HIGH'
          else:
              risk_level = 'CRITICAL'
          
          report['risk_assessment'] = {
              'overall_risk_level': risk_level,
              'risk_score': total_vulns,
              'risk_factors': [
                  f'Total vulnerabilities: {total_vulns}',
                  f'Tools scanned: {len(report[\"tools_used\"])}',
                  f'Recommendations: {len(report[\"recommendations\"])}'
              ]
          }
          
          # Save comprehensive report
          with open('dependency-vulnerability-report.json', 'w') as f:
              json.dump(report, f, indent=2)
          
          # Print summary
          print('🔍 Dependency Vulnerability Scan Summary:')
          print(f'  Total Vulnerabilities: {total_vulns}')
          print(f'  Risk Level: {risk_level}')
          print(f'  Tools Used: {len(report[\"tools_used\"])}')
          print(f'  Recommendations: {len(report[\"recommendations\"])}')
          "
      
      - name: 📤 Upload Comprehensive Report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: comprehensive-vulnerability-report
          path: dependency-vulnerability-report.json
      
      # ========================================================================
      # SECURITY ADVISORY CREATION
      # ========================================================================
      - name: 🚨 Create Security Advisory (if vulnerabilities found)
        if: always()
        run: |
          # Check if we have vulnerabilities and create advisory
          if [ -f "dependency-vulnerability-report.json" ]; then
            VULN_COUNT=$(python -c "
            import json
            with open('dependency-vulnerability-report.json', 'r') as f:
                data = json.load(f)
            print(data['vulnerability_summary']['total_vulnerabilities'])
            ")
            
            if [ "$VULN_COUNT" -gt "0" ]; then
              echo "🚨 $VULN_COUNT vulnerabilities found - Security review required"
              echo "vulnerability_count=$VULN_COUNT" >> $GITHUB_OUTPUT
            else
              echo "✅ No vulnerabilities found"
              echo "vulnerability_count=0" >> $GITHUB_OUTPUT
            fi
          fi
        id: security-check
      
      # ========================================================================
      # NOTIFICATION AND REPORTING
      # ========================================================================
      - name: 📊 Update Security Dashboard
        if: always()
        run: |
          echo "📊 Dependency scan completed"
          echo "Results uploaded to artifacts for review"
          echo "Security dashboard would be updated here in production"
      
      - name: 🚨 Fail on Critical Vulnerabilities
        if: steps.security-check.outputs.vulnerability_count > 10
        run: |
          echo "🚨 Critical number of vulnerabilities found: ${{ steps.security-check.outputs.vulnerability_count }}"
          echo "Security review required before deployment"
          exit 1
