// Performance monitoring service
import config from '../config.js';

class PerformanceService {
    constructor() {
        this.enabled = config.performance.enabled;
        this.metrics = config.performance.metrics;
        this.thresholds = config.performance.thresholds;
        this.measurements = new Map();
    }

    /**
     * Start measuring a metric
     * @param {string} metricName - The name of the metric
     * @returns {string} The measurement ID
     */
    startMeasurement(metricName) {
        if (!this.enabled) return null;

        const id = `${metricName}_${Date.now()}`;
        this.measurements.set(id, {
            name: metricName,
            startTime: performance.now(),
        });

        return id;
    }

    /**
     * End measuring a metric
     * @param {string} measurementId - The measurement ID
     * @returns {Object} The measurement result
     */
    endMeasurement(measurementId) {
        if (!this.enabled || !measurementId) return null;

        const measurement = this.measurements.get(measurementId);
        if (!measurement) return null;

        const endTime = performance.now();
        const duration = endTime - measurement.startTime;
        const threshold = this.thresholds[measurement.name];

        const result = {
            metric: measurement.name,
            duration,
            timestamp: new Date().toISOString(),
            exceededThreshold: threshold ? duration > threshold : false,
        };

        this.measurements.delete(measurementId);
        this.logMeasurement(result);

        return result;
    }

    /**
     * Log a measurement
     * @param {Object} measurement - The measurement to log
     */
    logMeasurement(measurement) {
        if (!this.enabled) return;

        const { metric, duration, exceededThreshold } = measurement;
        const threshold = this.thresholds[metric];

        if (exceededThreshold) {
            console.warn(
                `Performance warning - ${metric} exceeded threshold of ${threshold}ms ` +
                `(actual: ${duration.toFixed(2)}ms)`
            );
        } else {
            console.log(
                `Performance metric - ${metric}: ${duration.toFixed(2)}ms ` +
                `(threshold: ${threshold}ms)`
            );
        }
    }

    /**
     * Measure the performance of an async operation
     * @param {string} metricName - The name of the metric
     * @param {Function} operation - The operation to measure
     * @returns {Promise<any>} The result of the operation
     */
    async measureOperation(metricName, operation) {
        if (!this.enabled) return operation();

        const measurementId = this.startMeasurement(metricName);
        try {
            const result = await operation();
            this.endMeasurement(measurementId);
            return result;
        } catch (error) {
            this.endMeasurement(measurementId);
            throw error;
        }
    }

    /**
     * Get performance metrics
     * @returns {Object} Performance metrics
     */
    getMetrics() {
        if (!this.enabled) return null;

        const metrics = {};
        for (const [name, threshold] of Object.entries(this.thresholds)) {
            metrics[name] = {
                threshold,
                activeMeasurements: Array.from(this.measurements.values())
                    .filter(m => m.name === name).length,
            };
        }

        return metrics;
    }

    /**
     * Clear all measurements
     */
    clearMeasurements() {
        this.measurements.clear();
    }
}

// Create and export a singleton instance
const performanceService = new PerformanceService();
export default performanceService; 