// Internationalization service
import config from '../config.js';

class I18nService {
    constructor() {
        this.language = config.ui.language;
        this.dateFormat = config.ui.dateFormat;
        this.timeFormat = config.ui.timeFormat;
        this.translations = new Map();
        this.fallbackLanguage = 'en';
    }

    /**
     * Load translations for a language
     * @param {string} language - The language code
     * @returns {Promise<void>}
     */
    async loadTranslations(language) {
        try {
            const response = await fetch(`/locales/${language}.json`);
            if (!response.ok) {
                throw new Error(`Failed to load translations for ${language}`);
            }
            const translations = await response.json();
            this.translations.set(language, translations);
        } catch (error) {
            console.error(`Error loading translations for ${language}:`, error);
            if (language !== this.fallbackLanguage) {
                await this.loadTranslations(this.fallbackLanguage);
            }
        }
    }

    /**
     * Get a translation
     * @param {string} key - The translation key
     * @param {Object} [params] - Optional parameters
     * @returns {string} The translated string
     */
    translate(key, params = {}) {
        const translations = this.translations.get(this.language) ||
            this.translations.get(this.fallbackLanguage);

        if (!translations) {
            return key;
        }

        let translation = this.getNestedTranslation(translations, key);
        if (!translation) {
            return key;
        }

        // Replace parameters
        Object.entries(params).forEach(([param, value]) => {
            translation = translation.replace(
                new RegExp(`{${param}}`, 'g'),
                value
            );
        });

        return translation;
    }

    /**
     * Get a nested translation
     * @param {Object} translations - The translations object
     * @param {string} key - The translation key
     * @returns {string|undefined} The translation or undefined
     */
    getNestedTranslation(translations, key) {
        return key.split('.').reduce((obj, part) => obj?.[part], translations);
    }

    /**
     * Format a date
     * @param {Date} date - The date to format
     * @returns {string} The formatted date
     */
    formatDate(date) {
        const translations = this.translations.get(this.language);
        if (!translations?.dateFormats) {
            return date.toLocaleDateString();
        }

        const format = translations.dateFormats[this.dateFormat];
        if (!format) {
            return date.toLocaleDateString();
        }

        return this.formatDateTime(date, format);
    }

    /**
     * Format a time
     * @param {Date} date - The date containing the time
     * @returns {string} The formatted time
     */
    formatTime(date) {
        const translations = this.translations.get(this.language);
        if (!translations?.timeFormats) {
            return date.toLocaleTimeString();
        }

        const format = translations.timeFormats[this.timeFormat];
        if (!format) {
            return date.toLocaleTimeString();
        }

        return this.formatDateTime(date, format);
    }

    /**
     * Format a date and time
     * @param {Date} date - The date to format
     * @param {string} format - The format string
     * @returns {string} The formatted date and time
     */
    formatDateTime(date, format) {
        const translations = this.translations.get(this.language);
        const months = translations?.months || [];
        const weekdays = translations?.weekdays || [];

        return format.replace(/\{(\w+)\}/g, (match, key) => {
            switch (key) {
                case 'year':
                    return date.getFullYear();
                case 'month':
                    return months[date.getMonth()] || (date.getMonth() + 1);
                case 'day':
                    return date.getDate();
                case 'weekday':
                    return weekdays[date.getDay()] || '';
                case 'hours':
                    return date.getHours().toString().padStart(2, '0');
                case 'minutes':
                    return date.getMinutes().toString().padStart(2, '0');
                case 'seconds':
                    return date.getSeconds().toString().padStart(2, '0');
                default:
                    return match;
            }
        });
    }

    /**
     * Format a number
     * @param {number} number - The number to format
     * @param {Object} [options] - Formatting options
     * @returns {string} The formatted number
     */
    formatNumber(number, options = {}) {
        const translations = this.translations.get(this.language);
        if (!translations?.numberFormats) {
            return number.toLocaleString();
        }

        const format = translations.numberFormats[options.format || 'default'];
        if (!format) {
            return number.toLocaleString();
        }

        return new Intl.NumberFormat(this.language, {
            style: format.style,
            currency: format.currency,
            minimumFractionDigits: format.minimumFractionDigits,
            maximumFractionDigits: format.maximumFractionDigits,
        }).format(number);
    }

    /**
     * Set the language
     * @param {string} language - The language code
     */
    async setLanguage(language) {
        if (language === this.language) return;

        this.language = language;
        await this.loadTranslations(language);
        document.documentElement.setAttribute('lang', language);
    }

    /**
     * Get the current language
     * @returns {string} The current language code
     */
    getLanguage() {
        return this.language;
    }

    /**
     * Get available languages
     * @returns {string[]} Array of available language codes
     */
    getAvailableLanguages() {
        return Array.from(this.translations.keys());
    }
}

// Create and export a singleton instance
const i18nService = new I18nService();
export default i18nService; 