// Masonry service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class MasonryService {
    constructor() {
        this.masonries = new Map();
        this.subscribers = new Set();
        this.draggedItem = null;
        this.placeholder = null;
        this.initialize();
    }

    /**
     * Initialize the masonry service
     */
    initialize() {
        document.addEventListener('dragstart', this.handleDragStart.bind(this));
        document.addEventListener('dragend', this.handleDragEnd.bind(this));
        document.addEventListener('dragover', this.handleDragOver.bind(this));
        document.addEventListener('drop', this.handleDrop.bind(this));
        window.addEventListener('resize', this.handleResize.bind(this));
        window.addEventListener('load', this.handleLoad.bind(this));
    }

    /**
     * Subscribe to masonry events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of masonry events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in masonry subscriber:', error);
            }
        });
    }

    /**
     * Register a masonry container
     * @param {string} id - The container ID
     * @param {Object} options - The masonry options
     */
    registerMasonry(id, options = {}) {
        const container = document.getElementById(id);
        if (!container) {
            console.error(`Container ${id} not found`);
            return;
        }

        // Set masonry styles
        container.style.position = 'relative';
        container.style.width = '100%';

        // Make items draggable
        const items = container.querySelectorAll(options.itemSelector || '.masonry-item');
        items.forEach(item => {
            item.draggable = true;
            item.classList.add('masonry-item');
            item.style.position = 'absolute';
            item.style.width = `${options.itemWidth || 300}px`;
        });

        this.masonries.set(id, {
            ...options,
            container,
            items: Array.from(items),
        });

        // Initial layout
        this.layoutMasonry(id);
    }

    /**
     * Create a placeholder element
     * @param {HTMLElement} item - The dragged item
     * @returns {HTMLElement} The placeholder element
     */
    createPlaceholder(item) {
        const placeholder = document.createElement('div');
        placeholder.className = 'masonry-placeholder';
        placeholder.style.position = 'absolute';
        placeholder.style.width = item.style.width;
        placeholder.style.height = `${item.offsetHeight}px`;
        placeholder.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
        return placeholder;
    }

    /**
     * Layout a masonry grid
     * @param {string} id - The masonry ID
     */
    layoutMasonry(id) {
        return measurePerformance('masonry_layout', () => {
            const masonry = this.masonries.get(id);
            if (!masonry) {
                return;
            }

            const { container, items } = masonry;
            const itemWidth = masonry.itemWidth || 300;
            const gap = masonry.gap || 20;
            const columns = Math.floor(container.offsetWidth / (itemWidth + gap));
            const columnHeights = new Array(columns).fill(0);
            const columnItems = new Array(columns).fill().map(() => []);

            // Calculate positions
            items.forEach(item => {
                // Find shortest column
                const minHeight = Math.min(...columnHeights);
                const columnIndex = columnHeights.indexOf(minHeight);

                // Calculate position
                const x = columnIndex * (itemWidth + gap);
                const y = minHeight;

                // Update column height
                columnHeights[columnIndex] = y + item.offsetHeight + gap;
                columnItems[columnIndex].push(item);

                // Position item
                item.style.left = `${x}px`;
                item.style.top = `${y}px`;
            });

            // Update container height
            container.style.height = `${Math.max(...columnHeights)}px`;

            if (masonry.onLayout) {
                masonry.onLayout({ container, items, columns: columnItems });
            }

            this.notifySubscribers('layout', { id });
        });
    }

    /**
     * Handle drag start events
     * @param {DragEvent} event - The drag event
     */
    handleDragStart(event) {
        return measurePerformance('masonry_dragstart', () => {
            const item = event.target.closest('.masonry-item');
            if (!item) {
                return;
            }

            const container = item.parentElement;
            const id = container.id;
            const masonry = this.masonries.get(id);
            if (!masonry) {
                return;
            }

            this.draggedItem = item;
            this.placeholder = this.createPlaceholder(item);

            // Set drag data
            event.dataTransfer.effectAllowed = 'move';
            event.dataTransfer.setData('text/plain', id);

            // Add placeholder
            container.appendChild(this.placeholder);
            this.placeholder.style.left = item.style.left;
            this.placeholder.style.top = item.style.top;

            if (masonry.onDragStart) {
                masonry.onDragStart(event, {
                    item,
                    container,
                    placeholder: this.placeholder,
                });
            }

            this.notifySubscribers('dragstart', { id, event });
        });
    }

    /**
     * Handle drag end events
     * @param {DragEvent} event - The drag event
     */
    handleDragEnd(event) {
        return measurePerformance('masonry_dragend', () => {
            if (!this.draggedItem) {
                return;
            }

            const container = this.draggedItem.parentElement;
            const id = container.id;
            const masonry = this.masonries.get(id);
            if (!masonry) {
                return;
            }

            // Remove placeholder
            if (this.placeholder && this.placeholder.parentElement) {
                this.placeholder.parentElement.removeChild(this.placeholder);
            }

            if (masonry.onDragEnd) {
                masonry.onDragEnd(event, {
                    item: this.draggedItem,
                    container,
                });
            }

            this.draggedItem = null;
            this.placeholder = null;

            this.notifySubscribers('dragend', { id, event });
        });
    }

    /**
     * Handle drag over events
     * @param {DragEvent} event - The drag event
     */
    handleDragOver(event) {
        return measurePerformance('masonry_dragover', () => {
            event.preventDefault();

            if (!this.draggedItem || !this.placeholder) {
                return;
            }

            const container = this.draggedItem.parentElement;
            const id = container.id;
            const masonry = this.masonries.get(id);
            if (!masonry) {
                return;
            }

            const target = event.target.closest('.masonry-item');
            if (!target || target === this.draggedItem) {
                return;
            }

            // Calculate position
            const rect = target.getBoundingClientRect();
            const midpoint = rect.top + rect.height / 2;
            const position = event.clientY < midpoint ? 'before' : 'after';

            // Move placeholder
            if (position === 'before') {
                container.insertBefore(this.placeholder, target);
            } else {
                container.insertBefore(this.placeholder, target.nextSibling);
            }

            if (masonry.onDragOver) {
                masonry.onDragOver(event, {
                    item: this.draggedItem,
                    target,
                    container,
                    position,
                });
            }

            this.notifySubscribers('dragover', { id, event });
        });
    }

    /**
     * Handle drop events
     * @param {DragEvent} event - The drop event
     */
    handleDrop(event) {
        return measurePerformance('masonry_drop', () => {
            event.preventDefault();

            if (!this.draggedItem || !this.placeholder) {
                return;
            }

            const container = this.draggedItem.parentElement;
            const id = container.id;
            const masonry = this.masonries.get(id);
            if (!masonry) {
                return;
            }

            // Move item to placeholder position
            container.insertBefore(this.draggedItem, this.placeholder);

            // Update items array
            const items = Array.from(container.querySelectorAll('.masonry-item'));
            masonry.items = items;

            // Re-layout masonry
            this.layoutMasonry(id);

            if (masonry.onDrop) {
                masonry.onDrop(event, {
                    item: this.draggedItem,
                    container,
                    items,
                });
            }

            this.notifySubscribers('drop', { id, event });
        });
    }

    /**
     * Handle resize events
     * @param {Event} event - The resize event
     */
    handleResize(event) {
        return measurePerformance('masonry_resize', () => {
            this.masonries.forEach((masonry, id) => {
                this.layoutMasonry(id);
            });
        });
    }

    /**
     * Handle load events
     * @param {Event} event - The load event
     */
    handleLoad(event) {
        return measurePerformance('masonry_load', () => {
            this.masonries.forEach((masonry, id) => {
                this.layoutMasonry(id);
            });
        });
    }

    /**
     * Get masonry data
     * @param {string} id - The masonry ID
     * @returns {Object} The masonry data
     */
    getMasonryData(id) {
        return this.masonries.get(id);
    }

    /**
     * Update masonry data
     * @param {string} id - The masonry ID
     * @param {Object} data - The new masonry data
     */
    updateMasonryData(id, data) {
        const masonry = this.masonries.get(id);
        if (masonry) {
            Object.assign(masonry, data);
            this.layoutMasonry(id);
        }
    }
}

// Create and export a singleton instance
const masonryService = new MasonryService();
export default masonryService; 