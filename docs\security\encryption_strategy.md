# 🔐 Encryption Strategy

> **Comprehensive Encryption Architecture for ImpactCV**  
> **Standards:** AES-256 | TLS 1.3 | FIPS 140-2 | NIST SP 800-57

---

## 📋 **EXECUTIVE SUMMARY**

### **Encryption Overview**
ImpactCV implements a comprehensive encryption strategy covering data at rest, data in transit, and data in processing. The strategy follows industry best practices and compliance requirements including GDPR, HIPAA, and SOC 2 standards.

### **Encryption Principles**
1. **Defense in Depth** - Multiple layers of encryption protection
2. **Zero Trust** - Encrypt everything, trust nothing
3. **Key Management** - Secure key lifecycle management
4. **Performance Balance** - Strong security without compromising performance

---

## 🔒 **ENCRYPTION ARCHITECTURE OVERVIEW**

### **Encryption Layers**

```mermaid
graph TB
    subgraph "Application Layer"
        APP[Application Code]
        JWT[JWT Tokens]
        SESSION[Session Data]
    end
    
    subgraph "Transport Layer"
        TLS[TLS 1.3]
        MTLS[mTLS]
        VPN[VPN Tunnels]
    end
    
    subgraph "Data Layer"
        DB[Database Encryption]
        FILE[File Encryption]
        BACKUP[Backup Encryption]
    end
    
    subgraph "Infrastructure Layer"
        DISK[Disk Encryption]
        NETWORK[Network Encryption]
        CONTAINER[Container Encryption]
    end
    
    APP --> TLS
    JWT --> TLS
    SESSION --> TLS
    TLS --> DB
    MTLS --> FILE
    VPN --> BACKUP
    DB --> DISK
    FILE --> NETWORK
    BACKUP --> CONTAINER
```

---

## 🛡️ **DATA AT REST ENCRYPTION**

### **Database Encryption**

```python
# Database encryption implementation
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

class DatabaseEncryption:
    def __init__(self, master_key: bytes = None):
        if master_key is None:
            master_key = os.getenv("DB_MASTER_KEY").encode()
        
        # Derive encryption key from master key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'stable_salt_for_db',  # In production, use random salt per record
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(master_key))
        self.cipher = Fernet(key)
    
    def encrypt_field(self, plaintext: str) -> str:
        """Encrypt sensitive database field"""
        if not plaintext:
            return plaintext
        
        encrypted_bytes = self.cipher.encrypt(plaintext.encode())
        return base64.urlsafe_b64encode(encrypted_bytes).decode()
    
    def decrypt_field(self, ciphertext: str) -> str:
        """Decrypt sensitive database field"""
        if not ciphertext:
            return ciphertext
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(ciphertext.encode())
            decrypted_bytes = self.cipher.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception:
            # Handle decryption errors gracefully
            return "[ENCRYPTED_DATA]"

# SQLAlchemy model with field-level encryption
from sqlalchemy import Column, String, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.types import TypeDecorator, String as SQLString

class EncryptedType(TypeDecorator):
    impl = SQLString
    
    def __init__(self, encryptor: DatabaseEncryption, *args, **kwargs):
        self.encryptor = encryptor
        super().__init__(*args, **kwargs)
    
    def process_bind_param(self, value, dialect):
        if value is not None:
            return self.encryptor.encrypt_field(value)
        return value
    
    def process_result_value(self, value, dialect):
        if value is not None:
            return self.encryptor.decrypt_field(value)
        return value

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(String, primary_key=True)
    email = Column(String, nullable=False)  # Not encrypted (needed for queries)
    
    # Encrypted fields
    full_name = Column(EncryptedType(DatabaseEncryption()), nullable=True)
    phone_number = Column(EncryptedType(DatabaseEncryption()), nullable=True)
    address = Column(EncryptedType(DatabaseEncryption()), nullable=True)
    
    # Highly sensitive data
    ssn = Column(EncryptedType(DatabaseEncryption()), nullable=True)
    payment_info = Column(EncryptedType(DatabaseEncryption()), nullable=True)
```

### **File System Encryption**

```python
# File encryption for uploaded documents
import os
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.scrypt import Scrypt

class FileEncryption:
    def __init__(self, password: str = None):
        if password is None:
            password = os.getenv("FILE_ENCRYPTION_PASSWORD")
        
        # Derive key from password using Scrypt
        salt = os.urandom(16)
        kdf = Scrypt(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            n=2**14,
            r=8,
            p=1,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        self.cipher = Fernet(key)
        self.salt = salt
    
    def encrypt_file(self, file_path: Path, output_path: Path = None) -> Path:
        """Encrypt file and save to disk"""
        if output_path is None:
            output_path = file_path.with_suffix(file_path.suffix + '.enc')
        
        with open(file_path, 'rb') as infile:
            file_data = infile.read()
        
        # Encrypt file data
        encrypted_data = self.cipher.encrypt(file_data)
        
        # Write encrypted file with salt
        with open(output_path, 'wb') as outfile:
            outfile.write(self.salt)  # First 16 bytes are salt
            outfile.write(encrypted_data)
        
        return output_path
    
    def decrypt_file(self, encrypted_path: Path, output_path: Path = None) -> Path:
        """Decrypt file and save to disk"""
        if output_path is None:
            output_path = encrypted_path.with_suffix('')
        
        with open(encrypted_path, 'rb') as infile:
            salt = infile.read(16)  # Read salt
            encrypted_data = infile.read()
        
        # Recreate cipher with stored salt
        kdf = Scrypt(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            n=2**14,
            r=8,
            p=1,
        )
        key = base64.urlsafe_b64encode(kdf.derive(os.getenv("FILE_ENCRYPTION_PASSWORD").encode()))
        cipher = Fernet(key)
        
        # Decrypt file data
        decrypted_data = cipher.decrypt(encrypted_data)
        
        with open(output_path, 'wb') as outfile:
            outfile.write(decrypted_data)
        
        return output_path

# Integration with FastAPI file upload
from fastapi import UploadFile
import tempfile

class SecureFileHandler:
    def __init__(self):
        self.encryptor = FileEncryption()
        self.upload_dir = Path(os.getenv("SECURE_UPLOAD_DIR", "/app/secure_uploads"))
        self.upload_dir.mkdir(exist_ok=True)
    
    async def save_encrypted_file(self, file: UploadFile) -> str:
        """Save uploaded file in encrypted format"""
        # Generate unique filename
        file_id = str(uuid.uuid4())
        temp_path = self.upload_dir / f"{file_id}.tmp"
        encrypted_path = self.upload_dir / f"{file_id}.enc"
        
        # Save uploaded file temporarily
        with open(temp_path, 'wb') as temp_file:
            content = await file.read()
            temp_file.write(content)
        
        # Encrypt and save
        self.encryptor.encrypt_file(temp_path, encrypted_path)
        
        # Remove temporary file
        temp_path.unlink()
        
        return file_id
    
    def get_decrypted_file(self, file_id: str) -> Path:
        """Get decrypted file for processing"""
        encrypted_path = self.upload_dir / f"{file_id}.enc"
        temp_path = Path(tempfile.mktemp())
        
        return self.encryptor.decrypt_file(encrypted_path, temp_path)
```

---

## 🌐 **DATA IN TRANSIT ENCRYPTION**

### **TLS 1.3 Configuration**

```python
# TLS 1.3 configuration for FastAPI
import ssl
from fastapi import FastAPI
import uvicorn

def create_ssl_context() -> ssl.SSLContext:
    """Create secure SSL context with TLS 1.3"""
    context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
    
    # Force TLS 1.3
    context.minimum_version = ssl.TLSVersion.TLSv1_3
    context.maximum_version = ssl.TLSVersion.TLSv1_3
    
    # Secure cipher suites
    context.set_ciphers('TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256')
    
    # Load certificates
    context.load_cert_chain(
        certfile=os.getenv("SSL_CERT_PATH", "/app/certs/server.crt"),
        keyfile=os.getenv("SSL_KEY_PATH", "/app/certs/server.key")
    )
    
    # Security options
    context.options |= ssl.OP_NO_SSLv2
    context.options |= ssl.OP_NO_SSLv3
    context.options |= ssl.OP_NO_TLSv1
    context.options |= ssl.OP_NO_TLSv1_1
    context.options |= ssl.OP_NO_TLSv1_2
    context.options |= ssl.OP_SINGLE_DH_USE
    context.options |= ssl.OP_SINGLE_ECDH_USE
    
    return context

# FastAPI with TLS 1.3
app = FastAPI(title="ImpactCV Secure API")

if __name__ == "__main__":
    ssl_context = create_ssl_context()
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8443,
        ssl_context=ssl_context,
        ssl_keyfile=None,  # Already loaded in context
        ssl_certfile=None  # Already loaded in context
    )
```

### **mTLS for Service-to-Service Communication**

```python
# Mutual TLS for microservices
import httpx
import ssl
from pathlib import Path

class SecureServiceClient:
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.client = self._create_secure_client()
    
    def _create_secure_client(self) -> httpx.AsyncClient:
        """Create HTTPX client with mTLS"""
        cert_dir = Path("/app/certs")
        
        # Client certificate for authentication
        client_cert = cert_dir / f"{self.service_name}-client.crt"
        client_key = cert_dir / f"{self.service_name}-client.key"
        
        # CA certificate for server verification
        ca_cert = cert_dir / "ca.crt"
        
        return httpx.AsyncClient(
            cert=(str(client_cert), str(client_key)),
            verify=str(ca_cert),
            timeout=30.0
        )
    
    async def call_service(self, url: str, method: str = "GET", **kwargs):
        """Make secure service call with mTLS"""
        async with self.client as client:
            response = await client.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()

# Usage in microservices
class CVGenerationService:
    def __init__(self):
        self.rag_client = SecureServiceClient("cv-generation")
        self.data_client = SecureServiceClient("cv-generation")
    
    async def generate_cv(self, user_data: dict) -> dict:
        # Secure call to RAG service
        enhanced_data = await self.rag_client.call_service(
            "https://rag-service:8443/enhance",
            method="POST",
            json=user_data
        )
        
        # Secure call to data processing service
        processed_data = await self.data_client.call_service(
            "https://data-service:8443/process",
            method="POST",
            json=enhanced_data
        )
        
        return processed_data
```

---

## 🔑 **KEY MANAGEMENT**

### **Key Derivation and Storage**

```python
# Secure key management system
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import keyring
import os

class KeyManager:
    def __init__(self):
        self.master_key = self._get_master_key()
        self.key_cache = {}
    
    def _get_master_key(self) -> bytes:
        """Get master key from secure storage"""
        # In production, use hardware security module (HSM) or key vault
        master_key = os.getenv("MASTER_KEY")
        if not master_key:
            # Try to get from system keyring
            master_key = keyring.get_password("impactcv", "master_key")
        
        if not master_key:
            raise ValueError("Master key not found in environment or keyring")
        
        return master_key.encode()
    
    def derive_key(self, purpose: str, context: str = "") -> bytes:
        """Derive purpose-specific key from master key"""
        cache_key = f"{purpose}:{context}"
        
        if cache_key in self.key_cache:
            return self.key_cache[cache_key]
        
        # Use HKDF for key derivation
        hkdf = HKDF(
            algorithm=hashes.SHA256(),
            length=32,
            salt=f"impactcv-{purpose}".encode(),
            info=context.encode(),
        )
        
        derived_key = hkdf.derive(self.master_key)
        self.key_cache[cache_key] = derived_key
        
        return derived_key
    
    def get_database_key(self, table_name: str = "") -> bytes:
        """Get database encryption key"""
        return self.derive_key("database", table_name)
    
    def get_file_key(self, file_type: str = "") -> bytes:
        """Get file encryption key"""
        return self.derive_key("files", file_type)
    
    def get_jwt_key(self) -> bytes:
        """Get JWT signing key"""
        return self.derive_key("jwt", "signing")
    
    def rotate_keys(self):
        """Rotate encryption keys (implement key rotation strategy)"""
        # Clear cache to force re-derivation
        self.key_cache.clear()
        
        # In production, implement proper key rotation:
        # 1. Generate new master key
        # 2. Re-encrypt all data with new keys
        # 3. Update key references
        # 4. Securely delete old keys
        pass

# Key rotation scheduler
import asyncio
from datetime import datetime, timedelta

class KeyRotationScheduler:
    def __init__(self, key_manager: KeyManager):
        self.key_manager = key_manager
        self.rotation_interval = timedelta(days=90)  # Rotate every 90 days
        self.last_rotation = datetime.utcnow()
    
    async def schedule_rotation(self):
        """Schedule automatic key rotation"""
        while True:
            next_rotation = self.last_rotation + self.rotation_interval
            sleep_time = (next_rotation - datetime.utcnow()).total_seconds()
            
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
            
            # Perform key rotation
            await self.rotate_keys()
            self.last_rotation = datetime.utcnow()
    
    async def rotate_keys(self):
        """Perform key rotation"""
        try:
            # Log rotation start
            logger.info("Starting key rotation process")
            
            # Rotate keys
            self.key_manager.rotate_keys()
            
            # Notify administrators
            await self.notify_key_rotation()
            
            logger.info("Key rotation completed successfully")
            
        except Exception as e:
            logger.error(f"Key rotation failed: {e}")
            await self.notify_rotation_failure(e)
```

---

## 🔐 **ENCRYPTION IN DOCKER ENVIRONMENT**

### **Docker Secrets Management**

```yaml
# docker-compose.yml with secrets
version: '3.8'

secrets:
  db_master_key:
    file: ./secrets/db_master_key.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  ssl_cert:
    file: ./secrets/ssl_cert.pem
  ssl_key:
    file: ./secrets/ssl_key.pem

services:
  api-gateway:
    image: impactcv/api-gateway
    secrets:
      - db_master_key
      - jwt_secret
      - ssl_cert
      - ssl_key
    environment:
      - DB_MASTER_KEY_FILE=/run/secrets/db_master_key
      - JWT_SECRET_FILE=/run/secrets/jwt_secret
      - SSL_CERT_FILE=/run/secrets/ssl_cert
      - SSL_KEY_FILE=/run/secrets/ssl_key
    volumes:
      - encrypted_data:/app/data:rw
```

### **Container Volume Encryption**

```bash
# Create encrypted volume for sensitive data
#!/bin/bash

# Create encrypted volume using LUKS
sudo cryptsetup luksFormat /dev/sdb1
sudo cryptsetup luksOpen /dev/sdb1 encrypted_volume

# Format and mount
sudo mkfs.ext4 /dev/mapper/encrypted_volume
sudo mkdir -p /mnt/encrypted_data
sudo mount /dev/mapper/encrypted_volume /mnt/encrypted_data

# Set up Docker volume
docker volume create --driver local \
  --opt type=none \
  --opt o=bind \
  --opt device=/mnt/encrypted_data \
  encrypted_data
```

---

## 📊 **ENCRYPTION PERFORMANCE OPTIMIZATION**

### **Performance Monitoring**

```python
# Encryption performance monitoring
import time
from functools import wraps
from prometheus_client import Histogram, Counter

encryption_duration = Histogram("encryption_operation_duration_seconds", ["operation", "algorithm"])
encryption_operations = Counter("encryption_operations_total", ["operation", "status"])

def monitor_encryption(operation: str):
    """Decorator to monitor encryption performance"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                encryption_operations.labels(operation=operation, status="success").inc()
                return result
            except Exception as e:
                encryption_operations.labels(operation=operation, status="error").inc()
                raise
            finally:
                duration = time.time() - start_time
                encryption_duration.labels(operation=operation, algorithm="AES-256").observe(duration)
        return wrapper
    return decorator

# Usage
class OptimizedEncryption:
    @monitor_encryption("field_encryption")
    def encrypt_field(self, data: str) -> str:
        return self.cipher.encrypt(data.encode()).decode()
    
    @monitor_encryption("file_encryption")
    def encrypt_file(self, file_path: Path) -> Path:
        # Optimized file encryption implementation
        pass
```

---

## ✅ **ENCRYPTION COMPLIANCE CHECKLIST**

### **GDPR Compliance**
- [ ] Personal data encrypted at rest (AES-256)
- [ ] Data in transit encrypted (TLS 1.3)
- [ ] Encryption keys properly managed
- [ ] Right to erasure implemented (crypto-shredding)
- [ ] Data breach notification procedures

### **Security Standards**
- [ ] FIPS 140-2 compliant algorithms
- [ ] NIST SP 800-57 key management
- [ ] Perfect Forward Secrecy (PFS)
- [ ] Regular key rotation (90 days)
- [ ] Secure key storage (HSM/Key Vault)

### **Implementation Verification**
- [ ] All sensitive data fields encrypted
- [ ] File uploads encrypted on disk
- [ ] Database connections encrypted
- [ ] Service-to-service mTLS
- [ ] JWT tokens properly secured
- [ ] Backup data encrypted

---

*This comprehensive encryption strategy ensures that all data in the ImpactCV system is protected using industry-standard encryption algorithms and best practices, maintaining confidentiality and integrity throughout the data lifecycle.*
