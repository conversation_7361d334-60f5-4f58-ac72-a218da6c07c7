"""
Embedding Service
Text embedding generation with caching and batch processing
"""

import asyncio
import hashlib
import logging
import time
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import openai
from openai import AsyncOpenAI
from pydantic import BaseModel, Field

from app.core.config import settings

logger = logging.getLogger(__name__)


class EmbeddingRequest(BaseModel):
    """Embedding request model."""
    
    texts: List[str] = Field(..., min_items=1, max_items=100, description="Texts to embed")
    model: str = Field(default="text-embedding-3-small", description="Embedding model")
    user_id: Optional[str] = Field(None, description="User ID for tracking")
    cache_key: Optional[str] = Field(None, description="Custom cache key")


class EmbeddingResponse(BaseModel):
    """Embedding response model."""
    
    embeddings: List[List[float]] = Field(..., description="Generated embeddings")
    model: str = Field(..., description="Model used")
    usage: Dict[str, int] = Field(..., description="Token usage")
    response_time_ms: float = Field(..., description="Response time")
    cache_hit: bool = Field(default=False, description="Whether result was cached")
    cost_estimate: float = Field(..., description="Estimated cost")


class EmbeddingError(Exception):
    """Custom exception for embedding service errors."""
    
    def __init__(self, message: str, error_code: str = "EMBEDDING_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class EmbeddingService:
    """
    Enterprise embedding service with caching and batch processing.
    
    Features:
    - Multiple embedding models support
    - In-memory caching with TTL
    - Batch processing for efficiency
    - Cost tracking and monitoring
    - Error handling and retries
    """
    
    def __init__(self):
        """Initialize embedding service."""
        self.client = None
        self._initialized = False
        self._cache = {}  # Simple in-memory cache
        self._cache_ttl = 3600  # 1 hour TTL
        self._request_count = 0
        self._total_cost = 0.0
        
        # Model pricing (per 1K tokens)
        self.pricing = {
            "text-embedding-3-small": 0.00002,
            "text-embedding-3-large": 0.00013,
            "text-embedding-ada-002": 0.0001,
        }
        
        # Model dimensions
        self.dimensions = {
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072,
            "text-embedding-ada-002": 1536,
        }
    
    async def initialize(self) -> None:
        """Initialize OpenAI client."""
        if self._initialized:
            return
        
        try:
            api_key = settings.OPENAI_API_KEY
            if not api_key:
                raise EmbeddingError(
                    "OpenAI API key not configured",
                    error_code="MISSING_API_KEY"
                )
            
            self.client = AsyncOpenAI(
                api_key=api_key,
                timeout=30.0,
                max_retries=3,
            )
            
            # Test connection
            await self._test_connection()
            
            self._initialized = True
            logger.info("Embedding service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize embedding service: {e}")
            raise EmbeddingError(
                f"Embedding service initialization failed: {str(e)}",
                error_code="INITIALIZATION_FAILED"
            )
    
    async def _test_connection(self) -> None:
        """Test OpenAI API connection."""
        try:
            response = await self.client.embeddings.create(
                model="text-embedding-3-small",
                input=["test"],
            )
            logger.info("OpenAI embeddings API connection test successful")
        except Exception as e:
            raise EmbeddingError(
                f"OpenAI embeddings API connection test failed: {str(e)}",
                error_code="CONNECTION_TEST_FAILED"
            )
    
    async def generate_embeddings(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """
        Generate embeddings for texts.
        
        Args:
            request: Embedding request with texts and parameters
            
        Returns:
            EmbeddingResponse: Generated embeddings with metadata
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            # Validate request
            self._validate_request(request)
            
            # Check cache first
            cache_key = self._generate_cache_key(request)
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info(f"Cache hit for embedding request: {cache_key}")
                return EmbeddingResponse(
                    embeddings=cached_result["embeddings"],
                    model=cached_result["model"],
                    usage=cached_result["usage"],
                    response_time_ms=(time.time() - start_time) * 1000,
                    cache_hit=True,
                    cost_estimate=0.0  # No cost for cached results
                )
            
            # Process in batches if needed
            all_embeddings = []
            total_usage = {"prompt_tokens": 0, "total_tokens": 0}
            
            batch_size = 50  # OpenAI recommended batch size
            for i in range(0, len(request.texts), batch_size):
                batch_texts = request.texts[i:i + batch_size]
                
                # Make API call
                response = await self._make_embedding_call(request.model, batch_texts)
                
                # Extract embeddings
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
                
                # Accumulate usage
                usage = response.usage.model_dump()
                total_usage["prompt_tokens"] += usage.get("prompt_tokens", 0)
                total_usage["total_tokens"] += usage.get("total_tokens", 0)
                
                # Small delay between batches to avoid rate limits
                if i + batch_size < len(request.texts):
                    await asyncio.sleep(0.1)
            
            # Calculate cost
            cost_estimate = self._calculate_cost(request.model, total_usage)
            self._total_cost += cost_estimate
            self._request_count += 1
            
            # Calculate response time
            response_time_ms = (time.time() - start_time) * 1000
            
            # Create response
            embedding_response = EmbeddingResponse(
                embeddings=all_embeddings,
                model=request.model,
                usage=total_usage,
                response_time_ms=response_time_ms,
                cache_hit=False,
                cost_estimate=cost_estimate
            )
            
            # Cache the result
            self._store_in_cache(cache_key, {
                "embeddings": all_embeddings,
                "model": request.model,
                "usage": total_usage,
                "timestamp": time.time()
            })
            
            logger.info(f"Generated embeddings for {len(request.texts)} texts in {response_time_ms:.2f}ms")
            return embedding_response
            
        except openai.RateLimitError as e:
            error_msg = f"Rate limit exceeded: {str(e)}"
            logger.warning(error_msg)
            raise EmbeddingError(error_msg, error_code="RATE_LIMIT_EXCEEDED")
            
        except openai.AuthenticationError as e:
            error_msg = f"Authentication failed: {str(e)}"
            logger.error(error_msg)
            raise EmbeddingError(error_msg, error_code="AUTHENTICATION_FAILED")
            
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            raise EmbeddingError(error_msg, error_code="UNEXPECTED_ERROR")
    
    async def generate_single_embedding(self, text: str, model: str = "text-embedding-3-small") -> List[float]:
        """
        Generate embedding for a single text (convenience method).
        
        Args:
            text: Text to embed
            model: Embedding model to use
            
        Returns:
            List[float]: Embedding vector
        """
        request = EmbeddingRequest(texts=[text], model=model)
        response = await self.generate_embeddings(request)
        return response.embeddings[0]
    
    def _validate_request(self, request: EmbeddingRequest) -> None:
        """Validate embedding request."""
        
        # Check model support
        if request.model not in self.pricing:
            raise EmbeddingError(
                f"Unsupported model: {request.model}",
                error_code="UNSUPPORTED_MODEL"
            )
        
        # Check text lengths
        for i, text in enumerate(request.texts):
            if len(text.strip()) == 0:
                raise EmbeddingError(
                    f"Empty text at index {i}",
                    error_code="EMPTY_TEXT"
                )
            
            # OpenAI has a token limit per text
            if len(text) > 8000:  # Rough character limit
                raise EmbeddingError(
                    f"Text too long at index {i} (max ~8000 characters)",
                    error_code="TEXT_TOO_LONG"
                )
    
    async def _make_embedding_call(self, model: str, texts: List[str]) -> Any:
        """Make embedding API call with retry logic."""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                response = await self.client.embeddings.create(
                    model=model,
                    input=texts,
                    timeout=30.0
                )
                return response
                
            except (openai.RateLimitError, openai.APITimeoutError) as e:
                if attempt == max_retries - 1:
                    raise
                
                wait_time = (2 ** attempt) + (attempt * 0.1)
                logger.warning(f"Embedding API call failed (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                await asyncio.sleep(wait_time)
            
            except Exception as e:
                raise
    
    def _generate_cache_key(self, request: EmbeddingRequest) -> str:
        """Generate cache key for request."""
        if request.cache_key:
            return request.cache_key
        
        # Create hash from texts and model
        content = f"{request.model}:{':'.join(request.texts)}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_from_cache(self, cache_key: str) -> Optional[Dict]:
        """Get result from cache if not expired."""
        if cache_key not in self._cache:
            return None
        
        cached_item = self._cache[cache_key]
        if time.time() - cached_item["timestamp"] > self._cache_ttl:
            del self._cache[cache_key]
            return None
        
        return cached_item
    
    def _store_in_cache(self, cache_key: str, data: Dict) -> None:
        """Store result in cache."""
        self._cache[cache_key] = data
        
        # Simple cache cleanup - remove oldest items if cache gets too large
        if len(self._cache) > 1000:
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k]["timestamp"])
            del self._cache[oldest_key]
    
    def _calculate_cost(self, model: str, usage: Dict[str, int]) -> float:
        """Calculate estimated cost for the request."""
        if model not in self.pricing:
            return 0.0
        
        tokens = usage.get("total_tokens", 0)
        cost = (tokens / 1000) * self.pricing[model]
        return round(cost, 6)
    
    def clear_cache(self) -> None:
        """Clear the embedding cache."""
        self._cache.clear()
        logger.info("Embedding cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cache_size": len(self._cache),
            "cache_ttl": self._cache_ttl,
        }
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        return {
            "initialized": self._initialized,
            "request_count": self._request_count,
            "total_cost": self._total_cost,
            "supported_models": list(self.pricing.keys()),
            "cache_stats": self.get_cache_stats(),
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Test embedding generation
            start_time = time.time()
            await self.generate_single_embedding("test", "text-embedding-3-small")
            response_time = (time.time() - start_time) * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "cache_size": len(self._cache),
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
            }
    
    def cosine_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            float: Cosine similarity score (-1 to 1)
        """
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return float(dot_product / (norm1 * norm2))


# Global service instance
embedding_service = EmbeddingService()
