"""
CV Models
Comprehensive CV and resume management with AI integration
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, Integer, String, Text, JSON
from sqlalchemy.orm import relationship

from app.models.base import Base


class CVTemplate(Base):
    """
    CV template model for standardized CV formats.
    
    Stores reusable CV templates with:
    - Layout and styling information
    - Section configurations
    - AI generation prompts
    """
    
    # Template information
    name = Column(
        String(150),
        nullable=False,
        doc="Template name"
    )
    
    description = Column(
        Text,
        nullable=True,
        doc="Template description"
    )
    
    category = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Template category (e.g., 'professional', 'creative', 'academic')"
    )
    
    # Template configuration
    layout_config = Column(
        JSON,
        nullable=False,
        doc="Layout configuration as JSON"
    )

    style_config = Column(
        JSON,
        nullable=False,
        doc="Styling configuration as JSON"
    )

    sections_config = Column(
        JSON,
        nullable=False,
        doc="Sections configuration as JSON"
    )
    
    # AI generation settings
    ai_prompt_template = Column(
        Text,
        nullable=True,
        doc="AI prompt template for content generation"
    )
    
    ai_generation_settings = Column(
        JSON,
        nullable=True,
        doc="AI generation settings as JSON"
    )
    
    # Template metadata
    is_public = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether the template is publicly available"
    )
    
    is_premium = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the template requires premium access"
    )
    
    usage_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times this template has been used"
    )
    
    # Relationships
    cvs = relationship("CV", back_populates="template")
    
    def __repr__(self) -> str:
        """String representation of the template."""
        return f"<CVTemplate(name={self.name}, category={self.category})>"


class CV(Base):
    """
    Main CV model representing a user's resume.
    
    Stores:
    - CV metadata and settings
    - Generated content
    - AI generation history
    - Export formats
    """
    
    # Foreign keys
    user_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the CV owner"
    )

    template_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("cv_template.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="Reference to the CV template used"
    )
    
    # CV information
    title = Column(
        String(200),
        nullable=False,
        doc="CV title or name"
    )
    
    description = Column(
        Text,
        nullable=True,
        doc="CV description or notes"
    )
    
    # CV status
    status = Column(
        String(20),
        default="draft",
        nullable=False,
        index=True,
        doc="CV status: draft, published, archived"
    )
    
    is_public = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether the CV is publicly viewable"
    )
    
    # Content
    content_json = Column(
        JSON,
        nullable=True,
        doc="CV content as structured JSON"
    )
    
    content_html = Column(
        Text,
        nullable=True,
        doc="Generated HTML content"
    )
    
    content_markdown = Column(
        Text,
        nullable=True,
        doc="Generated Markdown content"
    )
    
    # AI generation metadata
    ai_generated = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether content was AI-generated"
    )
    
    ai_generation_prompt = Column(
        Text,
        nullable=True,
        doc="Prompt used for AI generation"
    )
    
    ai_generation_settings = Column(
        JSON,
        nullable=True,
        doc="AI generation settings used"
    )
    
    ai_generation_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When AI generation was performed"
    )
    
    # Export and sharing
    pdf_url = Column(
        String(500),
        nullable=True,
        doc="URL to generated PDF"
    )
    
    share_token = Column(
        String(100),
        unique=True,
        nullable=True,
        index=True,
        doc="Token for public sharing"
    )
    
    share_expires_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the share link expires"
    )
    
    # Analytics
    view_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times CV has been viewed"
    )
    
    download_count = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of times CV has been downloaded"
    )
    
    last_viewed = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Last time CV was viewed"
    )
    
    # Relationships
    user = relationship("User", back_populates="cvs")
    template = relationship("CVTemplate", back_populates="cvs")
    sections = relationship("CVSection", back_populates="cv", cascade="all, delete-orphan")
    experiences = relationship("CVExperience", back_populates="cv", cascade="all, delete-orphan")
    educations = relationship("CVEducation", back_populates="cv", cascade="all, delete-orphan")
    skills = relationship("CVSkill", back_populates="cv", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        """String representation of the CV."""
        return f"<CV(title={self.title}, user_id={self.user_id})>"
    
    @property
    def is_shared(self) -> bool:
        """Check if CV is currently shared."""
        if not self.share_token or not self.share_expires_at:
            return False
        return datetime.utcnow() < self.share_expires_at
    
    def generate_share_token(self, expires_in_days: int = 30) -> str:
        """Generate a new share token."""
        import secrets
        self.share_token = secrets.token_urlsafe(32)
        self.share_expires_at = datetime.utcnow() + datetime.timedelta(days=expires_in_days)
        return self.share_token
    
    def revoke_share_token(self) -> None:
        """Revoke the current share token."""
        self.share_token = None
        self.share_expires_at = None


class CVSection(Base):
    """
    CV section model for organizing CV content.
    
    Represents different sections of a CV like:
    - Summary/Objective
    - Experience
    - Education
    - Skills
    - Custom sections
    """
    
    # Foreign key
    cv_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("cv.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the CV"
    )
    
    # Section information
    section_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Type of section (summary, experience, education, skills, custom)"
    )
    
    title = Column(
        String(150),
        nullable=False,
        doc="Section title"
    )
    
    content = Column(
        Text,
        nullable=True,
        doc="Section content"
    )
    
    # Section configuration
    order_index = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Order of section in CV"
    )
    
    is_visible = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether section is visible in CV"
    )
    
    style_config = Column(
        JSON,
        nullable=True,
        doc="Section-specific styling configuration"
    )
    
    # AI generation
    ai_generated = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether content was AI-generated"
    )
    
    ai_prompt = Column(
        Text,
        nullable=True,
        doc="AI prompt used for this section"
    )
    
    # Relationships
    cv = relationship("CV", back_populates="sections")
    
    def __repr__(self) -> str:
        """String representation of the section."""
        return f"<CVSection(type={self.section_type}, title={self.title})>"


class CVExperience(Base):
    """
    Work experience entries for CVs.
    """
    
    # Foreign key
    cv_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("cv.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the CV"
    )
    
    # Experience information
    job_title = Column(
        String(150),
        nullable=False,
        doc="Job title or position"
    )
    
    company = Column(
        String(150),
        nullable=False,
        doc="Company or organization name"
    )
    
    location = Column(
        String(100),
        nullable=True,
        doc="Job location"
    )
    
    # Dates
    start_date = Column(
        DateTime(timezone=True),
        nullable=False,
        doc="Start date of employment"
    )
    
    end_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="End date of employment (null if current)"
    )
    
    is_current = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this is current employment"
    )
    
    # Content
    description = Column(
        Text,
        nullable=True,
        doc="Job description and responsibilities"
    )
    
    achievements = Column(
        JSON,
        nullable=True,
        doc="List of achievements and accomplishments (stored as JSON array)"
    )

    technologies = Column(
        JSON,
        nullable=True,
        doc="Technologies and tools used (stored as JSON array)"
    )
    
    # Configuration
    order_index = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Order of experience in CV"
    )
    
    is_visible = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether experience is visible in CV"
    )
    
    # AI generation
    ai_generated = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether content was AI-generated"
    )
    
    # Relationships
    cv = relationship("CV", back_populates="experiences")
    
    def __repr__(self) -> str:
        """String representation of the experience."""
        return f"<CVExperience(title={self.job_title}, company={self.company})>"


class CVEducation(Base):
    """
    Education entries for CVs.
    """
    
    # Foreign key
    cv_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("cv.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the CV"
    )
    
    # Education information
    degree = Column(
        String(150),
        nullable=False,
        doc="Degree or qualification"
    )
    
    field_of_study = Column(
        String(150),
        nullable=True,
        doc="Field of study or major"
    )
    
    institution = Column(
        String(150),
        nullable=False,
        doc="Educational institution"
    )
    
    location = Column(
        String(100),
        nullable=True,
        doc="Institution location"
    )
    
    # Dates
    start_date = Column(
        DateTime(timezone=True),
        nullable=False,
        doc="Start date of education"
    )
    
    end_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="End date of education (null if ongoing)"
    )
    
    is_current = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether this is current education"
    )
    
    # Academic details
    gpa = Column(
        String(10),
        nullable=True,
        doc="Grade Point Average"
    )
    
    honors = Column(
        String(100),
        nullable=True,
        doc="Academic honors or distinctions"
    )
    
    description = Column(
        Text,
        nullable=True,
        doc="Additional description or coursework"
    )
    
    # Configuration
    order_index = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Order of education in CV"
    )
    
    is_visible = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether education is visible in CV"
    )
    
    # Relationships
    cv = relationship("CV", back_populates="educations")
    
    def __repr__(self) -> str:
        """String representation of the education."""
        return f"<CVEducation(degree={self.degree}, institution={self.institution})>"


class CVSkill(Base):
    """
    Skills entries for CVs.
    """
    
    # Foreign key
    cv_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("cv.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the CV"
    )
    
    # Skill information
    name = Column(
        String(100),
        nullable=False,
        doc="Skill name"
    )
    
    category = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Skill category (e.g., 'technical', 'language', 'soft')"
    )
    
    level = Column(
        String(20),
        nullable=True,
        doc="Skill level (e.g., 'beginner', 'intermediate', 'advanced', 'expert')"
    )
    
    proficiency_score = Column(
        Integer,
        nullable=True,
        doc="Proficiency score (1-10 or 1-100)"
    )
    
    # Additional details
    description = Column(
        Text,
        nullable=True,
        doc="Skill description or context"
    )
    
    years_experience = Column(
        Integer,
        nullable=True,
        doc="Years of experience with this skill"
    )
    
    # Configuration
    order_index = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Order of skill in CV"
    )
    
    is_visible = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether skill is visible in CV"
    )
    
    # Relationships
    cv = relationship("CV", back_populates="skills")
    
    def __repr__(self) -> str:
        """String representation of the skill."""
        return f"<CVSkill(name={self.name}, level={self.level})>"
