#!/usr/bin/env python3
"""
Debug script for CV section extraction
"""

from app.services.document_parser import DocumentParser, DocumentContent

def debug_section_extraction():
    cv_text = """
        <PERSON>
        Software Engineer
        
        SUMMARY
        Experienced software engineer with 5 years in web development.
        
        EXPERIENCE
        Senior Developer at Tech Corp (2020-2023)
        - Led development team
        - Built scalable applications
        
        EDUCATION
        BS Computer Science, University (2016-2020)
        
        SKILLS
        Python, JavaScript, React, Node.js
        
        CONTACT
        <EMAIL>
        (555) 123-4567
        """

    content = DocumentContent(text=cv_text, metadata={}, pages=[], formatting={}, images=[])
    parser = DocumentParser()
    
    print("=== DEBUG: CV Section Extraction ===")
    print(f"Original text length: {len(cv_text)}")
    print("\n=== Lines in text ===")
    lines = cv_text.split('\n')
    for i, line in enumerate(lines):
        print(f"{i:2d}: '{line.strip()}' -> lower: '{line.lower().strip()}'")
    
    print("\n=== Section patterns ===")
    section_patterns = {
        'summary': ['summary', 'profile', 'objective', 'about'],
        'experience': ['experience', 'employment', 'work history', 'career'],
        'education': ['education', 'academic', 'qualifications'],
        'skills': ['skills', 'competencies', 'expertise', 'technologies'],
        'certifications': ['certifications', 'certificates', 'licenses'],
        'projects': ['projects', 'portfolio', 'achievements'],
        'contact': ['contact', 'personal details', 'information'],
    }
    
    for section_name, patterns in section_patterns.items():
        print(f"{section_name}: {patterns}")
    
    print("\n=== Pattern matching ===")
    for i, line in enumerate(lines):
        line_lower = line.lower().strip()
        if line_lower:
            for section_name, patterns in section_patterns.items():
                if any(pattern in line_lower for pattern in patterns):
                    print(f"Line {i}: '{line.strip()}' -> MATCHES {section_name} (pattern: {[p for p in patterns if p in line_lower]})")
                    break
    
    print("\n=== Extracted sections ===")
    sections = parser.extract_cv_sections(content)
    print(f"Sections found: {list(sections.keys())}")
    for key, value in sections.items():
        print(f"{key}: '{value[:100]}{'...' if len(value) > 100 else ''}'")

if __name__ == "__main__":
    debug_section_extraction()
