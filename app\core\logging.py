"""
ImpactCV Logging Configuration
Secure, structured logging with PII sanitization and audit capabilities
"""

import json
import logging
import logging.handlers
import os
import re
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from app.core.config import settings


class PIISanitizer:
    """
    PII (Personally Identifiable Information) sanitizer for logs.
    Removes or masks sensitive information from log messages.
    """
    
    # Regex patterns for common PII
    EMAIL_PATTERN = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
    PHONE_PATTERN = re.compile(r'\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b')
    SSN_PATTERN = re.compile(r'\b\d{3}-?\d{2}-?\d{4}\b')
    CREDIT_CARD_PATTERN = re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b')
    API_KEY_PATTERN = re.compile(r'\b[A-Za-z0-9]{32,}\b')
    JWT_PATTERN = re.compile(r'\beyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*\b')
    
    @classmethod
    def sanitize(cls, message: str) -> str:
        """
        Sanitize log message by removing or masking PII.
        
        Args:
            message: Original log message
            
        Returns:
            str: Sanitized log message
        """
        # Replace email addresses
        message = cls.EMAIL_PATTERN.sub('[EMAIL_REDACTED]', message)
        
        # Replace phone numbers
        message = cls.PHONE_PATTERN.sub('[PHONE_REDACTED]', message)
        
        # Replace SSNs
        message = cls.SSN_PATTERN.sub('[SSN_REDACTED]', message)
        
        # Replace credit card numbers
        message = cls.CREDIT_CARD_PATTERN.sub('[CARD_REDACTED]', message)
        
        # Replace API keys
        message = cls.API_KEY_PATTERN.sub('[API_KEY_REDACTED]', message)
        
        # Replace JWT tokens
        message = cls.JWT_PATTERN.sub('[JWT_REDACTED]', message)
        
        return message


class StructuredFormatter(logging.Formatter):
    """
    Structured JSON formatter for logs with security features.
    """
    
    def __init__(self, include_extra: bool = True):
        """
        Initialize structured formatter.
        
        Args:
            include_extra: Whether to include extra fields in log records
        """
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record as structured JSON.
        
        Args:
            record: Log record to format
            
        Returns:
            str: Formatted JSON log message
        """
        # Base log structure
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": PIISanitizer.sanitize(record.getMessage()),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add process and thread info
        log_entry.update({
            "process_id": os.getpid(),
            "thread_id": record.thread,
            "thread_name": record.threadName,
        })
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info),
            }
        
        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in {
                    'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                    'filename', 'module', 'exc_info', 'exc_text', 'stack_info',
                    'lineno', 'funcName', 'created', 'msecs', 'relativeCreated',
                    'thread', 'threadName', 'processName', 'process', 'message'
                }:
                    # Sanitize extra field values
                    if isinstance(value, str):
                        value = PIISanitizer.sanitize(value)
                    extra_fields[key] = value
            
            if extra_fields:
                log_entry["extra"] = extra_fields
        
        # Add application context
        log_entry["application"] = {
            "name": "ImpactCV",
            "version": "1.0.0",
            "environment": settings.ENVIRONMENT,
        }
        
        return json.dumps(log_entry, default=str, ensure_ascii=False)


class TextFormatter(logging.Formatter):
    """
    Human-readable text formatter with PII sanitization.
    """
    
    def __init__(self):
        """Initialize text formatter."""
        super().__init__(
            fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record as human-readable text.
        
        Args:
            record: Log record to format
            
        Returns:
            str: Formatted text log message
        """
        # Sanitize the message
        original_msg = record.msg
        if isinstance(record.msg, str):
            record.msg = PIISanitizer.sanitize(record.msg)
        
        # Format the record
        formatted = super().format(record)
        
        # Restore original message
        record.msg = original_msg
        
        return formatted


class SecurityAuditLogger:
    """
    Dedicated logger for security events and audit trails.
    """
    
    def __init__(self):
        """Initialize security audit logger."""
        self.logger = logging.getLogger("security_audit")
        self.logger.setLevel(logging.INFO)
        
        # Create security log file handler
        if settings.LOG_FILE_PATH:
            log_dir = Path(settings.LOG_FILE_PATH).parent
            log_dir.mkdir(parents=True, exist_ok=True)
            
            security_log_path = log_dir / "security_audit.log"
            
            handler = logging.handlers.RotatingFileHandler(
                security_log_path,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=10,
                encoding="utf-8"
            )
            
            handler.setFormatter(StructuredFormatter())
            self.logger.addHandler(handler)
    
    def log_authentication_attempt(
        self,
        user_id: Optional[str] = None,
        email: Optional[str] = None,
        success: bool = False,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        failure_reason: Optional[str] = None
    ) -> None:
        """
        Log authentication attempt.
        
        Args:
            user_id: User identifier
            email: User email (will be sanitized)
            success: Whether authentication was successful
            ip_address: Client IP address
            user_agent: Client user agent
            failure_reason: Reason for failure if applicable
        """
        self.logger.info(
            "Authentication attempt",
            extra={
                "event_type": "authentication",
                "user_id": user_id,
                "email": "[EMAIL_REDACTED]" if email else None,
                "success": success,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "failure_reason": failure_reason,
            }
        )
    
    def log_authorization_failure(
        self,
        user_id: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> None:
        """
        Log authorization failure.
        
        Args:
            user_id: User identifier
            resource: Resource being accessed
            action: Action being attempted
            ip_address: Client IP address
        """
        self.logger.warning(
            "Authorization failure",
            extra={
                "event_type": "authorization_failure",
                "user_id": user_id,
                "resource": resource,
                "action": action,
                "ip_address": ip_address,
            }
        )
    
    def log_data_access(
        self,
        user_id: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        record_count: Optional[int] = None
    ) -> None:
        """
        Log data access for audit purposes.
        
        Args:
            user_id: User identifier
            resource: Resource being accessed
            action: Action performed
            record_count: Number of records accessed
        """
        self.logger.info(
            "Data access",
            extra={
                "event_type": "data_access",
                "user_id": user_id,
                "resource": resource,
                "action": action,
                "record_count": record_count,
            }
        )
    
    def log_security_event(
        self,
        event_type: str,
        severity: str = "INFO",
        description: str = "",
        **kwargs
    ) -> None:
        """
        Log general security event.
        
        Args:
            event_type: Type of security event
            severity: Event severity
            description: Event description
            **kwargs: Additional event data
        """
        log_method = getattr(self.logger, severity.lower(), self.logger.info)
        log_method(
            description or f"Security event: {event_type}",
            extra={
                "event_type": event_type,
                "severity": severity,
                **kwargs
            }
        )


def setup_logging() -> logging.Logger:
    """
    Setup application logging configuration.
    
    Returns:
        logging.Logger: Configured root logger
    """
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
    
    # Choose formatter based on configuration
    if settings.LOG_FORMAT == "json":
        console_formatter = StructuredFormatter()
    else:
        console_formatter = TextFormatter()
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if configured)
    if settings.LOG_FILE_PATH:
        # Create log directory
        log_dir = Path(settings.LOG_FILE_PATH).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Rotating file handler
        file_handler = logging.handlers.RotatingFileHandler(
            settings.LOG_FILE_PATH,
            maxBytes=_parse_size(settings.LOG_ROTATION_SIZE),
            backupCount=settings.LOG_RETENTION_DAYS,
            encoding="utf-8"
        )
        
        file_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))
        file_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(file_handler)
    
    # Configure third-party loggers
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    
    # Suppress noisy loggers in production
    if settings.is_production():
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        logging.getLogger("requests").setLevel(logging.WARNING)
    
    return root_logger


def _parse_size(size_str: str) -> int:
    """
    Parse size string to bytes.
    
    Args:
        size_str: Size string (e.g., "10MB", "1GB")
        
    Returns:
        int: Size in bytes
    """
    size_str = size_str.upper()
    
    if size_str.endswith("KB"):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith("MB"):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith("GB"):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


# Global security audit logger instance
security_audit_logger = SecurityAuditLogger()
