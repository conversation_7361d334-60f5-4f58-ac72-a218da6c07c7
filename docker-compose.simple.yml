version: '3.8'

services:
  # =============================================================================
  # DATABASES & STORAGE
  # =============================================================================
  
  postgresql:
    image: postgres:15-alpine
    container_name: impactcv-postgres
    environment:
      POSTGRES_DB: impactcv
      POSTGRES_USER: impactcv_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-dev_postgres_123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U impactcv_user -d impactcv"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - impactcv-network

  redis:
    image: redis:7-alpine
    container_name: impactcv-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-dev_redis_123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - impactcv-network

  # =============================================================================
  # MAIN APPLICATION
  # =============================================================================

  impactcv-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: impactcv-app
    environment:
      - DATABASE_URL=postgresql://impactcv_user:${POSTGRES_PASSWORD:-dev_postgres_123}@postgresql:5432/impactcv
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev_jwt_secret_key_very_long_and_secure}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=INFO
    ports:
      - "8000:8000"
    volumes:
      - ./app:/app/app
      - ./config:/app/config
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./storage:/app/storage
    depends_on:
      postgresql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - impactcv-network

  # =============================================================================
  # MONITORING (OPTIONAL)
  # =============================================================================

  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: impactcv-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped
    networks:
      - impactcv-network

  grafana:
    image: grafana/grafana:10.2.0
    container_name: impactcv-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - impactcv-network

# =============================================================================
# VOLUMES
# =============================================================================

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# =============================================================================
# NETWORKS
# =============================================================================

networks:
  impactcv-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
