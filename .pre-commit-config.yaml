# ImpactCV Pre-commit Configuration
# Automated code quality and security checks before commits

repos:
  # ============================================================================
  # GENERAL CODE QUALITY
  # ============================================================================
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--allow-multiple-documents]
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-added-large-files
        args: [--maxkb=1000]
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: detect-private-key
      - id: detect-aws-credentials
      - id: mixed-line-ending
        args: [--fix=lf]

  # ============================================================================
  # PYTHON CODE FORMATTING
  # ============================================================================
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  - repo: https://github.com/pycqa/autoflake
    rev: v2.2.1
    hooks:
      - id: autoflake
        args:
          - --in-place
          - --remove-all-unused-imports
          - --remove-unused-variables
          - --remove-duplicate-keys
          - --ignore-init-module-imports

  # ============================================================================
  # PYTHON CODE LINTING
  # ============================================================================
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args:
          - --max-line-length=88
          - --extend-ignore=E203,W503,E501
          - --exclude=migrations,alembic/versions
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        args: [--ignore-missing-imports, --no-strict-optional]
        additional_dependencies:
          - types-requests
          - types-redis
          - types-python-dateutil
          - types-PyYAML

  # ============================================================================
  # SECURITY SCANNING
  # ============================================================================
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args:
          - --config=.bandit
          - --recursive
          - --exclude=tests,test,migrations,alembic/versions
        files: \.py$

  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .secrets.baseline]
        exclude: |
          (?x)^(
              .*\.lock$|
              .*\.min\.js$|
              .*\.min\.css$|
              package-lock\.json$|
              yarn\.lock$
          )$

  - repo: https://github.com/gitguardian/ggshield
    rev: v1.25.0
    hooks:
      - id: ggshield
        language: python
        stages: [commit]

  # ============================================================================
  # DEPENDENCY SECURITY
  # ============================================================================
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        args: [--ignore=70612]  # Add CVE IDs to ignore if needed

  # ============================================================================
  # DOCKER SECURITY
  # ============================================================================
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # ============================================================================
  # YAML/JSON VALIDATION
  # ============================================================================
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.33.0
    hooks:
      - id: yamllint
        args: [-c=.yamllint.yml]

  # ============================================================================
  # TERRAFORM VALIDATION
  # ============================================================================
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.83.6
    hooks:
      - id: terraform_fmt
      - id: terraform_validate
      - id: terraform_docs
      - id: terraform_tflint
        args:
          - --args=--config=__GIT_WORKING_DIR__/.tflint.hcl

  # ============================================================================
  # KUBERNETES VALIDATION
  # ============================================================================
  - repo: https://github.com/Lucas-C/pre-commit-hooks-markup
    rev: v1.0.1
    hooks:
      - id: rst-backticks
      - id: rst-directive-colons
      - id: rst-inline-touching-normal

  # ============================================================================
  # COMMIT MESSAGE VALIDATION
  # ============================================================================
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.12.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # ============================================================================
  # DOCUMENTATION
  # ============================================================================
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args:
          - --convention=google
          - --add-ignore=D100,D101,D102,D103,D104,D105,D106,D107
        exclude: |
          (?x)^(
              tests/.*|
              migrations/.*|
              alembic/versions/.*
          )$

  # ============================================================================
  # CUSTOM SECURITY HOOKS
  # ============================================================================
  - repo: local
    hooks:
      - id: custom-security-scan
        name: Custom Security Scan
        entry: python scripts/security/run_security_scan.py
        args: [--tool=bandit, --project-root=.]
        language: system
        files: \.py$
        pass_filenames: false

      - id: check-api-keys
        name: Check for API Keys
        entry: python scripts/security/check_api_keys.py
        language: system
        files: \.py$
        pass_filenames: false

      - id: validate-security-config
        name: Validate Security Configuration
        entry: python scripts/security/validate_config.py
        language: system
        files: |
          (?x)^(
              \.bandit$|
              \.semgrep\.yml$|
              \.semgrepignore$|
              security/.*\.py$
          )$
        pass_filenames: false

  # ============================================================================
  # PERFORMANCE CHECKS
  # ============================================================================
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.15.0
    hooks:
      - id: pyupgrade
        args: [--py311-plus]

  - repo: https://github.com/pycqa/vulture
    rev: v2.10
    hooks:
      - id: vulture
        args: [--min-confidence=80]
        exclude: |
          (?x)^(
              tests/.*|
              migrations/.*|
              alembic/versions/.*
          )$

# ============================================================================
# PRE-COMMIT CONFIGURATION
# ============================================================================
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: "3.0.0"

# ============================================================================
# CI CONFIGURATION
# ============================================================================
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
