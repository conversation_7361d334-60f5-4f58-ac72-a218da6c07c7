# ImpactCV AI-Powered CV Generation System
# Docker Compose for Testing Environment

version: '3.8'

services:
  # ============================================================================
  # APPLICATION UNDER TEST
  # ============================================================================
  app-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: impactcv-test-app
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=testing
      - DATABASE_URL=***************************************************/test_db
      - REDIS_URL=redis://redis-test:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY:-test-key}
      - JWT_SECRET_KEY=test-secret-key-for-testing-only
      - CORS_ORIGINS=*
      - LOG_LEVEL=DEBUG
    volumes:
      - ./app:/app/app
      - ./tests:/app/tests
      - ./test-results:/app/test-results
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - test-network
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        python scripts/wait_for_db.py &&
        echo 'Running database migrations...' &&
        alembic upgrade head &&
        echo 'Starting application...' &&
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 30s

  # ============================================================================
  # TEST DATABASE
  # ============================================================================
  postgres-test:
    image: postgres:15-alpine
    container_name: impactcv-test-postgres
    environment:
      POSTGRES_DB: test_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_pass
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - ./scripts/db/test_init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    networks:
      - test-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_db"]
      interval: 5s
      timeout: 3s
      retries: 10
      start_period: 10s

  redis-test:
    image: redis:7-alpine
    container_name: impactcv-test-redis
    command: redis-server --appendonly no
    ports:
      - "6380:6379"
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 10
      start_period: 10s

  # ============================================================================
  # TESTING SERVICES
  # ============================================================================
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: impactcv-test-runner
    environment:
      - ENVIRONMENT=testing
      - DATABASE_URL=***************************************************/test_db
      - REDIS_URL=redis://redis-test:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY:-test-key}
      - JWT_SECRET_KEY=test-secret-key-for-testing-only
      - PYTHONPATH=/app
    volumes:
      - ./app:/app/app
      - ./tests:/app/tests
      - ./test-results:/app/test-results
      - ./coverage-reports:/app/coverage-reports
    depends_on:
      app-test:
        condition: service_healthy
    networks:
      - test-network
    command: >
      sh -c "
        echo 'Running test suite...' &&
        pytest tests/ -v \
          --cov=app \
          --cov-report=html:/app/coverage-reports/html \
          --cov-report=xml:/app/coverage-reports/coverage.xml \
          --cov-report=term-missing \
          --cov-fail-under=90 \
          --junitxml=/app/test-results/junit.xml \
          --html=/app/test-results/report.html \
          --self-contained-html
      "

  # ============================================================================
  # SECURITY TESTING
  # ============================================================================
  security-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: impactcv-security-test
    environment:
      - TARGET_URL=http://app-test:8000
    volumes:
      - ./security-reports:/app/security-reports
    depends_on:
      app-test:
        condition: service_healthy
    networks:
      - test-network
    command: >
      sh -c "
        echo 'Running security tests...' &&
        pip install bandit safety &&
        echo 'Running Bandit security scan...' &&
        bandit -r app/ -f json -o /app/security-reports/bandit-report.json &&
        echo 'Running Safety dependency check...' &&
        safety check --json --output /app/security-reports/safety-report.json &&
        echo 'Security tests completed'
      "

  # ============================================================================
  # PERFORMANCE TESTING
  # ============================================================================
  performance-test:
    image: locustio/locust:latest
    container_name: impactcv-performance-test
    environment:
      - TARGET_HOST=http://app-test:8000
    volumes:
      - ./tests/performance:/mnt/locust
      - ./performance-reports:/app/performance-reports
    depends_on:
      app-test:
        condition: service_healthy
    networks:
      - test-network
    command: >
      sh -c "
        echo 'Running performance tests...' &&
        locust -f /mnt/locust/locustfile.py \
          --host=http://app-test:8000 \
          --users=10 \
          --spawn-rate=2 \
          --run-time=60s \
          --headless \
          --html=/app/performance-reports/performance-report.html \
          --csv=/app/performance-reports/performance
      "

  # ============================================================================
  # API TESTING
  # ============================================================================
  api-test:
    image: postman/newman:latest
    container_name: impactcv-api-test
    volumes:
      - ./tests/api:/etc/newman
      - ./api-test-reports:/app/api-test-reports
    depends_on:
      app-test:
        condition: service_healthy
    networks:
      - test-network
    command: >
      sh -c "
        echo 'Running API tests...' &&
        newman run /etc/newman/ImpactCV-API-Tests.postman_collection.json \
          --environment /etc/newman/test-environment.postman_environment.json \
          --reporters cli,html,junit \
          --reporter-html-export /app/api-test-reports/api-test-report.html \
          --reporter-junit-export /app/api-test-reports/api-test-results.xml \
          --timeout 30000 \
          --delay-request 1000
      "

  # ============================================================================
  # ACCESSIBILITY TESTING
  # ============================================================================
  accessibility-test:
    image: node:18-alpine
    container_name: impactcv-accessibility-test
    volumes:
      - ./tests/accessibility:/app/tests
      - ./accessibility-reports:/app/accessibility-reports
    depends_on:
      app-test:
        condition: service_healthy
    networks:
      - test-network
    working_dir: /app/tests
    command: >
      sh -c "
        echo 'Installing accessibility testing tools...' &&
        npm install -g @axe-core/cli pa11y &&
        echo 'Running accessibility tests...' &&
        axe http://app-test:8000 --save /app/accessibility-reports/axe-report.json &&
        pa11y http://app-test:8000 --reporter json > /app/accessibility-reports/pa11y-report.json &&
        echo 'Accessibility tests completed'
      "

  # ============================================================================
  # INTEGRATION TESTING
  # ============================================================================
  integration-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: impactcv-integration-test
    environment:
      - ENVIRONMENT=testing
      - DATABASE_URL=***************************************************/test_db
      - REDIS_URL=redis://redis-test:6379/0
      - API_BASE_URL=http://app-test:8000
      - OPENAI_API_KEY=${OPENAI_API_KEY:-test-key}
    volumes:
      - ./tests/integration:/app/tests/integration
      - ./integration-reports:/app/integration-reports
    depends_on:
      app-test:
        condition: service_healthy
    networks:
      - test-network
    command: >
      sh -c "
        echo 'Running integration tests...' &&
        pytest tests/integration/ -v \
          --junitxml=/app/integration-reports/integration-results.xml \
          --html=/app/integration-reports/integration-report.html \
          --self-contained-html \
          --tb=short
      "

# ============================================================================
# NETWORKS
# ============================================================================
networks:
  test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
