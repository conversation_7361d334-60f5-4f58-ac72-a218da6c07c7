// Lazy loading service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class LazyLoadService {
    constructor() {
        this.containers = new Map();
        this.subscribers = new Set();
        this.observers = new Map();
        this.initialize();
    }

    /**
     * Initialize the lazy loading service
     */
    initialize() {
        // Create intersection observer
        this.observer = new IntersectionObserver(
            this.handleIntersection.bind(this),
            {
                root: null,
                rootMargin: '50px',
                threshold: 0.1,
            }
        );
    }

    /**
     * Subscribe to lazy loading events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of lazy loading events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in lazy loading subscriber:', error);
            }
        });
    }

    /**
     * Register a lazy loading container
     * @param {string} id - The container ID
     * @param {Object} options - The lazy loading options
     */
    registerContainer(id, options = {}) {
        const container = document.getElementById(id);
        if (!container) {
            console.error(`Container ${id} not found`);
            return;
        }

        // Find lazy elements
        const elements = container.querySelectorAll(options.selector || '[data-lazy]');
        elements.forEach(element => {
            element.classList.add('lazy-loading');
            this.observer.observe(element);
        });

        this.containers.set(id, {
            ...options,
            container,
            elements: Array.from(elements),
            loaded: new Set(),
        });
    }

    /**
     * Handle intersection events
     * @param {IntersectionObserverEntry[]} entries - The intersection entries
     */
    handleIntersection(entries) {
        return measurePerformance('lazy_intersection', () => {
            entries.forEach(entry => {
                if (!entry.isIntersecting) {
                    return;
                }

                const element = entry.target;
                const container = element.closest('[id]');
                if (!container) {
                    return;
                }

                const id = container.id;
                const lazy = this.containers.get(id);
                if (!lazy || lazy.loaded.has(element)) {
                    return;
                }

                this.loadElement(id, element);
            });
        });
    }

    /**
     * Load a lazy element
     * @param {string} id - The container ID
     * @param {HTMLElement} element - The element to load
     */
    async loadElement(id, element) {
        return measurePerformance('lazy_load', async () => {
            const lazy = this.containers.get(id);
            if (!lazy || lazy.loaded.has(element)) {
                return;
            }

            try {
                // Get source
                const source = element.dataset.lazy;
                if (!source) {
                    return;
                }

                // Load content
                if (element.tagName === 'IMG') {
                    element.src = source;
                } else if (element.tagName === 'VIDEO') {
                    element.src = source;
                } else if (element.tagName === 'IFRAME') {
                    element.src = source;
                } else {
                    const response = await fetch(source);
                    const content = await response.text();
                    element.innerHTML = content;
                }

                // Mark as loaded
                element.classList.remove('lazy-loading');
                element.classList.add('lazy-loaded');
                lazy.loaded.add(element);

                if (lazy.onLoad) {
                    lazy.onLoad({
                        container: lazy.container,
                        element,
                        source,
                    });
                }

                this.notifySubscribers('load', { id, element, source });
            } catch (error) {
                console.error('Error loading element:', error);

                if (lazy.onError) {
                    lazy.onError(error, {
                        container: lazy.container,
                        element,
                    });
                }

                this.notifySubscribers('error', { id, element, error });
            }
        });
    }

    /**
     * Load all elements in a container
     * @param {string} id - The container ID
     */
    async loadAll(id) {
        return measurePerformance('lazy_loadall', async () => {
            const lazy = this.containers.get(id);
            if (!lazy) {
                return;
            }

            const promises = lazy.elements
                .filter(element => !lazy.loaded.has(element))
                .map(element => this.loadElement(id, element));

            await Promise.all(promises);
        });
    }

    /**
     * Reset a container
     * @param {string} id - The container ID
     */
    resetContainer(id) {
        return measurePerformance('lazy_reset', () => {
            const lazy = this.containers.get(id);
            if (!lazy) {
                return;
            }

            // Reset elements
            lazy.elements.forEach(element => {
                element.classList.remove('lazy-loaded');
                element.classList.add('lazy-loading');

                if (element.tagName === 'IMG') {
                    element.src = '';
                } else if (element.tagName === 'VIDEO') {
                    element.src = '';
                } else if (element.tagName === 'IFRAME') {
                    element.src = '';
                } else {
                    element.innerHTML = '';
                }
            });

            // Reset state
            lazy.loaded.clear();

            if (lazy.onReset) {
                lazy.onReset({
                    container: lazy.container,
                });
            }

            this.notifySubscribers('reset', { id });
        });
    }

    /**
     * Get container data
     * @param {string} id - The container ID
     * @returns {Object} The container data
     */
    getContainerData(id) {
        return this.containers.get(id);
    }

    /**
     * Update container data
     * @param {string} id - The container ID
     * @param {Object} data - The new container data
     */
    updateContainerData(id, data) {
        const lazy = this.containers.get(id);
        if (lazy) {
            Object.assign(lazy, data);
        }
    }
}

// Create and export a singleton instance
const lazyLoadService = new LazyLoadService();
export default lazyLoadService; 