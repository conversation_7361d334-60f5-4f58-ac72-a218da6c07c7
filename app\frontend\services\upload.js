// File upload service
import config from '../config.js';
import { measurePerformance } from '../utils.js';
import apiService from './api.js';
import notificationService from './notification.js';

class UploadService {
    constructor() {
        this.maxFileSize = config.upload?.maxFileSize || 10 * 1024 * 1024; // 10MB
        this.allowedTypes = config.upload?.allowedTypes || ['image/jpeg', 'image/png', 'application/pdf'];
        this.maxConcurrentUploads = config.upload?.maxConcurrentUploads || 3;
        this.uploadQueue = [];
        this.activeUploads = 0;
    }

    /**
     * Upload a file
     * @param {File} file - The file to upload
     * @param {Object} [options] - Upload options
     * @param {string} [options.endpoint] - The upload endpoint
     * @param {Object} [options.metadata] - Additional metadata
     * @param {Function} [options.onProgress] - Progress callback
     * @returns {Promise<Object>} The upload result
     */
    async upload(file, options = {}) {
        return measurePerformance('file_upload', async () => {
            // Validate file
            if (!this.validateFile(file)) {
                throw new Error('Invalid file');
            }

            // Check concurrent uploads limit
            if (this.activeUploads >= this.maxConcurrentUploads) {
                return new Promise((resolve, reject) => {
                    this.uploadQueue.push({ file, options, resolve, reject });
                });
            }

            this.activeUploads++;

            try {
                const formData = new FormData();
                formData.append('file', file);

                if (options.metadata) {
                    formData.append('metadata', JSON.stringify(options.metadata));
                }

                const response = await apiService.request(options.endpoint || '/upload', {
                    method: 'POST',
                    body: formData,
                    onUploadProgress: (progressEvent) => {
                        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        if (options.onProgress) {
                            options.onProgress(progress);
                        }
                    },
                });

                notificationService.success('File uploaded successfully');
                return response;
            } catch (error) {
                notificationService.error('File upload failed');
                throw error;
            } finally {
                this.activeUploads--;
                this.processQueue();
            }
        });
    }

    /**
     * Process the upload queue
     */
    processQueue() {
        if (this.uploadQueue.length > 0 && this.activeUploads < this.maxConcurrentUploads) {
            const { file, options, resolve, reject } = this.uploadQueue.shift();
            this.upload(file, options).then(resolve).catch(reject);
        }
    }

    /**
     * Validate a file
     * @param {File} file - The file to validate
     * @returns {boolean} Whether the file is valid
     */
    validateFile(file) {
        if (!file) {
            notificationService.error('No file selected');
            return false;
        }

        if (file.size > this.maxFileSize) {
            notificationService.error(`File size exceeds ${this.maxFileSize / 1024 / 1024}MB limit`);
            return false;
        }

        if (!this.allowedTypes.includes(file.type)) {
            notificationService.error('File type not allowed');
            return false;
        }

        return true;
    }

    /**
     * Upload multiple files
     * @param {File[]} files - The files to upload
     * @param {Object} [options] - Upload options
     * @returns {Promise<Object[]>} The upload results
     */
    async uploadMultiple(files, options = {}) {
        return Promise.all(files.map(file => this.upload(file, options)));
    }

    /**
     * Cancel an upload
     * @param {string} uploadId - The upload ID
     */
    cancelUpload(uploadId) {
        // Implementation depends on the upload mechanism
        // This is a placeholder for the actual implementation
        notificationService.info('Upload cancelled');
    }

    /**
     * Get upload progress
     * @param {string} uploadId - The upload ID
     * @returns {number} The upload progress percentage
     */
    getUploadProgress(uploadId) {
        // Implementation depends on the upload mechanism
        // This is a placeholder for the actual implementation
        return 0;
    }

    /**
     * Get active uploads count
     * @returns {number} The number of active uploads
     */
    getActiveUploadsCount() {
        return this.activeUploads;
    }

    /**
     * Get queued uploads count
     * @returns {number} The number of queued uploads
     */
    getQueuedUploadsCount() {
        return this.uploadQueue.length;
    }

    /**
     * Clear the upload queue
     */
    clearQueue() {
        this.uploadQueue = [];
        notificationService.info('Upload queue cleared');
    }

    /**
     * Set the maximum file size
     * @param {number} size - The maximum file size in bytes
     */
    setMaxFileSize(size) {
        this.maxFileSize = size;
    }

    /**
     * Set the allowed file types
     * @param {string[]} types - The allowed file types
     */
    setAllowedTypes(types) {
        this.allowedTypes = types;
    }

    /**
     * Set the maximum concurrent uploads
     * @param {number} count - The maximum number of concurrent uploads
     */
    setMaxConcurrentUploads(count) {
        this.maxConcurrentUploads = count;
    }
}

// Create and export a singleton instance
const uploadService = new UploadService();
export default uploadService; 