#!/usr/bin/env python3
"""
ImpactCV Security Scanner
Comprehensive security scanning with Bandit, Semgrep, and Safety
"""

import argparse
import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import yaml


class SecurityScanner:
    """Comprehensive security scanner for ImpactCV."""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.results_dir = self.project_root / "security-reports"
        self.results_dir.mkdir(exist_ok=True)
        
        # Scan configuration
        self.scan_config = {
            "bandit": {
                "enabled": True,
                "config_file": ".bandit",
                "output_format": "json",
                "severity": "medium",
                "confidence": "medium"
            },
            "semgrep": {
                "enabled": True,
                "config_file": ".semgrep.yml",
                "rulesets": [
                    "p/security-audit",
                    "p/secrets",
                    "p/owasp-top-ten",
                    "p/python"
                ]
            },
            "safety": {
                "enabled": True,
                "output_format": "json",
                "ignore_ids": []  # Add CVE IDs to ignore if needed
            }
        }
    
    def run_bandit_scan(self) -> Tuple[bool, Dict]:
        """Run Bandit security scan."""
        print("🔍 Running Bandit security scan...")
        
        output_file = self.results_dir / "bandit-report.json"
        baseline_file = self.project_root / ".bandit_baseline.json"
        
        cmd = [
            "bandit",
            "-r", str(self.project_root / "app"),
            "-f", "json",
            "-o", str(output_file),
            "-ll",  # Low severity, low confidence
            "--exclude", "tests,test,migrations,alembic/versions"
        ]
        
        # Add baseline if it exists
        if baseline_file.exists():
            cmd.extend(["-b", str(baseline_file)])
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            # Bandit returns non-zero exit code when issues are found
            success = result.returncode in [0, 1]
            
            if output_file.exists():
                with open(output_file, 'r') as f:
                    report_data = json.load(f)
            else:
                report_data = {"results": [], "metrics": {}}
            
            # Add metadata
            report_data["scan_metadata"] = {
                "tool": "bandit",
                "timestamp": datetime.now().isoformat(),
                "exit_code": result.returncode,
                "command": " ".join(cmd)
            }
            
            if result.stderr:
                report_data["scan_metadata"]["stderr"] = result.stderr
            
            # Save updated report
            with open(output_file, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            print(f"✅ Bandit scan completed. Report: {output_file}")
            return success, report_data
            
        except FileNotFoundError:
            print("❌ Bandit not found. Install with: pip install bandit")
            return False, {}
        except Exception as e:
            print(f"❌ Bandit scan failed: {e}")
            return False, {}
    
    def run_semgrep_scan(self) -> Tuple[bool, Dict]:
        """Run Semgrep security scan."""
        print("🔍 Running Semgrep security scan...")
        
        output_file = self.results_dir / "semgrep-report.json"
        sarif_file = self.results_dir / "semgrep-results.sarif"
        
        cmd = [
            "semgrep",
            "--config=auto",  # Use default rulesets
            "--json",
            "--output", str(output_file),
            "--sarif-output", str(sarif_file),
            "--severity", "WARNING",
            "--exclude", "tests",
            "--exclude", "test",
            "--exclude", "migrations",
            str(self.project_root)
        ]
        
        # Add custom config if it exists
        config_file = self.project_root / ".semgrep.yml"
        if config_file.exists():
            cmd.extend(["--config", str(config_file)])
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            success = result.returncode == 0
            
            if output_file.exists():
                with open(output_file, 'r') as f:
                    report_data = json.load(f)
            else:
                report_data = {"results": [], "errors": []}
            
            # Add metadata
            report_data["scan_metadata"] = {
                "tool": "semgrep",
                "timestamp": datetime.now().isoformat(),
                "exit_code": result.returncode,
                "command": " ".join(cmd)
            }
            
            if result.stderr:
                report_data["scan_metadata"]["stderr"] = result.stderr
            
            # Save updated report
            with open(output_file, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            print(f"✅ Semgrep scan completed. Report: {output_file}")
            return success, report_data
            
        except FileNotFoundError:
            print("❌ Semgrep not found. Install with: pip install semgrep")
            return False, {}
        except Exception as e:
            print(f"❌ Semgrep scan failed: {e}")
            return False, {}
    
    def run_safety_scan(self) -> Tuple[bool, Dict]:
        """Run Safety dependency vulnerability scan."""
        print("🔍 Running Safety dependency scan...")
        
        output_file = self.results_dir / "safety-report.json"
        
        cmd = [
            "safety",
            "check",
            "--json",
            "--output", str(output_file)
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            # Safety returns non-zero when vulnerabilities are found
            success = result.returncode in [0, 64]  # 64 = vulnerabilities found
            
            if output_file.exists():
                with open(output_file, 'r') as f:
                    report_data = json.load(f)
            else:
                report_data = []
            
            # Wrap in standard format
            if isinstance(report_data, list):
                report_data = {"vulnerabilities": report_data}
            
            # Add metadata
            report_data["scan_metadata"] = {
                "tool": "safety",
                "timestamp": datetime.now().isoformat(),
                "exit_code": result.returncode,
                "command": " ".join(cmd)
            }
            
            if result.stderr:
                report_data["scan_metadata"]["stderr"] = result.stderr
            
            # Save updated report
            with open(output_file, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            print(f"✅ Safety scan completed. Report: {output_file}")
            return success, report_data
            
        except FileNotFoundError:
            print("❌ Safety not found. Install with: pip install safety")
            return False, {}
        except Exception as e:
            print(f"❌ Safety scan failed: {e}")
            return False, {}
    
    def generate_summary_report(self, bandit_data: Dict, semgrep_data: Dict, 
                              safety_data: Dict) -> Dict:
        """Generate comprehensive security summary report."""
        print("📊 Generating security summary report...")
        
        summary = {
            "scan_summary": {
                "timestamp": datetime.now().isoformat(),
                "project": "ImpactCV",
                "scan_type": "comprehensive_security_scan"
            },
            "tools": {
                "bandit": {
                    "enabled": True,
                    "issues_found": len(bandit_data.get("results", [])),
                    "severity_breakdown": self._get_bandit_severity_breakdown(bandit_data)
                },
                "semgrep": {
                    "enabled": True,
                    "issues_found": len(semgrep_data.get("results", [])),
                    "severity_breakdown": self._get_semgrep_severity_breakdown(semgrep_data)
                },
                "safety": {
                    "enabled": True,
                    "vulnerabilities_found": len(safety_data.get("vulnerabilities", [])),
                    "severity_breakdown": self._get_safety_severity_breakdown(safety_data)
                }
            },
            "overall_risk_assessment": self._calculate_risk_score(
                bandit_data, semgrep_data, safety_data
            ),
            "recommendations": self._generate_recommendations(
                bandit_data, semgrep_data, safety_data
            )
        }
        
        # Save summary report
        summary_file = self.results_dir / "security-summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"📊 Security summary report saved: {summary_file}")
        return summary
    
    def _get_bandit_severity_breakdown(self, data: Dict) -> Dict:
        """Get severity breakdown for Bandit results."""
        breakdown = {"HIGH": 0, "MEDIUM": 0, "LOW": 0}
        for result in data.get("results", []):
            severity = result.get("issue_severity", "UNKNOWN").upper()
            if severity in breakdown:
                breakdown[severity] += 1
        return breakdown
    
    def _get_semgrep_severity_breakdown(self, data: Dict) -> Dict:
        """Get severity breakdown for Semgrep results."""
        breakdown = {"ERROR": 0, "WARNING": 0, "INFO": 0}
        for result in data.get("results", []):
            severity = result.get("extra", {}).get("severity", "UNKNOWN").upper()
            if severity in breakdown:
                breakdown[severity] += 1
        return breakdown
    
    def _get_safety_severity_breakdown(self, data: Dict) -> Dict:
        """Get severity breakdown for Safety results."""
        breakdown = {"HIGH": 0, "MEDIUM": 0, "LOW": 0}
        for vuln in data.get("vulnerabilities", []):
            # Safety doesn't provide severity, so we estimate based on CVE
            breakdown["MEDIUM"] += 1  # Default to medium
        return breakdown
    
    def _calculate_risk_score(self, bandit_data: Dict, semgrep_data: Dict, 
                            safety_data: Dict) -> Dict:
        """Calculate overall risk score."""
        # Simple scoring algorithm
        bandit_score = (
            len([r for r in bandit_data.get("results", []) 
                if r.get("issue_severity") == "HIGH"]) * 10 +
            len([r for r in bandit_data.get("results", []) 
                if r.get("issue_severity") == "MEDIUM"]) * 5 +
            len([r for r in bandit_data.get("results", []) 
                if r.get("issue_severity") == "LOW"]) * 1
        )
        
        semgrep_score = (
            len([r for r in semgrep_data.get("results", []) 
                if r.get("extra", {}).get("severity") == "ERROR"]) * 10 +
            len([r for r in semgrep_data.get("results", []) 
                if r.get("extra", {}).get("severity") == "WARNING"]) * 5 +
            len([r for r in semgrep_data.get("results", []) 
                if r.get("extra", {}).get("severity") == "INFO"]) * 1
        )
        
        safety_score = len(safety_data.get("vulnerabilities", [])) * 7
        
        total_score = bandit_score + semgrep_score + safety_score
        
        if total_score == 0:
            risk_level = "LOW"
        elif total_score <= 20:
            risk_level = "MEDIUM"
        elif total_score <= 50:
            risk_level = "HIGH"
        else:
            risk_level = "CRITICAL"
        
        return {
            "total_score": total_score,
            "risk_level": risk_level,
            "component_scores": {
                "bandit": bandit_score,
                "semgrep": semgrep_score,
                "safety": safety_score
            }
        }
    
    def _generate_recommendations(self, bandit_data: Dict, semgrep_data: Dict, 
                                safety_data: Dict) -> List[str]:
        """Generate security recommendations."""
        recommendations = []
        
        # Bandit recommendations
        high_severity_bandit = len([r for r in bandit_data.get("results", []) 
                                  if r.get("issue_severity") == "HIGH"])
        if high_severity_bandit > 0:
            recommendations.append(
                f"Address {high_severity_bandit} high-severity security issues found by Bandit"
            )
        
        # Semgrep recommendations
        error_level_semgrep = len([r for r in semgrep_data.get("results", []) 
                                 if r.get("extra", {}).get("severity") == "ERROR"])
        if error_level_semgrep > 0:
            recommendations.append(
                f"Fix {error_level_semgrep} error-level security issues found by Semgrep"
            )
        
        # Safety recommendations
        vulnerabilities = len(safety_data.get("vulnerabilities", []))
        if vulnerabilities > 0:
            recommendations.append(
                f"Update {vulnerabilities} vulnerable dependencies identified by Safety"
            )
        
        if not recommendations:
            recommendations.append("No critical security issues found. Continue monitoring.")
        
        return recommendations
    
    def run_comprehensive_scan(self) -> bool:
        """Run all security scans and generate comprehensive report."""
        print("🚀 Starting comprehensive security scan...")
        print(f"📁 Project root: {self.project_root}")
        print(f"📊 Results directory: {self.results_dir}")
        
        all_success = True
        
        # Run individual scans
        bandit_success, bandit_data = self.run_bandit_scan()
        semgrep_success, semgrep_data = self.run_semgrep_scan()
        safety_success, safety_data = self.run_safety_scan()
        
        all_success = bandit_success and semgrep_success and safety_success
        
        # Generate summary report
        summary = self.generate_summary_report(bandit_data, semgrep_data, safety_data)
        
        # Print summary
        self._print_summary(summary)
        
        return all_success
    
    def _print_summary(self, summary: Dict):
        """Print security scan summary to console."""
        print("\n" + "="*60)
        print("🔒 SECURITY SCAN SUMMARY")
        print("="*60)
        
        tools = summary["tools"]
        risk = summary["overall_risk_assessment"]
        
        print(f"📊 Bandit Issues: {tools['bandit']['issues_found']}")
        print(f"📊 Semgrep Issues: {tools['semgrep']['issues_found']}")
        print(f"📊 Safety Vulnerabilities: {tools['safety']['vulnerabilities_found']}")
        print(f"📊 Overall Risk Level: {risk['risk_level']}")
        print(f"📊 Risk Score: {risk['total_score']}")
        
        print("\n🎯 RECOMMENDATIONS:")
        for i, rec in enumerate(summary["recommendations"], 1):
            print(f"  {i}. {rec}")
        
        print("\n📁 Reports saved in:", self.results_dir)
        print("="*60)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="ImpactCV Security Scanner")
    parser.add_argument(
        "--project-root",
        default=".",
        help="Project root directory (default: current directory)"
    )
    parser.add_argument(
        "--tool",
        choices=["bandit", "semgrep", "safety", "all"],
        default="all",
        help="Security tool to run (default: all)"
    )
    parser.add_argument(
        "--fail-on-issues",
        action="store_true",
        help="Exit with non-zero code if security issues are found"
    )
    
    args = parser.parse_args()
    
    scanner = SecurityScanner(args.project_root)
    
    if args.tool == "all":
        success = scanner.run_comprehensive_scan()
    elif args.tool == "bandit":
        success, _ = scanner.run_bandit_scan()
    elif args.tool == "semgrep":
        success, _ = scanner.run_semgrep_scan()
    elif args.tool == "safety":
        success, _ = scanner.run_safety_scan()
    
    if args.fail_on_issues and not success:
        sys.exit(1)
    
    sys.exit(0)


if __name__ == "__main__":
    main()
