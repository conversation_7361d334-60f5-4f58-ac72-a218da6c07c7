#!/usr/bin/env python3
"""
Script para validar la Fase 2: Comprehensive Testing Suite
Ejecuta todos los tests y genera reportes de cobertura
"""

import asyncio
import subprocess
import sys
import json
import time
from pathlib import Path
from datetime import datetime
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase2Validator:
    """Validador de la Fase 2: Comprehensive Testing Suite"""
    
    def __init__(self):
        self.results = {
            "phase_2_validation": {
                "start_time": datetime.now().isoformat(),
                "test_categories": {},
                "coverage_report": {},
                "overall_success": False,
                "summary": {}
            }
        }
    
    def run_command(self, command: str, description: str) -> dict:
        """Ejecutar comando y capturar resultado"""
        logger.info(f"🔄 {description}")
        logger.info(f"📝 Command: {command}")
        
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutos timeout
            )
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "description": description
            }
            
        except subprocess.TimeoutExpired:
            logger.error(f"❌ Timeout executing: {description}")
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": "Command timed out",
                "description": description
            }
        except Exception as e:
            logger.error(f"❌ Error executing: {description} - {e}")
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "description": description
            }
    
    def validate_unit_tests(self) -> dict:
        """Validar tests unitarios"""
        logger.info("🧪 Validating Unit Tests")
        
        # Ejecutar tests unitarios con coverage
        result = self.run_command(
            "python -m pytest tests/unit/ -v --cov=app --cov-report=json --cov-report=term-missing",
            "Unit Tests Execution"
        )
        
        # Analizar resultados
        unit_results = {
            "executed": result["success"],
            "details": result,
            "test_count": 0,
            "passed": 0,
            "failed": 0,
            "coverage": 0
        }
        
        if result["success"]:
            # Extraer estadísticas de pytest
            stdout = result["stdout"]
            if "passed" in stdout:
                try:
                    # Buscar línea con resultados
                    for line in stdout.split('\n'):
                        if "passed" in line and ("failed" in line or "error" in line or line.strip().endswith("passed")):
                            # Parsear resultados básicos
                            if "passed" in line:
                                unit_results["passed"] = stdout.count(" PASSED")
                                unit_results["failed"] = stdout.count(" FAILED") + stdout.count(" ERROR")
                                unit_results["test_count"] = unit_results["passed"] + unit_results["failed"]
                            break
                except:
                    pass
            
            # Leer coverage si existe
            try:
                if Path("coverage.json").exists():
                    with open("coverage.json", "r") as f:
                        coverage_data = json.load(f)
                        unit_results["coverage"] = coverage_data.get("totals", {}).get("percent_covered", 0)
            except:
                pass
        
        logger.info(f"📊 Unit Tests: {unit_results['passed']} passed, {unit_results['failed']} failed")
        return unit_results
    
    def validate_integration_tests(self) -> dict:
        """Validar tests de integración"""
        logger.info("🔗 Validating Integration Tests")
        
        result = self.run_command(
            "python -m pytest tests/integration/ -v -m integration",
            "Integration Tests Execution"
        )
        
        integration_results = {
            "executed": result["success"],
            "details": result,
            "test_count": 0,
            "passed": 0,
            "failed": 0
        }
        
        if result["success"]:
            stdout = result["stdout"]
            integration_results["passed"] = stdout.count(" PASSED")
            integration_results["failed"] = stdout.count(" FAILED") + stdout.count(" ERROR")
            integration_results["test_count"] = integration_results["passed"] + integration_results["failed"]
        
        logger.info(f"📊 Integration Tests: {integration_results['passed']} passed, {integration_results['failed']} failed")
        return integration_results
    
    def validate_performance_tests(self) -> dict:
        """Validar tests de performance"""
        logger.info("⚡ Validating Performance Tests")
        
        result = self.run_command(
            "python -m pytest tests/performance/ -v -m performance --tb=short",
            "Performance Tests Execution"
        )
        
        performance_results = {
            "executed": result["success"],
            "details": result,
            "test_count": 0,
            "passed": 0,
            "failed": 0
        }
        
        if result["success"]:
            stdout = result["stdout"]
            performance_results["passed"] = stdout.count(" PASSED")
            performance_results["failed"] = stdout.count(" FAILED") + stdout.count(" ERROR")
            performance_results["test_count"] = performance_results["passed"] + performance_results["failed"]
        
        logger.info(f"📊 Performance Tests: {performance_results['passed']} passed, {performance_results['failed']} failed")
        return performance_results
    
    def validate_security_tests(self) -> dict:
        """Validar tests de seguridad"""
        logger.info("🛡️ Validating Security Tests")
        
        result = self.run_command(
            "python -m pytest tests/security/ -v -m security --tb=short",
            "Security Tests Execution"
        )
        
        security_results = {
            "executed": result["success"],
            "details": result,
            "test_count": 0,
            "passed": 0,
            "failed": 0
        }
        
        if result["success"]:
            stdout = result["stdout"]
            security_results["passed"] = stdout.count(" PASSED")
            security_results["failed"] = stdout.count(" FAILED") + stdout.count(" ERROR")
            security_results["test_count"] = security_results["passed"] + security_results["failed"]
        
        logger.info(f"📊 Security Tests: {security_results['passed']} passed, {security_results['failed']} failed")
        return security_results
    
    def generate_coverage_report(self) -> dict:
        """Generar reporte de cobertura"""
        logger.info("📈 Generating Coverage Report")
        
        # Generar reporte HTML
        html_result = self.run_command(
            "python -m pytest tests/ --cov=app --cov-report=html:htmlcov --cov-report=json --cov-fail-under=70",
            "Coverage Report Generation"
        )
        
        coverage_report = {
            "html_generated": html_result["success"],
            "coverage_percentage": 0,
            "details": html_result
        }
        
        # Leer datos de cobertura
        try:
            if Path("coverage.json").exists():
                with open("coverage.json", "r") as f:
                    coverage_data = json.load(f)
                    coverage_report["coverage_percentage"] = coverage_data.get("totals", {}).get("percent_covered", 0)
                    coverage_report["lines_covered"] = coverage_data.get("totals", {}).get("covered_lines", 0)
                    coverage_report["lines_total"] = coverage_data.get("totals", {}).get("num_statements", 0)
        except Exception as e:
            logger.warning(f"⚠️ Could not read coverage data: {e}")
        
        logger.info(f"📊 Coverage: {coverage_report['coverage_percentage']:.1f}%")
        return coverage_report
    
    async def validate_phase_2(self) -> bool:
        """Ejecutar validación completa de la Fase 2"""
        logger.info("🎯 Starting Phase 2: Comprehensive Testing Suite Validation")
        
        # 1. Unit Tests
        self.results["phase_2_validation"]["test_categories"]["unit"] = self.validate_unit_tests()
        
        # 2. Integration Tests
        self.results["phase_2_validation"]["test_categories"]["integration"] = self.validate_integration_tests()
        
        # 3. Performance Tests
        self.results["phase_2_validation"]["test_categories"]["performance"] = self.validate_performance_tests()
        
        # 4. Security Tests
        self.results["phase_2_validation"]["test_categories"]["security"] = self.validate_security_tests()
        
        # 5. Coverage Report
        self.results["phase_2_validation"]["coverage_report"] = self.generate_coverage_report()
        
        # Calcular resumen
        total_tests = 0
        total_passed = 0
        total_failed = 0
        categories_executed = 0
        
        for category, results in self.results["phase_2_validation"]["test_categories"].items():
            if results["executed"]:
                categories_executed += 1
                total_tests += results["test_count"]
                total_passed += results["passed"]
                total_failed += results["failed"]
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        coverage_percentage = self.results["phase_2_validation"]["coverage_report"]["coverage_percentage"]
        
        # Criterios de éxito para Fase 2
        phase_2_success = (
            categories_executed >= 3 and  # Al menos 3 categorías ejecutadas
            success_rate >= 70 and        # 70% de tests exitosos
            coverage_percentage >= 60     # 60% de cobertura mínima
        )
        
        self.results["phase_2_validation"]["summary"] = {
            "categories_executed": categories_executed,
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "success_rate": success_rate,
            "coverage_percentage": coverage_percentage,
            "phase_2_success": phase_2_success
        }
        
        self.results["phase_2_validation"]["overall_success"] = phase_2_success
        self.results["phase_2_validation"]["end_time"] = datetime.now().isoformat()
        
        # Generar reporte
        await self.generate_phase_2_report()
        
        return phase_2_success
    
    async def generate_phase_2_report(self):
        """Generar reporte de la Fase 2"""
        logger.info("📊 Generating Phase 2 Report")
        
        # Guardar resultados JSON
        with open("phase_2_validation_report.json", "w", encoding="utf-8") as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        # Log resumen
        summary = self.results["phase_2_validation"]["summary"]
        logger.info("📋 PHASE 2 VALIDATION REPORT:")
        logger.info(f"   Categories Executed: {summary['categories_executed']}/4")
        logger.info(f"   Total Tests: {summary['total_tests']}")
        logger.info(f"   Tests Passed: {summary['total_passed']}")
        logger.info(f"   Tests Failed: {summary['total_failed']}")
        logger.info(f"   Success Rate: {summary['success_rate']:.1f}%")
        logger.info(f"   Code Coverage: {summary['coverage_percentage']:.1f}%")
        logger.info(f"   Phase 2 Status: {'✅ SUCCESS' if summary['phase_2_success'] else '❌ NEEDS ATTENTION'}")

async def main():
    """Función principal"""
    validator = Phase2Validator()
    success = await validator.validate_phase_2()
    
    if success:
        logger.info("🎉 PHASE 2 VALIDATION SUCCESSFUL")
        return 0
    else:
        logger.warning("⚠️ PHASE 2 VALIDATION COMPLETED WITH ISSUES")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
