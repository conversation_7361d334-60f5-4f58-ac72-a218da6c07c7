// Virtual scroll service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class VirtualScrollService {
    constructor() {
        this.containers = new Map();
        this.subscribers = new Set();
        this.observers = new Map();
        this.initialize();
    }

    /**
     * Initialize the virtual scroll service
     */
    initialize() {
        // Create intersection observer
        this.observer = new IntersectionObserver(
            this.handleIntersection.bind(this),
            {
                root: null,
                rootMargin: '100px',
                threshold: 0,
            }
        );
    }

    /**
     * Subscribe to virtual scroll events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of virtual scroll events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in virtual scroll subscriber:', error);
            }
        });
    }

    /**
     * Register a virtual scroll container
     * @param {string} id - The container ID
     * @param {Object} options - The virtual scroll options
     */
    registerContainer(id, options = {}) {
        const container = document.getElementById(id);
        if (!container) {
            console.error(`Container ${id} not found`);
            return;
        }

        // Create viewport
        const viewport = document.createElement('div');
        viewport.className = 'virtual-scroll-viewport';
        viewport.style.position = 'relative';
        viewport.style.overflow = 'auto';
        viewport.style.height = '100%';
        container.appendChild(viewport);

        // Create content
        const content = document.createElement('div');
        content.className = 'virtual-scroll-content';
        content.style.position = 'relative';
        viewport.appendChild(content);

        // Create sentinels
        const topSentinel = document.createElement('div');
        topSentinel.className = 'virtual-scroll-sentinel virtual-scroll-sentinel-top';
        content.appendChild(topSentinel);

        const bottomSentinel = document.createElement('div');
        bottomSentinel.className = 'virtual-scroll-sentinel virtual-scroll-sentinel-bottom';
        content.appendChild(bottomSentinel);

        // Observe sentinels
        this.observer.observe(topSentinel);
        this.observer.observe(bottomSentinel);

        this.containers.set(id, {
            ...options,
            container,
            viewport,
            content,
            topSentinel,
            bottomSentinel,
            items: [],
            visibleItems: new Set(),
            startIndex: 0,
            endIndex: 0,
            itemHeight: options.itemHeight || 50,
            buffer: options.buffer || 5,
        });

        // Initial layout
        this.layoutContainer(id);
    }

    /**
     * Handle intersection events
     * @param {IntersectionObserverEntry[]} entries - The intersection entries
     */
    handleIntersection(entries) {
        return measurePerformance('virtual_intersection', () => {
            entries.forEach(entry => {
                if (!entry.isIntersecting) {
                    return;
                }

                const sentinel = entry.target;
                const content = sentinel.parentElement;
                const viewport = content.parentElement;
                const container = viewport.parentElement;
                const id = container.id;
                const virtual = this.containers.get(id);
                if (!virtual) {
                    return;
                }

                if (sentinel === virtual.topSentinel) {
                    this.loadPrevious(id);
                } else if (sentinel === virtual.bottomSentinel) {
                    this.loadNext(id);
                }
            });
        });
    }

    /**
     * Layout a container
     * @param {string} id - The container ID
     */
    layoutContainer(id) {
        return measurePerformance('virtual_layout', () => {
            const virtual = this.containers.get(id);
            if (!virtual) {
                return;
            }

            const { viewport, content, items, itemHeight, buffer } = virtual;
            const viewportHeight = viewport.clientHeight;
            const visibleCount = Math.ceil(viewportHeight / itemHeight) + buffer * 2;

            // Calculate indices
            const scrollTop = viewport.scrollTop;
            const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer);
            const endIndex = Math.min(items.length, startIndex + visibleCount);

            // Update content height
            content.style.height = `${items.length * itemHeight}px`;

            // Update visible items
            this.updateVisibleItems(id, startIndex, endIndex);

            if (virtual.onLayout) {
                virtual.onLayout({
                    container: virtual.container,
                    viewport,
                    content,
                    startIndex,
                    endIndex,
                });
            }

            this.notifySubscribers('layout', { id, startIndex, endIndex });
        });
    }

    /**
     * Update visible items
     * @param {string} id - The container ID
     * @param {number} startIndex - The start index
     * @param {number} endIndex - The end index
     */
    updateVisibleItems(id, startIndex, endIndex) {
        return measurePerformance('virtual_update', () => {
            const virtual = this.containers.get(id);
            if (!virtual) {
                return;
            }

            const { content, items, itemHeight, visibleItems } = virtual;

            // Remove old items
            visibleItems.forEach(item => {
                if (item.index < startIndex || item.index >= endIndex) {
                    content.removeChild(item.element);
                    visibleItems.delete(item);
                }
            });

            // Add new items
            for (let i = startIndex; i < endIndex; i++) {
                if (Array.from(visibleItems).some(item => item.index === i)) {
                    continue;
                }

                const item = items[i];
                if (!item) {
                    continue;
                }

                const element = document.createElement('div');
                element.className = 'virtual-scroll-item';
                element.style.position = 'absolute';
                element.style.top = `${i * itemHeight}px`;
                element.style.width = '100%';
                element.style.height = `${itemHeight}px`;

                if (virtual.renderItem) {
                    virtual.renderItem(element, item, i);
                }

                content.appendChild(element);
                visibleItems.add({ element, index: i });
            }

            virtual.startIndex = startIndex;
            virtual.endIndex = endIndex;
        });
    }

    /**
     * Load previous items
     * @param {string} id - The container ID
     */
    async loadPrevious(id) {
        return measurePerformance('virtual_previous', async () => {
            const virtual = this.containers.get(id);
            if (!virtual || virtual.loading) {
                return;
            }

            try {
                virtual.loading = true;

                const items = await virtual.loadPreviousItems(virtual.startIndex);
                if (!items || items.length === 0) {
                    return;
                }

                // Add items
                virtual.items.unshift(...items);

                // Update layout
                this.layoutContainer(id);

                if (virtual.onLoadPrevious) {
                    virtual.onLoadPrevious({
                        container: virtual.container,
                        items,
                    });
                }

                this.notifySubscribers('loadprevious', { id, items });
            } catch (error) {
                console.error('Error loading previous items:', error);

                if (virtual.onError) {
                    virtual.onError(error);
                }

                this.notifySubscribers('error', { id, error });
            } finally {
                virtual.loading = false;
            }
        });
    }

    /**
     * Load next items
     * @param {string} id - The container ID
     */
    async loadNext(id) {
        return measurePerformance('virtual_next', async () => {
            const virtual = this.containers.get(id);
            if (!virtual || virtual.loading) {
                return;
            }

            try {
                virtual.loading = true;

                const items = await virtual.loadNextItems(virtual.endIndex);
                if (!items || items.length === 0) {
                    return;
                }

                // Add items
                virtual.items.push(...items);

                // Update layout
                this.layoutContainer(id);

                if (virtual.onLoadNext) {
                    virtual.onLoadNext({
                        container: virtual.container,
                        items,
                    });
                }

                this.notifySubscribers('loadnext', { id, items });
            } catch (error) {
                console.error('Error loading next items:', error);

                if (virtual.onError) {
                    virtual.onError(error);
                }

                this.notifySubscribers('error', { id, error });
            } finally {
                virtual.loading = false;
            }
        });
    }

    /**
     * Reset a container
     * @param {string} id - The container ID
     */
    resetContainer(id) {
        return measurePerformance('virtual_reset', () => {
            const virtual = this.containers.get(id);
            if (!virtual) {
                return;
            }

            // Clear items
            virtual.items = [];
            virtual.visibleItems.clear();
            virtual.startIndex = 0;
            virtual.endIndex = 0;

            // Clear content
            while (virtual.content.firstChild) {
                virtual.content.removeChild(virtual.content.firstChild);
            }

            // Add sentinels
            virtual.content.appendChild(virtual.topSentinel);
            virtual.content.appendChild(virtual.bottomSentinel);

            if (virtual.onReset) {
                virtual.onReset({
                    container: virtual.container,
                });
            }

            this.notifySubscribers('reset', { id });
        });
    }

    /**
     * Get container data
     * @param {string} id - The container ID
     * @returns {Object} The container data
     */
    getContainerData(id) {
        return this.containers.get(id);
    }

    /**
     * Update container data
     * @param {string} id - The container ID
     * @param {Object} data - The new container data
     */
    updateContainerData(id, data) {
        const virtual = this.containers.get(id);
        if (virtual) {
            Object.assign(virtual, data);
            this.layoutContainer(id);
        }
    }
}

// Create and export a singleton instance
const virtualScrollService = new VirtualScrollService();
export default virtualScrollService; 