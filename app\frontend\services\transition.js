// Transition service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class TransitionService {
    constructor() {
        this.transitions = new Map();
        this.subscribers = new Set();
        this.animationFrame = null;
        this.isTransitioning = false;
    }

    /**
     * Subscribe to transition events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of transition events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in transition subscriber:', error);
            }
        });
    }

    /**
     * Register a transition
     * @param {string} id - The transition ID
     * @param {Object} options - The transition options
     */
    register(id, options = {}) {
        this.transitions.set(id, {
            ...options,
            startTime: null,
            currentTime: null,
            progress: 0,
            isTransitioning: false,
            from: null,
            to: null,
        });
    }

    /**
     * Start a transition
     * @param {string} id - The transition ID
     * @param {Object} from - The starting state
     * @param {Object} to - The ending state
     * @param {Object} [options] - The transition options
     */
    start(id, from, to, options = {}) {
        return measurePerformance('transition_start', () => {
            const transition = this.transitions.get(id);
            if (!transition) {
                console.error(`Transition ${id} not found`);
                return;
            }

            if (transition.isTransitioning) {
                return;
            }

            transition.startTime = performance.now();
            transition.currentTime = transition.startTime;
            transition.progress = 0;
            transition.isTransitioning = true;
            transition.from = from;
            transition.to = to;

            if (!this.isTransitioning) {
                this.isTransitioning = true;
                this.transition();
            }

            if (transition.onStart) {
                transition.onStart(from, to, options);
            }

            this.notifySubscribers('start', { id, from, to, options });
        });
    }

    /**
     * Stop a transition
     * @param {string} id - The transition ID
     */
    stop(id) {
        return measurePerformance('transition_stop', () => {
            const transition = this.transitions.get(id);
            if (!transition) {
                console.error(`Transition ${id} not found`);
                return;
            }

            if (!transition.isTransitioning) {
                return;
            }

            transition.isTransitioning = false;
            transition.progress = 1;

            if (transition.onStop) {
                transition.onStop();
            }

            this.notifySubscribers('stop', { id });

            // Check if any transitions are still active
            this.isTransitioning = Array.from(this.transitions.values()).some(t => t.isTransitioning);
            if (!this.isTransitioning) {
                cancelAnimationFrame(this.animationFrame);
            }
        });
    }

    /**
     * Update transitions
     */
    transition() {
        if (!this.isTransitioning) {
            return;
        }

        const currentTime = performance.now();
        let hasActiveTransitions = false;

        this.transitions.forEach((transition, id) => {
            if (!transition.isTransitioning) {
                return;
            }

            transition.currentTime = currentTime;
            const elapsed = currentTime - transition.startTime;
            transition.progress = Math.min(elapsed / transition.duration, 1);

            if (transition.onUpdate) {
                const currentState = this.interpolate(transition.from, transition.to, transition.progress);
                transition.onUpdate(currentState, transition.progress);
            }

            if (transition.progress < 1) {
                hasActiveTransitions = true;
            } else {
                transition.isTransitioning = false;
                if (transition.onComplete) {
                    transition.onComplete(transition.to);
                }
                this.notifySubscribers('complete', { id, state: transition.to });
            }
        });

        this.isTransitioning = hasActiveTransitions;
        if (this.isTransitioning) {
            this.animationFrame = requestAnimationFrame(this.transition.bind(this));
        }
    }

    /**
     * Interpolate between two states
     * @param {Object} from - The starting state
     * @param {Object} to - The ending state
     * @param {number} progress - The transition progress (0-1)
     * @returns {Object} The interpolated state
     */
    interpolate(from, to, progress) {
        const result = {};

        for (const key in to) {
            if (typeof to[key] === 'number') {
                result[key] = from[key] + (to[key] - from[key]) * progress;
            } else {
                result[key] = progress < 0.5 ? from[key] : to[key];
            }
        }

        return result;
    }

    /**
     * Get transition data
     * @param {string} id - The transition ID
     * @returns {Object} The transition data
     */
    getTransitionData(id) {
        return this.transitions.get(id);
    }

    /**
     * Update transition data
     * @param {string} id - The transition ID
     * @param {Object} data - The new transition data
     */
    updateTransitionData(id, data) {
        const transition = this.transitions.get(id);
        if (transition) {
            Object.assign(transition, data);
        }
    }

    /**
     * Check if a transition is active
     * @param {string} id - The transition ID
     * @returns {boolean} Whether the transition is active
     */
    isActive(id) {
        const transition = this.transitions.get(id);
        return transition ? transition.isTransitioning : false;
    }

    /**
     * Get transition progress
     * @param {string} id - The transition ID
     * @returns {number} The transition progress (0-1)
     */
    getProgress(id) {
        const transition = this.transitions.get(id);
        return transition ? transition.progress : 0;
    }

    /**
     * Get current state
     * @param {string} id - The transition ID
     * @returns {Object|null} The current state
     */
    getCurrentState(id) {
        const transition = this.transitions.get(id);
        if (!transition || !transition.isTransitioning) {
            return null;
        }

        return this.interpolate(transition.from, transition.to, transition.progress);
    }
}

// Create and export a singleton instance
const transitionService = new TransitionService();
export default transitionService; 