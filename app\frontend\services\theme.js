// Theme service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ThemeService {
    constructor() {
        this.themes = new Map();
        this.currentTheme = config.theme?.default || 'light';
        this.subscribers = new Set();
        this.prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

        // Listen for system theme changes
        this.mediaQuery.addEventListener('change', (e) => {
            if (this.currentTheme === 'system') {
                this.applyTheme(e.matches ? 'dark' : 'light');
            }
        });
    }

    /**
     * Subscribe to theme changes
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of theme changes
     */
    notifySubscribers() {
        this.subscribers.forEach(callback => {
            try {
                callback(this.currentTheme);
            } catch (error) {
                console.error('Error in theme subscriber:', error);
            }
        });
    }

    /**
     * Register a theme
     * @param {string} name - The theme name
     * @param {Object} theme - The theme object
     */
    registerTheme(name, theme) {
        this.themes.set(name, theme);
    }

    /**
     * Set the current theme
     * @param {string} theme - The theme to set
     */
    setTheme(theme) {
        if (theme === 'system') {
            this.currentTheme = 'system';
            this.applyTheme(this.prefersDark ? 'dark' : 'light');
        } else {
            this.currentTheme = theme;
            this.applyTheme(theme);
        }

        // Save theme preference
        localStorage.setItem('theme', theme);
        this.notifySubscribers();
    }

    /**
     * Apply a theme
     * @param {string} theme - The theme to apply
     */
    applyTheme(theme) {
        return measurePerformance('apply_theme', () => {
            const themeData = this.themes.get(theme);
            if (!themeData) {
                console.warn(`Theme ${theme} not found`);
                return;
            }

            // Apply CSS variables
            Object.entries(themeData).forEach(([key, value]) => {
                document.documentElement.style.setProperty(`--${key}`, value);
            });

            // Update document theme
            document.documentElement.setAttribute('data-theme', theme);
        });
    }

    /**
     * Get the current theme
     * @returns {string} The current theme
     */
    getTheme() {
        return this.currentTheme;
    }

    /**
     * Get the effective theme (resolves 'system' to actual theme)
     * @returns {string} The effective theme
     */
    getEffectiveTheme() {
        if (this.currentTheme === 'system') {
            return this.prefersDark ? 'dark' : 'light';
        }
        return this.currentTheme;
    }

    /**
     * Get theme data
     * @param {string} [theme] - The theme to get data for
     * @returns {Object} The theme data
     */
    getThemeData(theme = this.getEffectiveTheme()) {
        return this.themes.get(theme) || {};
    }

    /**
     * Initialize the theme service
     */
    initialize() {
        // Load saved theme preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            this.setTheme(savedTheme);
        } else {
            // Use system preference
            this.setTheme('system');
        }

        // Register default themes
        this.registerTheme('light', {
            'background-primary': '#ffffff',
            'background-secondary': '#f5f5f5',
            'text-primary': '#333333',
            'text-secondary': '#666666',
            'accent-primary': '#007bff',
            'accent-secondary': '#6c757d',
            'border-color': '#dee2e6',
            'shadow-color': 'rgba(0, 0, 0, 0.1)',
        });

        this.registerTheme('dark', {
            'background-primary': '#1a1a1a',
            'background-secondary': '#2d2d2d',
            'text-primary': '#ffffff',
            'text-secondary': '#b3b3b3',
            'accent-primary': '#0d6efd',
            'accent-secondary': '#6c757d',
            'border-color': '#404040',
            'shadow-color': 'rgba(0, 0, 0, 0.3)',
        });
    }

    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        const current = this.getEffectiveTheme();
        this.setTheme(current === 'light' ? 'dark' : 'light');
    }

    /**
     * Check if a theme is available
     * @param {string} theme - The theme to check
     * @returns {boolean} Whether the theme is available
     */
    isThemeAvailable(theme) {
        return this.themes.has(theme);
    }

    /**
     * Get available themes
     * @returns {string[]} The available themes
     */
    getAvailableThemes() {
        return Array.from(this.themes.keys());
    }

    /**
     * Update theme data
     * @param {string} theme - The theme to update
     * @param {Object} data - The new theme data
     */
    updateThemeData(theme, data) {
        if (!this.themes.has(theme)) {
            console.warn(`Theme ${theme} not found`);
            return;
        }

        const currentData = this.themes.get(theme);
        this.themes.set(theme, { ...currentData, ...data });

        // Reapply theme if it's the current theme
        if (this.getEffectiveTheme() === theme) {
            this.applyTheme(theme);
        }
    }
}

// Create and export a singleton instance
const themeService = new ThemeService();
export default themeService; 