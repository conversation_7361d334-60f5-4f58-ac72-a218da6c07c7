<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImpactCV - AI-Powered CV Achievement Generator</title>
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center">
                <div class="text-2xl font-bold text-primary">ImpactCV</div>
                <button class="btn-primary">Get Started</button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container mx-auto px-4 py-16">
            <h1 class="text-4xl md:text-5xl font-bold text-center mb-8">
                Transform Your Career Experience
            </h1>
            <p class="text-xl text-center text-light-text mb-12">
                Generate impactful CV achievements using AI
            </p>
        </div>
    </section>

    <!-- CV Generator Form -->
    <section class="form-section">
        <div class="container mx-auto px-4 py-8">
            <form id="cvForm" class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
                <div class="mb-6">
                    <label for="name" class="block text-sm font-medium mb-2">Name</label>
                    <input type="text" id="name" name="name" required
                        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary">
                </div>
                
                <div class="mb-6">
                    <label for="currentPosition" class="block text-sm font-medium mb-2">Current Position</label>
                    <input type="text" id="currentPosition" name="currentPosition" required
                        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary">
                </div>
                
                <div class="mb-6">
                    <label for="yearsOfExperience" class="block text-sm font-medium mb-2">Years of Experience</label>
                    <input type="number" id="yearsOfExperience" name="yearsOfExperience" required min="0"
                        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary">
                </div>
                
                <div class="mb-6">
                    <label for="achievementDescription" class="block text-sm font-medium mb-2">Achievement Description</label>
                    <textarea id="achievementDescription" name="achievementDescription" required rows="4"
                        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary"></textarea>
                </div>
                
                <div class="flex justify-center">
                    <button type="submit" id="submitButton" class="btn-primary">
                        Generate Achievement
                    </button>
                    <button type="button" id="loadingButton" class="btn-primary hidden" disabled>
                        Generating...
                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- Results Section -->
    <section id="resultSection" class="results-section hidden">
        <div class="container mx-auto px-4 py-8">
            <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6">Generated Achievement</h2>
                
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">Task</h3>
                    <p id="task" class="text-light-text"></p>
                </div>
                
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">Quantification</h3>
                    <p id="quantification" class="text-light-text"></p>
                </div>
                
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-2">Result</h3>
                    <p id="result" class="text-light-text"></p>
                </div>
            </div>
        </div>
    </section>

    <script src="app.js"></script>
</body>
</html> 