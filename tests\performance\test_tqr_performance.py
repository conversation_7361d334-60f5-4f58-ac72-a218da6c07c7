"""
Performance tests for TQR generation system
Tests system performance under various load conditions
"""

import pytest
import time
import asyncio
import statistics
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import Mock, patch
import httpx

from app.main import app


class TestTQRPerformance:
    """Performance tests for TQR generation system"""
    
    @pytest.fixture
    async def async_client(self):
        """Create async test client"""
        async with httpx.AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    def sample_request(self):
        """Sample TQR request for performance testing"""
        return {
            "name": "Performance Test User",
            "position": "Senior Developer",
            "experience_years": 5,
            "achievement_description": "Optimicé el sistema de base de datos para mejorar el rendimiento"
        }
    
    @pytest.fixture
    def performance_thresholds(self):
        """Performance thresholds for testing"""
        return {
            "max_response_time": 60000,  # 60 seconds max
            "avg_response_time": 30000,  # 30 seconds average
            "min_success_rate": 80,      # 80% minimum success rate
            "max_concurrent_users": 10   # 10 concurrent users
        }

    @pytest.mark.asyncio
    @pytest.mark.performance
    @pytest.mark.slow
    async def test_single_request_performance(self, async_client, sample_request, performance_thresholds):
        """Test performance of single TQR request"""
        response_times = []
        
        # Run multiple single requests to get baseline
        for i in range(5):
            start_time = time.time()
            
            response = await async_client.post(
                "/api/v1/ai-test/generate-tqr",
                json=sample_request
            )
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to ms
            
            if response.status_code == 200:
                response_times.append(response_time)
                print(f"Request {i+1}: {response_time:.2f}ms")
            else:
                print(f"Request {i+1}: Failed with status {response.status_code}")
        
        if response_times:
            avg_time = statistics.mean(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            
            print(f"Performance Results:")
            print(f"  Average: {avg_time:.2f}ms")
            print(f"  Min: {min_time:.2f}ms")
            print(f"  Max: {max_time:.2f}ms")
            
            # Assert performance thresholds
            assert avg_time < performance_thresholds["avg_response_time"]
            assert max_time < performance_thresholds["max_response_time"]
        else:
            pytest.skip("No successful requests to measure performance")

    @pytest.mark.asyncio
    @pytest.mark.performance
    @pytest.mark.slow
    async def test_concurrent_requests_performance(self, async_client, sample_request, performance_thresholds):
        """Test performance under concurrent load"""
        concurrent_users = 3  # Reduced for testing
        requests_per_user = 2
        
        async def user_session(user_id):
            """Simulate a user session with multiple requests"""
            session_results = []
            
            for request_num in range(requests_per_user):
                start_time = time.time()
                
                try:
                    response = await async_client.post(
                        "/api/v1/ai-test/generate-tqr",
                        json={
                            **sample_request,
                            "name": f"User {user_id} Request {request_num}"
                        }
                    )
                    
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000
                    
                    session_results.append({
                        "user_id": user_id,
                        "request_num": request_num,
                        "status_code": response.status_code,
                        "response_time": response_time,
                        "success": response.status_code == 200
                    })
                    
                except Exception as e:
                    session_results.append({
                        "user_id": user_id,
                        "request_num": request_num,
                        "status_code": "ERROR",
                        "response_time": None,
                        "success": False,
                        "error": str(e)
                    })
            
            return session_results
        
        # Run concurrent user sessions
        start_time = time.time()
        tasks = [user_session(user_id) for user_id in range(concurrent_users)]
        all_results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Flatten results
        results = [result for session in all_results for result in session]
        
        # Analyze results
        successful_requests = [r for r in results if r["success"]]
        failed_requests = [r for r in results if not r["success"]]
        
        total_requests = len(results)
        success_count = len(successful_requests)
        success_rate = (success_count / total_requests) * 100 if total_requests > 0 else 0
        
        total_time = (end_time - start_time) * 1000
        
        print(f"Concurrent Load Test Results:")
        print(f"  Concurrent Users: {concurrent_users}")
        print(f"  Requests per User: {requests_per_user}")
        print(f"  Total Requests: {total_requests}")
        print(f"  Successful Requests: {success_count}")
        print(f"  Failed Requests: {len(failed_requests)}")
        print(f"  Success Rate: {success_rate:.2f}%")
        print(f"  Total Test Time: {total_time:.2f}ms")
        
        if successful_requests:
            response_times = [r["response_time"] for r in successful_requests]
            avg_response_time = statistics.mean(response_times)
            max_response_time = max(response_times)
            
            print(f"  Average Response Time: {avg_response_time:.2f}ms")
            print(f"  Max Response Time: {max_response_time:.2f}ms")
            
            # Assert performance thresholds (more lenient for concurrent)
            assert success_rate >= 50  # 50% success rate for concurrent
            assert avg_response_time < performance_thresholds["avg_response_time"] * 2  # 2x tolerance
        else:
            pytest.skip("No successful requests in concurrent test")

    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_health_check_performance(self, async_client):
        """Test health check endpoint performance"""
        response_times = []
        
        for i in range(10):
            start_time = time.time()
            
            response = await async_client.get("/api/v1/ai-test/health")
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            response_times.append(response_time)
            print(f"Health check {i+1}: {response_time:.2f}ms")
        
        avg_time = statistics.mean(response_times)
        max_time = max(response_times)
        
        print(f"Health Check Performance:")
        print(f"  Average: {avg_time:.2f}ms")
        print(f"  Max: {max_time:.2f}ms")
        
        # Health checks should be very fast
        assert avg_time < 1000  # 1 second average
        assert max_time < 5000  # 5 seconds max

    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_memory_usage_monitoring(self, async_client, sample_request):
        """Monitor memory usage during requests"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            
            # Get baseline memory
            baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Make several requests
            for i in range(3):
                response = await async_client.post(
                    "/api/v1/ai-test/generate-tqr",
                    json=sample_request
                )
                
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - baseline_memory
                
                print(f"Request {i+1}: Memory usage: {current_memory:.2f}MB (+{memory_increase:.2f}MB)")
                
                # Memory shouldn't grow excessively
                assert memory_increase < 500  # Less than 500MB increase
            
            # Final memory check
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            total_increase = final_memory - baseline_memory
            
            print(f"Total memory increase: {total_increase:.2f}MB")
            assert total_increase < 1000  # Less than 1GB total increase
            
        except ImportError:
            pytest.skip("psutil not available for memory monitoring")

    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_response_time_consistency(self, async_client, sample_request):
        """Test response time consistency"""
        response_times = []
        
        for i in range(10):
            start_time = time.time()
            
            response = await async_client.post(
                "/api/v1/ai-test/generate-tqr",
                json=sample_request
            )
            
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            if response.status_code == 200:
                response_times.append(response_time)
        
        if len(response_times) >= 3:
            avg_time = statistics.mean(response_times)
            std_dev = statistics.stdev(response_times)
            coefficient_of_variation = std_dev / avg_time
            
            print(f"Response Time Consistency:")
            print(f"  Average: {avg_time:.2f}ms")
            print(f"  Std Dev: {std_dev:.2f}ms")
            print(f"  Coefficient of Variation: {coefficient_of_variation:.2f}")
            
            # Response times should be reasonably consistent
            assert coefficient_of_variation < 1.0  # Less than 100% variation
        else:
            pytest.skip("Not enough successful requests for consistency test")

    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_throughput_measurement(self, async_client, sample_request):
        """Measure system throughput"""
        duration = 30  # seconds
        max_concurrent = 2  # Reduced for testing
        
        async def throughput_worker():
            """Worker that makes requests continuously"""
            request_count = 0
            start_time = time.time()
            
            while (time.time() - start_time) < duration:
                try:
                    response = await async_client.post(
                        "/api/v1/ai-test/generate-tqr",
                        json=sample_request
                    )
                    
                    if response.status_code == 200:
                        request_count += 1
                    
                    # Small delay between requests
                    await asyncio.sleep(1)
                    
                except Exception:
                    pass
            
            return request_count
        
        # Run throughput test
        start_time = time.time()
        tasks = [throughput_worker() for _ in range(max_concurrent)]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        total_requests = sum(results)
        actual_duration = end_time - start_time
        throughput = total_requests / actual_duration if actual_duration > 0 else 0
        
        print(f"Throughput Test Results:")
        print(f"  Duration: {actual_duration:.2f}s")
        print(f"  Total Successful Requests: {total_requests}")
        print(f"  Throughput: {throughput:.2f} requests/second")
        
        # Should achieve some throughput
        assert total_requests > 0
        assert throughput >= 0.1  # At least 0.1 requests per second
