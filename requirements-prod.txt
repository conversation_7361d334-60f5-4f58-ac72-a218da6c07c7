# ImpactCV AI-Powered CV Generation System
# Production-Optimized Dependencies

# Include core dependencies
-r requirements.txt

# ============================================================================
# PRODUCTION WEB SERVER
# ============================================================================
gunicorn==21.2.0
uvicorn[standard]==0.24.0

# ============================================================================
# PRODUCTION MONITORING
# ============================================================================
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0
structlog==23.2.0

# ============================================================================
# PRODUCTION SECURITY
# ============================================================================
cryptography==41.0.7
python-jose[cryptography]==3.3.0

# ============================================================================
# PRODUCTION PERFORMANCE
# ============================================================================
orjson==3.9.10
cachetools==5.3.2
lru-dict==1.3.0

# ============================================================================
# PRODUCTION DATABASE
# ============================================================================
psycopg2-binary==2.9.9
asyncpg==0.29.0

# ============================================================================
# PRODUCTION CACHING
# ============================================================================
redis==5.0.1
hiredis==2.2.3

# ============================================================================
# PRODUCTION UTILITIES
# ============================================================================
python-dotenv==1.0.0
click==8.1.7
