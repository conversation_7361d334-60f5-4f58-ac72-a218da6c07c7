# Rules Manifest

## Conjunto de Reglas del Sistema Generador de Currículums con IA

## 🤖 Configuración de IA Local
**Modelo Principal**: Mistral 7B ejecutándose localmente
- **Servidor Local**: Ollama en `http://localhost:11434`
- **Modo Offline**: 100% sin dependencias externas
- **Fallback**: OpenAI API (configurable por variables de entorno)
- **Formato de Respuesta**: JSON estructurado para compatibilidad con TQR
- **Prompts**: Optimizados para Mistral 7B con instrucciones claras y ejemplos
- **Sanitización**: Validación y limpieza de inputs antes del procesamiento

### Arquitectura de Software y Diseño Tecnológico

El sistema debe diseñarse con arquitectura modular y microservicios, facilitando la escalabilidad horizontal en la nube. Cada componente (interfaz, almacenamiento de datos, motor de IA, agente RAG, etc.) se implementará como servicio desacoplado con APIs bien definidas (por ejemplo, REST/JSON con OpenAPI). El uso de contenedores (Docker/Kubernetes) y orquestadores garantiza alta disponibilidad y despliegues controlados.

Se debe prever el uso de un pipeline de extracción–generación (RAG): un módulo de recuperación de información alimentará datos consolidados al modelo Mistral 7B local, separando responsabilidades. Las llamadas al modelo de IA se harán vía HTTP local con gestión de errores y reintentos automáticos. Los datos de entrenamiento o embeddings deben actualizarse periódicamente sin interrumpir el servicio.

El diseño seguirá patrones comprobados (p. ej. orientación a eventos, arquitectura hexagonal) y permitirá integrar nuevos componentes en el futuro (por ejemplo, módulos adicionales de generación de cartas de presentación, análisis de lenguaje, multi-idioma, etc.) sin reestructurar todo el sistema. Se implementará versionado de microservicios y documentación técnica para facilitar la interoperabilidad interna.

### Ciberseguridad y Confianza Cero

Se aplicarán los principios de la lista OWASP Top 10 en el desarrollo, vigilando especialmente la validación y saneamiento de entradas y salidas. Por ejemplo, cualquier dato de usuario o externo debe filtrarse para prevenir inyecciones (SQL/NoSQL, comandos, HTML/JS) y otros ataques comunes. Se emplearán mecanismos de autenticación y autorización robustos (OAuth 2.0/OpenID Connect, MFA), cifrado de credenciales y tokens en reposo y TLS para tránsito. Cada servicio requiere autenticación independiente; no se confiará automáticamente en servicios dentro de la red (principio "nunca confíe, siempre verifique").

El modelo de confianza cero exige que cada conexión entre usuario, dispositivo y servicio se valide según políticas de seguridad. Esto implica segmentación de red (p. ej. VPC privadas, subredes aisladas), autenticación mutua (mTLS) entre microservicios y monitoreo continuo de accesos. Se implementará control de acceso granular (roles/propietarios de datos) y políticas de privilegio mínimo para todos los recursos sensibles.

Se mantendrán actualizados los componentes (lenguajes, frameworks, librerías) y se deshabilitarán configuraciones por defecto inseguras. Se realizará escaneo automático de vulnerabilidades (SAST, DAST) durante el desarrollo y pruebas de penetración regulares. Además, se usarán security gates en el pipeline de CI/CD para bloquear despliegues con vulnerabilidades detectadas, de forma que "solo llegue a producción código seguro".

### Gobernanza y Calidad de Datos (alineado con DAMA-DMBOK v2)

Los datos (perfil del usuario, plantillas de CV, historiales profesionales, ontologías de habilidades, etc.) se tratarán como activos con roles y responsabilidades claras (Data Owners y Data Stewards asignados). Se definirá un catálogo de datos y un diccionario de datos con metadatos que describan cada campo, origen y formato. Esto cumple los principios de DAMA-DMBOK de gestión del ciclo de vida de los datos: gobierno, calidad y seguridad.

Se implementarán procesos formales de calidad de datos: validación de formato (fechas, números), normalización (nombre y dirección de texto consistente), limpieza (eliminar duplicados) y reglas de negocio (p.ej. la fecha de ingreso debe ser anterior a la fecha de egreso en empleos). Se monitorizará la calidad con métricas (porcentaje de datos faltantes, inconsistencias) y se notificará a un responsable cuando se detecten anomalías.

La gobernanza de datos incluye cumplimiento normativo y privacidad: se documentarán flujos de datos y políticas de retención/archivo. Se llevarán registros (data lineage) de origen de cada dato extraído (p.ej. fuentes públicas, entradas del usuario) para auditoría. Además, se incorporarán revisiones periódicas de calidad y pruebas de integridad para asegurar que los CV generados reflejen datos veraces y confiables, evitando sesgos o errores acumulados.

### DevSecOps y Pipeline CI/CD

Se establecerá un entorno de DevSecOps: todo código vive en control de versiones (Git con ramas por característica), se somete a revisiones de código obligatorias (pull requests con aprobado de pares) y pruebas automáticas antes de integrarse. Cada commit gatillará el CI para ejecutar linters, análisis estático de seguridad, compilación y baterías de pruebas (unitarias, integradas y de regresión). Sólo se desplegará el código al staging o producción si pasa estas validaciones.

El pipeline de CI/CD incluirá escaneo automatizado de vulnerabilidades de dependencias, análisis de contenedores, e infraestructura como código (IaC). Se usarán security gates que detengan el avance si SAST/DAST encuentran fallas: "puertas de seguridad" donde el código debe cumplir criterios de seguridad para proseguir. También se automatizarán chequeos de cumplimiento (por ejemplo, políticas de cifrado, privacidad o normativas locales) reduciendo el error humano.

Se implementará monitorización continua del rendimiento y la consistencia del modelo de IA: se registrarán los datos de entrada, las respuestas del modelo y métricas de rendimiento para detectar drift de datos o concepto. El sistema debe detectar de inmediato desviaciones significativas (por ejemplo, cambios inusuales en la distribución de entradas o en la precisión) para activar retraining o investigaciones. Esto se logrará con herramientas de monitoreo de modelos ML que alerten ante anomalías y permitan mantener la fiabilidad a lo largo del tiempo.

### Interoperabilidad y Escalabilidad

Se garantizará interoperabilidad usando estándares abiertos y APIs documentadas (p. ej. OpenAPI/Swagger para los endpoints REST). Los datos intercambiados (CV en formato JSON, plantillas, registros de usuario) seguirán esquemas versionados. Las interfaces mantendrán compatibilidad hacia atrás, y las versiones mayores se gestionarán cuidadosamente para no romper integraciones existentes.

Se diseñará el sistema para poder escalar tanto vertical como horizontalmente según la demanda: capacidad para aumentar instancias de procesamiento IA o bases de datos sin afectar el servicio. Deberá soportar despliegues multi-región o de alta disponibilidad como en grandes empresas (p. ej. balanceadores de carga, copias redundantes de bases de datos). La arquitectura deberá ser lo suficientemente flexible para incorporar en el futuro nuevos módulos (agentes de IA especializados, integraciones con LinkedIn/portales de empleo, etc.) sin re-arquitectura fundamental.

Para la interoperabilidad interna, se usarán buses de mensajes o colas (Kafka, RabbitMQ, SQS) para desacoplar servicios y permitir flujos de trabajo asíncronos (p. ej. generación de CV por lote, notificaciones). Esto facilita la integración de componentes adicionales y la comunicación fiable entre microservicios.

### Usabilidad y Experiencia de Usuario

La interfaz y el flujo de trabajo serán intuitivos y accesibles. El usuario podrá ingresar sus datos de manera guiada (pasos claros), visualizar avances en tiempo real y editar resultados antes de la versión final. Se aplicarán principios de diseño centrado en el usuario: pruebas de usabilidad, soporte multilenguaje y adaptabilidad a dispositivos móviles.

El sistema mostrará el carácter generativo del contenido (p. ej. aviso que el CV fue elaborado por IA) e incluirá mecanismos para corregir errores. Se proveerán ejemplos y sugerencias para mejorar el CV (por ej. recomendaciones de habilidades relevantes), pero el usuario tendrá control total sobre la edición final. Esto asegura transparencia y confianza en la tecnología.

Se medirá la experiencia de usuario mediante métricas (tiempos de respuesta, tasa de completitud del perfil, satisfacción) e iteraciones de mejora continua. Además, se documentarán flujos UX/UI (mockups, guías de estilo) para futuras expansiones, manteniendo consistencia visual en nuevos módulos.

### Privacidad y Protección de Datos

Se recogerán sólo los datos personales estrictamente necesarios (principio de minimización). Toda información sensible (nombre completo, contacto, historial laboral) se almacenará cifrada (AES-256 en reposo y TLS 1.2+/HTTPS en tránsito). Además, se establecerán mecanismos de control de acceso y registros de auditoría para saber quién consulta o modifica datos personales.

Se cumplirá la normativa de protección de datos aplicable (p.ej. GDPR/LOPD): se informará al usuario del propósito de la recolección, se obtendrá su consentimiento explícito y se le facilitarán derechos de acceso, rectificación o supresión. Los datos se retendrán sólo el tiempo necesario, con políticas claras de retención y borrado seguro. También se anonimizarán datos de usuarios si se utilizan para análisis o entrenamiento de modelos.

En caso de producción de datos por agentes de IA, se revisará que no se incluyan inadecuadamente datos sensibles. Se implementará un plan de respuesta a incidentes de datos personales, con cifrado de respaldo, gestión de llaves y pruebas regulares de restauración. En resumen, la privacidad estará integrada desde el diseño hasta la operación, siguiendo el principio de "privacidad por diseño".

### Buenas Prácticas de Desarrollo y Operaciones

**Control de versiones y revisión de código:** Todo código se gestionará en repositorio versionado (p.ej. Git) y empleará un esquema de versionado semántico. Las ramas de desarrollo deben fusionarse sólo después de code review por pares. Esto fomenta la colaboración y la calidad del código.

**Pruebas automatizadas:** Se mantendrá una suite completa de pruebas automatizadas (unitarias, de integración y de extremo a extremo) para cada servicio. Estas pruebas se ejecutan en cada integración continua y antes de cualquier despliegue, garantizando que nuevas características no rompan funcionalidades existentes.

**Registro y monitoreo centralizados:** Todos los microservicios enviarán logs estructurados a un sistema central (ELK, Splunk u otro), asegurando su integridad y disponibilidad. El logging facilitará la detección de anomalías y la trazabilidad de errores. Se configurarán alertas y dashboards (Prometheus/Grafana) para métricas clave (uso de CPU, latencia de respuestas, tasa de errores).

**Recuperación ante incidentes:** Se documentarán procedimientos de copia de seguridad (base de datos, modelos entrenados, configuraciones) y planes de recuperación ante desastres. Se realizarán simulacros (p.ej. chaos engineering básico) para probar la resiliencia. Si ocurre una falla crítica, se cuenta con procesos de rollback/rollback automático al último estado estable. Se designarán roles para respuesta a incidentes y comunicación clara con usuarios afectados.

**Monitoreo de seguridad y cumplimiento:** Se desplegarán herramientas de monitoreo continuo de seguridad (IDS/IPS) y de gestión de vulnerabilidades en producción. Los registros de acceso se revisarán periódicamente para detectar actividades sospechosas. Además, se mantendrá un ciclo de actualización de dependencias y parches, evitando software obsoleto.

**Documentación y capacitación:** Se generará documentación técnica y de usuario clara (archivos de configuración, manuales de API, guías de uso) para mantener la calidad del desarrollo. El equipo deberá recibir entrenamiento continuo en prácticas seguras y nuevas tecnologías (por ejemplo, métodos de auditoría de IA), asegurando que el sistema evolucione con altos estándares de calidad.

---

*Estas reglas se basan en estándares reconocidos (OWASP Top 10, arquitectura Zero Trust, guías DAMA-DMBOK, prácticas DevSecOps y mejores prácticas de cumplimiento/privacidad) para garantizar un desarrollo robusto y seguro, equivalente al nivel tecnológico de empresas como Uber o Netflix.*
