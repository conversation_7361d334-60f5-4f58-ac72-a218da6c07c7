// Scroll progress service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class ScrollProgressService {
    constructor() {
        this.elements = new Map();
        this.subscribers = new Set();
        this.initialize();
    }

    /**
     * Initialize the scroll progress service
     */
    initialize() {
        // Add scroll event listener
        window.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
        window.addEventListener('resize', this.handleResize.bind(this), { passive: true });
    }

    /**
     * Subscribe to scroll progress events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of scroll progress events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in scroll progress subscriber:', error);
            }
        });
    }

    /**
     * Register a scroll progress element
     * @param {string} id - The element ID
     * @param {Object} options - The scroll progress options
     */
    registerElement(id, options = {}) {
        const element = document.getElementById(id);
        if (!element) {
            console.error(`Element ${id} not found`);
            return;
        }

        // Create progress bar
        const progressBar = document.createElement('div');
        progressBar.className = 'scroll-progress-bar';
        progressBar.style.position = 'fixed';
        progressBar.style.top = '0';
        progressBar.style.left = '0';
        progressBar.style.width = '0%';
        progressBar.style.height = options.height || '4px';
        progressBar.style.backgroundColor = options.color || '#000';
        progressBar.style.transition = 'width 0.1s ease-out';
        progressBar.style.zIndex = options.zIndex || '9999';
        document.body.appendChild(progressBar);

        this.elements.set(id, {
            ...options,
            element,
            progressBar,
            progress: 0,
        });

        // Initial update
        this.updateElement(id);
    }

    /**
     * Handle scroll events
     */
    handleScroll() {
        return measurePerformance('scroll_progress_scroll', () => {
            this.elements.forEach((progress, id) => {
                this.updateElement(id);
            });
        });
    }

    /**
     * Handle resize events
     */
    handleResize() {
        return measurePerformance('scroll_progress_resize', () => {
            this.elements.forEach((progress, id) => {
                this.updateElement(id);
            });
        });
    }

    /**
     * Update a scroll progress element
     * @param {string} id - The element ID
     */
    updateElement(id) {
        return measurePerformance('scroll_progress_update', () => {
            const progress = this.elements.get(id);
            if (!progress) {
                return;
            }

            const { element, progressBar, options } = progress;
            const rect = element.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

            // Calculate progress
            const scrollProgress = {
                x: (rect.left + scrollLeft) / viewportWidth,
                y: (rect.top + scrollTop) / viewportHeight,
                scroll: scrollTop / (document.documentElement.scrollHeight - viewportHeight),
                viewport: {
                    x: rect.left / viewportWidth,
                    y: rect.top / viewportHeight,
                    width: rect.width / viewportWidth,
                    height: rect.height / viewportHeight,
                },
            };

            // Update progress bar
            progressBar.style.width = `${scrollProgress.scroll * 100}%`;

            // Update progress
            progress.progress = scrollProgress.scroll;

            if (progress.onUpdate) {
                progress.onUpdate({
                    element,
                    progressBar,
                    progress: scrollProgress,
                });
            }

            this.notifySubscribers('update', { id, progress: scrollProgress });
        });
    }

    /**
     * Reset a scroll progress element
     * @param {string} id - The element ID
     */
    resetElement(id) {
        return measurePerformance('scroll_progress_reset', () => {
            const progress = this.elements.get(id);
            if (!progress) {
                return;
            }

            const { element, progressBar } = progress;

            // Reset progress bar
            progressBar.style.width = '0%';

            // Reset progress
            progress.progress = 0;

            if (progress.onReset) {
                progress.onReset({
                    element,
                    progressBar,
                });
            }

            this.notifySubscribers('reset', { id });
        });
    }

    /**
     * Get element data
     * @param {string} id - The element ID
     * @returns {Object} The element data
     */
    getElementData(id) {
        return this.elements.get(id);
    }

    /**
     * Update element data
     * @param {string} id - The element ID
     * @param {Object} data - The new element data
     */
    updateElementData(id, data) {
        const progress = this.elements.get(id);
        if (progress) {
            Object.assign(progress, data);
            this.updateElement(id);
        }
    }
}

// Create and export a singleton instance
const scrollProgressService = new ScrollProgressService();
export default scrollProgressService; 