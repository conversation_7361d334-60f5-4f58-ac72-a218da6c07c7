"""
Mistral 7B Local Service
Servicio para interactuar con Mistral 7B ejecutándose localmente via Ollama
"""

import json
import logging
import asyncio
from typing import Dict, Any, Optional, List
import aiohttp
from pydantic import BaseModel, Field

from app.core.config import get_settings

logger = logging.getLogger(__name__)

class MistralRequest(BaseModel):
    """Modelo para requests a Mistral 7B"""
    model: str = Field(default="mistral:7b", description="Modelo a usar")
    prompt: str = Field(..., description="Prompt para el modelo")
    stream: bool = Field(default=False, description="Si usar streaming")
    options: Dict[str, Any] = Field(default_factory=dict, description="Opciones adicionales")

class MistralResponse(BaseModel):
    """Modelo para respuestas de Mistral 7B"""
    response: str = Field(..., description="Respuesta del modelo")
    model: str = Field(..., description="Modelo usado")
    created_at: str = Field(..., description="Timestamp de creación")
    done: bool = Field(..., description="Si la respuesta está completa")
    context: Optional[List[int]] = Field(None, description="Context tokens")
    total_duration: Optional[int] = Field(None, description="Duración total")
    load_duration: Optional[int] = Field(None, description="Duración de carga")
    prompt_eval_count: Optional[int] = Field(None, description="Tokens evaluados del prompt")
    prompt_eval_duration: Optional[int] = Field(None, description="Duración evaluación prompt")
    eval_count: Optional[int] = Field(None, description="Tokens generados")
    eval_duration: Optional[int] = Field(None, description="Duración generación")

class MistralService:
    """Servicio para interactuar con Mistral 7B local"""
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = self.settings.MISTRAL_BASE_URL
        self.model_name = self.settings.MISTRAL_MODEL_NAME
        self.timeout = self.settings.MISTRAL_TIMEOUT
        self.max_retries = 3
        
    async def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make request to Mistral API"""
        try:
            url = f"{self.base_url}/{endpoint}"
            logger.info(f"Making request to: {url}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    json=data,
                    timeout=30
                ) as response:
                    logger.info(f"Response status: {response.status}")
                    logger.debug(f"Response headers: {dict(response.headers)}")
                    
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Mistral API error: {response.status} - {error_text}")
                        raise Exception(f"Mistral API error: {response.status} - {error_text}")
                    
                    response_data = await response.json()
                    logger.debug(f"Response data: {response_data}")
                    
                    if not response_data:
                        raise Exception("Empty response from Mistral API")
                    
                    return response_data
                    
        except aiohttp.ClientError as e:
            logger.error(f"Mistral API connection error: {e}")
            raise Exception(f"Mistral API connection error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in Mistral API request: {e}")
            raise Exception(f"Mistral API request failed: {str(e)}")

    async def generate_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 500,
        **kwargs
    ) -> str:
        """
        Generar completion usando Mistral 7B local
        
        Args:
            prompt: Prompt principal
            system_prompt: Prompt del sistema (opcional)
            temperature: Temperatura para la generación
            max_tokens: Máximo número de tokens
            **kwargs: Argumentos adicionales
            
        Returns:
            str: Respuesta generada por el modelo
        """
        try:
            # Construir el prompt completo
            full_prompt = self._build_prompt(prompt, system_prompt)
            logger.info(f"Full prompt length: {len(full_prompt)} chars")
            logger.debug(f"Full prompt: {full_prompt[:200]}...")
            
            # Preparar request
            request_data = {
                "model": self.model_name,
                "prompt": full_prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens,
                    "top_p": kwargs.get("top_p", 0.9),
                    "top_k": kwargs.get("top_k", 40),
                    "repeat_penalty": kwargs.get("repeat_penalty", 1.1),
                }
            }
            
            logger.info(f"Sending request to Mistral: {self.model_name}")
            logger.debug(f"Request data: {request_data}")
            
            response_data = await self._make_request("api/generate", request_data)
            logger.debug(f"Raw response data: {response_data}")
            
            # Parsear respuesta
            mistral_response = MistralResponse(**response_data)
            
            # Verificar que la respuesta no esté vacía
            if not mistral_response.response or not mistral_response.response.strip():
                logger.error("Empty response received from Mistral")
                raise Exception("Empty response from Mistral model")
            
            logger.info(f"Mistral response received: {len(mistral_response.response)} chars")
            logger.debug(f"Response content: {mistral_response.response[:200]}...")
            
            return mistral_response.response.strip()
            
        except Exception as e:
            logger.error(f"Error generating completion with Mistral: {e}")
            raise Exception(f"Mistral generation failed: {str(e)}")
    
    async def generate_json_completion(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        schema_example: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generar completion en formato JSON
        
        Args:
            prompt: Prompt principal
            system_prompt: Prompt del sistema
            schema_example: Ejemplo del esquema JSON esperado
            **kwargs: Argumentos adicionales
            
        Returns:
            Dict[str, Any]: Respuesta en formato JSON
        """
        try:
            # Construir prompt para JSON
            json_prompt = self._build_json_prompt(prompt, system_prompt, schema_example)
            
            # Extraer parámetros para evitar conflictos
            temperature = kwargs.pop("temperature", 0.3)  # Menor temperatura para JSON
            max_tokens = kwargs.pop("max_tokens", 500)

            # Generar respuesta
            response = await self.generate_completion(
                json_prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            
            # Log the raw response for debugging
            logger.debug(f"Raw Mistral response: {response}")
            
            # Extraer JSON de la respuesta
            json_response = self._extract_json(response)
            
            # Log the parsed JSON for debugging
            logger.debug(f"Parsed JSON response: {json_response}")
            
            return json_response
            
        except Exception as e:
            logger.error(f"Error generating JSON completion: {e}")
            logger.error(f"Raw response that caused error: {response if 'response' in locals() else 'No response'}")
            raise Exception(f"JSON generation failed: {str(e)}")
    
    def _build_prompt(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """Construir prompt completo para Mistral"""
        if system_prompt:
            return f"<s>[INST] {system_prompt}\n\n{prompt} [/INST]"
        else:
            return f"<s>[INST] {prompt} [/INST]"
    
    def _build_json_prompt(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        schema_example: Optional[Dict[str, Any]] = None
    ) -> str:
        """Construir prompt específico para generar JSON"""
        base_system = "Eres un asistente experto que responde únicamente en formato JSON válido."
        
        if system_prompt:
            full_system = f"{base_system} {system_prompt}"
        else:
            full_system = base_system
            
        json_instruction = "\n\nRespuesta requerida: JSON válido únicamente, sin texto adicional."
        
        if schema_example:
            json_instruction += f"\n\nEjemplo de formato esperado:\n{json.dumps(schema_example, indent=2, ensure_ascii=False)}"
        
        full_prompt = f"{prompt}{json_instruction}"
        
        return self._build_prompt(full_prompt, full_system)
    
    def _extract_json(self, response: str) -> Dict[str, Any]:
        """Extraer JSON válido de la respuesta"""
        try:
            # Log the input for debugging
            logger.debug(f"Attempting to extract JSON from: {response[:200]}...")
            
            # Limpiar la respuesta
            response = response.strip()
            
            # Si la respuesta completa es JSON
            if response.startswith('{') and response.endswith('}'):
                try:
                    return json.loads(response)
                except json.JSONDecodeError as e:
                    logger.debug(f"Failed to parse complete response as JSON: {e}")
            
            # Buscar JSON entre marcadores
            start_markers = ['{', '```json\n{', '```\n{', '```json{', '```{']
            end_markers = ['}', '}\n```', '}\n```', '}```', '}```']
            
            for start_marker, end_marker in zip(start_markers, end_markers):
                start_idx = response.find(start_marker)
                if start_idx != -1:
                    end_idx = response.rfind(end_marker)
                    if end_idx != -1:
                        json_str = response[start_idx:end_idx + len(end_marker.split('\n')[0])]
                        # Limpiar marcadores y espacios
                        json_str = json_str.replace('```json', '').replace('```', '').strip()
                        try:
                            return json.loads(json_str)
                        except json.JSONDecodeError as e:
                            logger.debug(f"Failed to parse JSON with markers {start_marker}/{end_marker}: {e}")
                            continue
            
            # Si no se encuentra JSON válido, intentar limpiar y parsear
            cleaned_response = response.replace('\n', ' ').replace('\r', '')
            # Buscar el primer { y último }
            start_idx = cleaned_response.find('{')
            end_idx = cleaned_response.rfind('}')
            if start_idx != -1 and end_idx != -1:
                json_str = cleaned_response[start_idx:end_idx + 1]
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError as e:
                    logger.debug(f"Failed to parse cleaned response: {e}")
            
            # Si todo falla, intentar parsear la respuesta completa
            try:
                return json.loads(response)
            except json.JSONDecodeError as e:
                logger.debug(f"Failed to parse raw response: {e}")
            
            # Si llegamos aquí, no pudimos extraer JSON válido
            raise json.JSONDecodeError("No valid JSON found in response", response, 0)
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from response: {response[:200]}...")
            logger.error(f"JSON decode error details: {str(e)}")
            raise Exception(f"Invalid JSON response: {str(e)}")

    async def health_check(self) -> Dict[str, Any]:
        """Verificar el estado del servicio Mistral"""
        try:
            # Test simple
            test_response = await self.generate_completion(
                "Responde únicamente: 'OK'",
                temperature=0.1,
                max_tokens=10
            )
            
            return {
                "status": "healthy",
                "model": self.model_name,
                "base_url": self.base_url,
                "test_response": test_response[:50]
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "model": self.model_name,
                "base_url": self.base_url,
                "error": str(e)
            }

# Instancia global del servicio
mistral_service = MistralService()