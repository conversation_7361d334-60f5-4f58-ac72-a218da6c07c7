// API Configuration
const API_BASE_URL = 'http://localhost:8000/api/v1';  // Changed back to absolute URL since we're running separate servers

// DOM Elements
const form = document.getElementById('cvForm');
const resultSection = document.getElementById('resultSection');
const taskElement = document.getElementById('task');
const quantificationElement = document.getElementById('quantification');
const resultElement = document.getElementById('result');
const loadingButton = document.getElementById('loadingButton');
const submitButton = document.getElementById('submitButton');

// Form submission handler
form.addEventListener('submit', async (e) => {
    e.preventDefault();
    console.log('Form submitted');
    
    // Show loading state
    loadingButton.classList.remove('hidden');
    submitButton.classList.add('hidden');
    
    try {
        // Get form data
        const formData = {
            name: document.getElementById('name').value,
            position: document.getElementById('currentPosition').value,
            experience_years: parseInt(document.getElementById('yearsOfExperience').value),
            achievement_description: document.getElementById('achievementDescription').value
        };
        
        console.log('Form data:', formData);
        
        // Make API request
        console.log('Making API request to:', `${API_BASE_URL}/ai-test/generate-tqr`);
        const response = await fetch(`${API_BASE_URL}/ai-test/generate-tqr`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Error response:', errorText);
            throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
        }
        
        const data = await response.json();
        console.log('Response data:', data);
        
        // Update result section
        taskElement.textContent = data.tarea;
        quantificationElement.textContent = data.cuantificacion;
        resultElement.textContent = data.resultado;
        
        // Show result section
        resultSection.classList.remove('hidden');
        console.log('Achievement generated successfully');
        
    } catch (error) {
        console.error('Error generating achievement:', error);
        console.error('Error stack:', error.stack);
        alert('Failed to generate achievement. Please try again.');
    } finally {
        // Hide loading state
        loadingButton.classList.add('hidden');
        submitButton.classList.remove('hidden');
        console.log('Form submission completed');
    }
});

// Analytics tracking
function trackEvent(eventName, eventData) {
    // TODO: Implement analytics tracking
    console.log('Event tracked:', eventName, eventData);
}

// Performance monitoring
function measurePerformance(operation, startTime) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    console.log(`${operation} took ${duration}ms`);
    return duration;
}

// Error handling
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    trackEvent('error', {
        message: event.error.message,
        stack: event.error.stack
    });
});

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    console.log('ImpactCV frontend initialized');
    trackEvent('page_view', {
        path: window.location.pathname
    });
}); 