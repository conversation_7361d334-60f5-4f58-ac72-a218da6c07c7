# ImpactCV AI-Powered CV Generation System
# Infrastructure as Code - Main Configuration

terraform {
  required_version = ">= 1.6"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.24"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.12"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.6"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
  }

  backend "s3" {
    bucket         = "impactcv-terraform-state"
    key            = "infrastructure/terraform.tfstate"
    region         = "us-west-2"
    encrypt        = true
    dynamodb_table = "impactcv-terraform-locks"
  }
}

# ============================================================================
# PROVIDER CONFIGURATION
# ============================================================================
provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "ImpactCV"
      Environment = var.environment
      ManagedBy   = "Terraform"
      Owner       = "DevOps Team"
      CostCenter  = "Engineering"
    }
  }
}

provider "kubernetes" {
  host                   = module.eks.cluster_endpoint
  cluster_ca_certificate = base64decode(module.eks.cluster_certificate_authority_data)
  
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args        = ["eks", "get-token", "--cluster-name", module.eks.cluster_name]
  }
}

provider "helm" {
  kubernetes {
    host                   = module.eks.cluster_endpoint
    cluster_ca_certificate = base64decode(module.eks.cluster_certificate_authority_data)
    
    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "aws"
      args        = ["eks", "get-token", "--cluster-name", module.eks.cluster_name]
    }
  }
}

# ============================================================================
# DATA SOURCES
# ============================================================================
data "aws_availability_zones" "available" {
  state = "available"
}

data "aws_caller_identity" "current" {}

# ============================================================================
# LOCAL VALUES
# ============================================================================
locals {
  name_prefix = "impactcv-${var.environment}"
  
  common_tags = {
    Project     = "ImpactCV"
    Environment = var.environment
    ManagedBy   = "Terraform"
    Owner       = "DevOps Team"
  }
  
  azs = slice(data.aws_availability_zones.available.names, 0, 3)
}

# ============================================================================
# NETWORKING MODULE
# ============================================================================
module "vpc" {
  source = "./modules/networking"
  
  name_prefix = local.name_prefix
  environment = var.environment
  
  vpc_cidr = var.vpc_cidr
  azs      = local.azs
  
  private_subnets = var.private_subnets
  public_subnets  = var.public_subnets
  
  enable_nat_gateway   = true
  enable_vpn_gateway   = false
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = local.common_tags
}

# ============================================================================
# SECURITY MODULE
# ============================================================================
module "security" {
  source = "./modules/security"
  
  name_prefix = local.name_prefix
  environment = var.environment
  vpc_id      = module.vpc.vpc_id
  
  # Security groups
  allowed_cidr_blocks = var.allowed_cidr_blocks
  
  # WAF configuration
  enable_waf = var.enable_waf
  
  # Certificate management
  domain_name = var.domain_name
  
  tags = local.common_tags
}

# ============================================================================
# EKS CLUSTER MODULE
# ============================================================================
module "eks" {
  source = "./modules/eks"
  
  name_prefix = local.name_prefix
  environment = var.environment
  
  # Cluster configuration
  cluster_version = var.kubernetes_version
  vpc_id          = module.vpc.vpc_id
  subnet_ids      = module.vpc.private_subnets
  
  # Node groups
  node_groups = var.eks_node_groups
  
  # Security
  cluster_security_group_id = module.security.eks_cluster_security_group_id
  node_security_group_id    = module.security.eks_node_security_group_id
  
  # Add-ons
  cluster_addons = {
    coredns = {
      most_recent = true
    }
    kube-proxy = {
      most_recent = true
    }
    vpc-cni = {
      most_recent = true
    }
    aws-ebs-csi-driver = {
      most_recent = true
    }
  }
  
  tags = local.common_tags
}

# ============================================================================
# DATABASE MODULE
# ============================================================================
module "database" {
  source = "./modules/database"
  
  name_prefix = local.name_prefix
  environment = var.environment
  
  # RDS configuration
  engine_version    = var.postgres_version
  instance_class    = var.db_instance_class
  allocated_storage = var.db_allocated_storage
  
  # Networking
  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets
  
  # Security
  security_group_ids = [module.security.database_security_group_id]
  
  # Backup and maintenance
  backup_retention_period = var.db_backup_retention_period
  backup_window          = var.db_backup_window
  maintenance_window     = var.db_maintenance_window
  
  # Encryption
  storage_encrypted = true
  kms_key_id       = module.security.database_kms_key_id
  
  tags = local.common_tags
}

# ============================================================================
# CACHE MODULE (REDIS)
# ============================================================================
module "cache" {
  source = "./modules/cache"
  
  name_prefix = local.name_prefix
  environment = var.environment
  
  # ElastiCache configuration
  node_type               = var.redis_node_type
  num_cache_nodes        = var.redis_num_nodes
  parameter_group_name   = var.redis_parameter_group
  port                   = 6379
  
  # Networking
  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets
  
  # Security
  security_group_ids = [module.security.cache_security_group_id]
  
  # Encryption
  at_rest_encryption_enabled = true
  transit_encryption_enabled = true
  auth_token                 = var.redis_auth_token
  
  tags = local.common_tags
}

# ============================================================================
# MONITORING MODULE
# ============================================================================
module "monitoring" {
  source = "./modules/monitoring"
  
  name_prefix = local.name_prefix
  environment = var.environment
  
  # CloudWatch configuration
  log_retention_in_days = var.log_retention_days
  
  # Prometheus and Grafana
  enable_prometheus = var.enable_prometheus
  enable_grafana    = var.enable_grafana
  
  # EKS cluster info
  cluster_name = module.eks.cluster_name
  
  # Alerting
  sns_topic_arn = module.security.sns_topic_arn
  
  tags = local.common_tags
}

# ============================================================================
# STORAGE MODULE
# ============================================================================
module "storage" {
  source = "./modules/storage"
  
  name_prefix = local.name_prefix
  environment = var.environment
  
  # S3 buckets
  create_app_bucket    = true
  create_backup_bucket = true
  create_logs_bucket   = true
  
  # Encryption
  kms_key_id = module.security.s3_kms_key_id
  
  # Lifecycle policies
  enable_lifecycle_policies = true
  
  tags = local.common_tags
}

# ============================================================================
# LOAD BALANCER MODULE
# ============================================================================
module "load_balancer" {
  source = "./modules/load_balancer"
  
  name_prefix = local.name_prefix
  environment = var.environment
  
  # ALB configuration
  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.public_subnets
  
  # Security
  security_group_ids = [module.security.alb_security_group_id]
  certificate_arn    = module.security.acm_certificate_arn
  
  # WAF
  web_acl_arn = module.security.waf_web_acl_arn
  
  tags = local.common_tags
}

# ============================================================================
# SECRETS MANAGEMENT
# ============================================================================
resource "aws_secretsmanager_secret" "app_secrets" {
  name                    = "${local.name_prefix}-app-secrets"
  description             = "Application secrets for ImpactCV"
  recovery_window_in_days = 7
  
  kms_key_id = module.security.secrets_kms_key_id
  
  tags = local.common_tags
}

resource "aws_secretsmanager_secret_version" "app_secrets" {
  secret_id = aws_secretsmanager_secret.app_secrets.id
  secret_string = jsonencode({
    database_url    = module.database.connection_string
    redis_url       = module.cache.connection_string
    jwt_secret_key  = random_password.jwt_secret.result
    openai_api_key  = var.openai_api_key
  })
}

# ============================================================================
# RANDOM RESOURCES
# ============================================================================
resource "random_password" "jwt_secret" {
  length  = 64
  special = true
}

# ============================================================================
# KUBERNETES MANIFESTS
# ============================================================================
resource "kubernetes_namespace" "impactcv" {
  metadata {
    name = "impactcv"
    
    labels = {
      name        = "impactcv"
      environment = var.environment
    }
  }
  
  depends_on = [module.eks]
}

# ============================================================================
# HELM RELEASES
# ============================================================================
resource "helm_release" "nginx_ingress" {
  name       = "nginx-ingress"
  repository = "https://kubernetes.github.io/ingress-nginx"
  chart      = "ingress-nginx"
  namespace  = "ingress-nginx"
  
  create_namespace = true
  
  set {
    name  = "controller.service.type"
    value = "LoadBalancer"
  }
  
  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-type"
    value = "nlb"
  }
  
  depends_on = [module.eks]
}

resource "helm_release" "cert_manager" {
  name       = "cert-manager"
  repository = "https://charts.jetstack.io"
  chart      = "cert-manager"
  namespace  = "cert-manager"
  
  create_namespace = true
  
  set {
    name  = "installCRDs"
    value = "true"
  }
  
  depends_on = [module.eks]
}
