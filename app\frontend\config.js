// Frontend configuration
const config = {
    // API Configuration
    api: {
        baseUrl: process.env.API_BASE_URL || 'http://localhost:8000/api/v1',
        timeout: 30000, // 30 seconds
        retryAttempts: 3,
        retryDelay: 1000, // 1 second
    },

    // Analytics Configuration
    analytics: {
        enabled: process.env.ENABLE_ANALYTICS === 'true',
        trackingId: process.env.ANALYTICS_ID || '',
        events: {
            pageView: 'page_view',
            formSubmit: 'form_submit',
            generationSuccess: 'generation_success',
            generationError: 'generation_error',
        },
    },

    // Performance Monitoring
    performance: {
        enabled: process.env.ENABLE_PERFORMANCE_MONITORING === 'true',
        metrics: {
            pageLoad: 'page_load',
            apiResponse: 'api_response',
            formSubmit: 'form_submit',
        },
        thresholds: {
            pageLoad: 3000, // 3 seconds
            apiResponse: 5000, // 5 seconds
            formSubmit: 1000, // 1 second
        },
    },

    // Feature Flags
    features: {
        enableCaching: true,
        enableOfflineMode: false,
        enablePrint: true,
        enableExport: true,
    },

    // UI Configuration
    ui: {
        theme: 'light',
        language: 'en',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: 'HH:mm:ss',
        maxInputLength: {
            name: 100,
            position: 100,
            description: 500,
        },
    },

    // Error Handling
    errors: {
        showDetailedErrors: process.env.NODE_ENV === 'development',
        maxErrorLogs: 100,
        errorReportingEndpoint: process.env.ERROR_REPORTING_ENDPOINT || '',
    },

    // Cache Configuration
    cache: {
        enabled: true,
        maxAge: 3600, // 1 hour
        storage: 'localStorage',
    },
};

// Export configuration
export default config; 