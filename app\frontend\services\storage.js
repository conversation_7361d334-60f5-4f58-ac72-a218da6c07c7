// Storage service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class StorageService {
    constructor() {
        this.storage = window.localStorage;
        this.prefix = config.storage?.prefix || 'app_';
        this.subscribers = new Map();
    }

    /**
     * Subscribe to storage changes
     * @param {string} key - The storage key to subscribe to
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(key, callback) {
        if (!this.subscribers.has(key)) {
            this.subscribers.set(key, new Set());
        }
        this.subscribers.get(key).add(callback);

        return () => {
            const callbacks = this.subscribers.get(key);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.subscribers.delete(key);
                }
            }
        };
    }

    /**
     * Notify subscribers of storage changes
     * @param {string} key - The storage key that changed
     * @param {*} value - The new value
     */
    notifySubscribers(key, value) {
        const callbacks = this.subscribers.get(key);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(value);
                } catch (error) {
                    console.error('Error in storage subscriber:', error);
                }
            });
        }
    }

    /**
     * Get a value from storage
     * @param {string} key - The storage key
     * @param {*} [defaultValue] - The default value if key doesn't exist
     * @returns {*} The stored value
     */
    get(key, defaultValue = null) {
        return measurePerformance('storage_get', () => {
            try {
                const value = this.storage.getItem(this.prefix + key);
                return value ? JSON.parse(value) : defaultValue;
            } catch (error) {
                console.error(`Error getting value for key ${key}:`, error);
                return defaultValue;
            }
        });
    }

    /**
     * Set a value in storage
     * @param {string} key - The storage key
     * @param {*} value - The value to store
     */
    set(key, value) {
        return measurePerformance('storage_set', () => {
            try {
                const serializedValue = JSON.stringify(value);
                this.storage.setItem(this.prefix + key, serializedValue);
                this.notifySubscribers(key, value);
            } catch (error) {
                console.error(`Error setting value for key ${key}:`, error);
            }
        });
    }

    /**
     * Remove a value from storage
     * @param {string} key - The storage key
     */
    remove(key) {
        return measurePerformance('storage_remove', () => {
            try {
                this.storage.removeItem(this.prefix + key);
                this.notifySubscribers(key, null);
            } catch (error) {
                console.error(`Error removing value for key ${key}:`, error);
            }
        });
    }

    /**
     * Clear all storage
     */
    clear() {
        return measurePerformance('storage_clear', () => {
            try {
                this.storage.clear();
                this.subscribers.forEach((callbacks, key) => {
                    callbacks.forEach(callback => callback(null));
                });
            } catch (error) {
                console.error('Error clearing storage:', error);
            }
        });
    }

    /**
     * Get all storage keys
     * @returns {string[]} The storage keys
     */
    keys() {
        return measurePerformance('storage_keys', () => {
            try {
                return Object.keys(this.storage)
                    .filter(key => key.startsWith(this.prefix))
                    .map(key => key.slice(this.prefix.length));
            } catch (error) {
                console.error('Error getting storage keys:', error);
                return [];
            }
        });
    }

    /**
     * Check if a key exists in storage
     * @param {string} key - The storage key
     * @returns {boolean} Whether the key exists
     */
    has(key) {
        return measurePerformance('storage_has', () => {
            try {
                return this.storage.getItem(this.prefix + key) !== null;
            } catch (error) {
                console.error(`Error checking key ${key}:`, error);
                return false;
            }
        });
    }

    /**
     * Get the size of storage
     * @returns {number} The storage size in bytes
     */
    size() {
        return measurePerformance('storage_size', () => {
            try {
                let size = 0;
                for (let i = 0; i < this.storage.length; i++) {
                    const key = this.storage.key(i);
                    if (key.startsWith(this.prefix)) {
                        size += this.storage.getItem(key).length;
                    }
                }
                return size;
            } catch (error) {
                console.error('Error getting storage size:', error);
                return 0;
            }
        });
    }

    /**
     * Get the remaining storage space
     * @returns {number} The remaining storage space in bytes
     */
    remainingSpace() {
        return measurePerformance('storage_remaining', () => {
            try {
                const testKey = this.prefix + 'test';
                const testValue = 'x'.repeat(1024 * 1024); // 1MB
                let space = 0;

                while (true) {
                    try {
                        this.storage.setItem(testKey + space, testValue);
                        space += testValue.length;
                    } catch (e) {
                        break;
                    }
                }

                // Clean up test data
                for (let i = 0; i < space; i += testValue.length) {
                    this.storage.removeItem(testKey + i);
                }

                return space;
            } catch (error) {
                console.error('Error getting remaining storage space:', error);
                return 0;
            }
        });
    }

    /**
     * Set the storage prefix
     * @param {string} prefix - The new prefix
     */
    setPrefix(prefix) {
        this.prefix = prefix;
    }

    /**
     * Get the storage prefix
     * @returns {string} The current prefix
     */
    getPrefix() {
        return this.prefix;
    }
}

// Create and export a singleton instance
const storageService = new StorageService();
export default storageService; 