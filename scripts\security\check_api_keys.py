#!/usr/bin/env python3
"""
ImpactCV API Key Security Checker
Detect hardcoded API keys and secrets in code
"""

import re
import sys
from pathlib import Path
from typing import List, Tuple


class APIKeyChecker:
    """Check for hardcoded API keys and secrets."""
    
    def __init__(self):
        # Patterns for different types of API keys and secrets
        self.patterns = {
            "openai_api_key": {
                "pattern": r"sk-[a-zA-Z0-9]{48}",
                "description": "OpenAI API Key",
                "severity": "HIGH"
            },
            "generic_api_key": {
                "pattern": r"(?i)(api[_-]?key|apikey)\s*[:=]\s*['\"][a-zA-Z0-9]{20,}['\"]",
                "description": "Generic API Key",
                "severity": "MEDIUM"
            },
            "aws_access_key": {
                "pattern": r"AKIA[0-9A-Z]{16}",
                "description": "AWS Access Key",
                "severity": "HIGH"
            },
            "aws_secret_key": {
                "pattern": r"(?i)aws[_-]?secret[_-]?access[_-]?key\s*[:=]\s*['\"][a-zA-Z0-9/+=]{40}['\"]",
                "description": "AWS Secret Access Key",
                "severity": "HIGH"
            },
            "jwt_secret": {
                "pattern": r"(?i)jwt[_-]?secret\s*[:=]\s*['\"][a-zA-Z0-9]{20,}['\"]",
                "description": "JWT Secret",
                "severity": "HIGH"
            },
            "database_url": {
                "pattern": r"(?i)(database[_-]?url|db[_-]?url)\s*[:=]\s*['\"]postgresql://[^'\"]+['\"]",
                "description": "Database Connection String",
                "severity": "MEDIUM"
            },
            "password": {
                "pattern": r"(?i)password\s*[:=]\s*['\"][^'\"]{8,}['\"]",
                "description": "Hardcoded Password",
                "severity": "MEDIUM"
            },
            "private_key": {
                "pattern": r"-----BEGIN (RSA |EC |DSA )?PRIVATE KEY-----",
                "description": "Private Key",
                "severity": "HIGH"
            }
        }
        
        # File extensions to check
        self.file_extensions = {".py", ".js", ".ts", ".yaml", ".yml", ".json", ".env"}
        
        # Directories to exclude
        self.exclude_dirs = {
            "node_modules", ".git", "__pycache__", ".pytest_cache",
            "venv", ".venv", "env", ".env", "build", "dist",
            "htmlcov", "coverage-reports", "test-results"
        }
        
        # Files to exclude
        self.exclude_files = {
            ".gitignore", ".dockerignore", "requirements.txt",
            "package-lock.json", "yarn.lock"
        }
    
    def should_check_file(self, file_path: Path) -> bool:
        """Determine if a file should be checked."""
        # Check if any parent directory is in exclude list
        for parent in file_path.parents:
            if parent.name in self.exclude_dirs:
                return False
        
        # Check if file is in exclude list
        if file_path.name in self.exclude_files:
            return False
        
        # Check file extension
        return file_path.suffix in self.file_extensions
    
    def check_file(self, file_path: Path) -> List[Tuple[str, int, str, str, str]]:
        """Check a single file for API keys and secrets."""
        findings = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')
                
                for pattern_name, pattern_info in self.patterns.items():
                    pattern = pattern_info["pattern"]
                    description = pattern_info["description"]
                    severity = pattern_info["severity"]
                    
                    matches = re.finditer(pattern, content, re.MULTILINE)
                    
                    for match in matches:
                        # Find line number
                        line_num = content[:match.start()].count('\n') + 1
                        line_content = lines[line_num - 1].strip()
                        
                        # Skip if it's a comment explaining the pattern
                        if line_content.startswith('#') and 'example' in line_content.lower():
                            continue
                        
                        # Skip if it's in a test file with obvious test data
                        if 'test' in str(file_path).lower() and any(
                            test_indicator in line_content.lower() 
                            for test_indicator in ['test', 'mock', 'fake', 'dummy', 'example']
                        ):
                            continue
                        
                        findings.append((
                            str(file_path),
                            line_num,
                            pattern_name,
                            description,
                            severity
                        ))
        
        except Exception as e:
            print(f"Warning: Could not read file {file_path}: {e}")
        
        return findings
    
    def check_directory(self, directory: Path) -> List[Tuple[str, int, str, str, str]]:
        """Check all files in a directory recursively."""
        all_findings = []
        
        for file_path in directory.rglob("*"):
            if file_path.is_file() and self.should_check_file(file_path):
                findings = self.check_file(file_path)
                all_findings.extend(findings)
        
        return all_findings
    
    def print_findings(self, findings: List[Tuple[str, int, str, str, str]]):
        """Print findings in a formatted way."""
        if not findings:
            print("✅ No API keys or secrets found!")
            return
        
        print(f"❌ Found {len(findings)} potential API keys/secrets:")
        print("=" * 80)
        
        # Group by severity
        high_severity = [f for f in findings if f[4] == "HIGH"]
        medium_severity = [f for f in findings if f[4] == "MEDIUM"]
        
        if high_severity:
            print("🚨 HIGH SEVERITY ISSUES:")
            for file_path, line_num, pattern_name, description, severity in high_severity:
                print(f"  📁 {file_path}:{line_num}")
                print(f"     🔍 {description} ({pattern_name})")
                print()
        
        if medium_severity:
            print("⚠️  MEDIUM SEVERITY ISSUES:")
            for file_path, line_num, pattern_name, description, severity in medium_severity:
                print(f"  📁 {file_path}:{line_num}")
                print(f"     🔍 {description} ({pattern_name})")
                print()
        
        print("=" * 80)
        print("🔧 RECOMMENDATIONS:")
        print("1. Move secrets to environment variables")
        print("2. Use a secrets management service (AWS Secrets Manager, etc.)")
        print("3. Add sensitive files to .gitignore")
        print("4. Use configuration files that are not committed to version control")
        print("5. Consider using tools like python-dotenv for local development")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Check for hardcoded API keys and secrets")
    parser.add_argument(
        "path",
        nargs="?",
        default=".",
        help="Path to check (default: current directory)"
    )
    parser.add_argument(
        "--fail-on-findings",
        action="store_true",
        help="Exit with non-zero code if findings are detected"
    )
    
    args = parser.parse_args()
    
    checker = APIKeyChecker()
    path = Path(args.path)
    
    if path.is_file():
        findings = checker.check_file(path)
    elif path.is_dir():
        findings = checker.check_directory(path)
    else:
        print(f"❌ Path not found: {path}")
        sys.exit(1)
    
    checker.print_findings(findings)
    
    if args.fail_on_findings and findings:
        sys.exit(1)
    
    sys.exit(0)


if __name__ == "__main__":
    main()
