"""
RAG API Endpoints
RESTful API for RAG pipeline operations
"""

import logging
import time
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.api.deps import get_current_user, get_db
from app.core.rag_pipeline import RAGRequest, rag_pipeline
from app.models.user import User
from app.services.retrieval_service import RetrievalRequest, retrieval_service
from app.services.vector_store import SearchRequest, vector_store

logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response Models
class RAGGenerateRequest(BaseModel):
    """Request model for RAG generation."""
    
    query: str = Field(..., min_length=1, max_length=5000, description="User query or prompt")
    system_prompt: Optional[str] = Field(None, max_length=2000, description="Custom system prompt")
    context_config: Dict[str, Any] = Field(default_factory=dict, description="Context retrieval settings")
    llm_config: Dict[str, Any] = Field(default_factory=dict, description="LLM generation settings")


class RAGGenerateResponse(BaseModel):
    """Response model for RAG generation."""
    
    query: str = Field(..., description="Original query")
    response: str = Field(..., description="Generated response")
    sources: List[Dict[str, Any]] = Field(..., description="Source documents")
    metadata: Dict[str, Any] = Field(..., description="Generation metadata")


class DocumentSearchRequest(BaseModel):
    """Request model for document search."""
    
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    top_k: int = Field(default=5, ge=1, le=20, description="Number of results")
    score_threshold: float = Field(default=0.3, ge=0.0, le=1.0, description="Minimum similarity score")
    filter_metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata filters")


class DocumentSearchResponse(BaseModel):
    """Response model for document search."""
    
    query: str = Field(..., description="Search query")
    results: List[Dict[str, Any]] = Field(..., description="Search results")
    total_found: int = Field(..., description="Total results found")
    search_time_ms: float = Field(..., description="Search time in milliseconds")


class CVSectionRequest(BaseModel):
    """Request model for CV section generation."""
    
    section_type: str = Field(..., description="Type of CV section")
    user_data: Dict[str, Any] = Field(..., description="User data for the section")
    target_role: Optional[str] = Field(None, description="Target job role")
    industry: Optional[str] = Field(None, description="Target industry")


class CVImprovementRequest(BaseModel):
    """Request model for CV improvement."""
    
    current_content: str = Field(..., min_length=10, description="Current CV content")
    improvement_type: str = Field(default="general", description="Type of improvement")
    target_role: Optional[str] = Field(None, description="Target job role")
    industry: Optional[str] = Field(None, description="Target industry")


# RAG Generation Endpoints
@router.post("/generate", response_model=RAGGenerateResponse)
async def generate_with_rag(
    request: RAGGenerateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> RAGGenerateResponse:
    """
    Generate content using RAG pipeline.
    
    This endpoint uses the complete RAG pipeline to:
    1. Retrieve relevant context from the knowledge base
    2. Generate high-quality responses using LLM
    3. Return the response with source attribution
    """
    try:
        # Create RAG request
        rag_request = RAGRequest(
            query=request.query,
            system_prompt=request.system_prompt,
            context_config=request.context_config,
            llm_config=request.llm_config,
            user_id=str(current_user.id)
        )
        
        # Execute RAG pipeline
        rag_response = await rag_pipeline.generate(rag_request)
        
        # Prepare response
        return RAGGenerateResponse(
            query=rag_response.query,
            response=rag_response.response,
            sources=rag_response.sources,
            metadata={
                "retrieval_time_ms": rag_response.retrieval_time_ms,
                "generation_time_ms": rag_response.generation_time_ms,
                "total_time_ms": rag_response.total_time_ms,
                "context_tokens": rag_response.context_tokens,
                "generation_tokens": rag_response.generation_tokens,
                "context_relevance_score": rag_response.context_relevance_score,
                "sources_count": rag_response.sources_count,
                "model_used": rag_response.model_used,
                "cost_estimate": rag_response.cost_estimate,
            }
        )
        
    except Exception as e:
        logger.error(f"RAG generation failed for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"RAG generation failed: {str(e)}"
        )


@router.post("/cv/section", response_model=RAGGenerateResponse)
async def generate_cv_section(
    request: CVSectionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> RAGGenerateResponse:
    """
    Generate a specific CV section using RAG.
    
    This endpoint specializes in generating CV sections like:
    - Professional Summary
    - Work Experience
    - Skills
    - Education
    """
    try:
        # Add context filters for CV section generation
        context_filters = {"section_type": request.section_type}
        if request.target_role:
            context_filters["target_role"] = request.target_role
        if request.industry:
            context_filters["industry"] = request.industry
        
        # Generate CV section
        rag_response = await rag_pipeline.generate_cv_section(
            section_type=request.section_type,
            user_data=request.user_data,
            context_filters=context_filters
        )
        
        return RAGGenerateResponse(
            query=rag_response.query,
            response=rag_response.response,
            sources=rag_response.sources,
            metadata={
                "section_type": request.section_type,
                "retrieval_time_ms": rag_response.retrieval_time_ms,
                "generation_time_ms": rag_response.generation_time_ms,
                "total_time_ms": rag_response.total_time_ms,
                "sources_count": rag_response.sources_count,
                "model_used": rag_response.model_used,
            }
        )
        
    except Exception as e:
        logger.error(f"CV section generation failed for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"CV section generation failed: {str(e)}"
        )


@router.post("/cv/improve", response_model=RAGGenerateResponse)
async def improve_cv_content(
    request: CVImprovementRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> RAGGenerateResponse:
    """
    Improve existing CV content using RAG.
    
    This endpoint analyzes current CV content and provides
    specific improvement suggestions based on best practices.
    """
    try:
        # Generate improvement suggestions
        rag_response = await rag_pipeline.improve_cv_content(
            current_content=request.current_content,
            improvement_type=request.improvement_type
        )
        
        return RAGGenerateResponse(
            query=f"Improve CV content ({request.improvement_type})",
            response=rag_response.response,
            sources=rag_response.sources,
            metadata={
                "improvement_type": request.improvement_type,
                "target_role": request.target_role,
                "industry": request.industry,
                "retrieval_time_ms": rag_response.retrieval_time_ms,
                "generation_time_ms": rag_response.generation_time_ms,
                "total_time_ms": rag_response.total_time_ms,
                "sources_count": rag_response.sources_count,
            }
        )
        
    except Exception as e:
        logger.error(f"CV improvement failed for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"CV improvement failed: {str(e)}"
        )


# Document Search Endpoints
@router.post("/search", response_model=DocumentSearchResponse)
async def search_documents(
    request: DocumentSearchRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> DocumentSearchResponse:
    """
    Search documents in the knowledge base.
    
    This endpoint provides direct access to the vector search
    functionality for finding relevant documents.
    """
    try:
        import time
        start_time = time.time()
        
        # Create search request
        search_request = SearchRequest(
            query=request.query,
            top_k=request.top_k,
            score_threshold=request.score_threshold,
            filter_metadata=request.filter_metadata
        )
        
        # Execute search
        search_results = await vector_store.search(search_request)
        
        # Calculate search time
        search_time_ms = (time.time() - start_time) * 1000
        
        # Format results
        formatted_results = []
        for result in search_results:
            formatted_results.append({
                "document_id": result.document.id,
                "content": result.document.content,
                "metadata": result.document.metadata,
                "score": result.score,
                "rank": result.rank,
            })
        
        return DocumentSearchResponse(
            query=request.query,
            results=formatted_results,
            total_found=len(formatted_results),
            search_time_ms=search_time_ms
        )
        
    except Exception as e:
        logger.error(f"Document search failed for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Document search failed: {str(e)}"
        )


@router.get("/search/similar/{document_id}")
async def find_similar_documents(
    document_id: str,
    top_k: int = 5,
    score_threshold: float = 0.5,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> DocumentSearchResponse:
    """
    Find documents similar to a given document.
    
    This endpoint finds documents that are semantically similar
    to a reference document in the knowledge base.
    """
    try:
        import time
        start_time = time.time()
        
        # Find similar documents
        similar_results = await retrieval_service.get_similar_documents(
            document_id=document_id,
            top_k=top_k,
            score_threshold=score_threshold
        )
        
        # Calculate search time
        search_time_ms = (time.time() - start_time) * 1000
        
        # Format results
        formatted_results = []
        for result in similar_results:
            formatted_results.append({
                "document_id": result.document.id,
                "content": result.document.content,
                "metadata": result.document.metadata,
                "score": result.score,
                "rank": result.rank,
            })
        
        return DocumentSearchResponse(
            query=f"Similar to document {document_id}",
            results=formatted_results,
            total_found=len(formatted_results),
            search_time_ms=search_time_ms
        )
        
    except Exception as e:
        logger.error(f"Similar document search failed for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Similar document search failed: {str(e)}"
        )


# Statistics and Health Endpoints
@router.get("/stats")
async def get_rag_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get RAG pipeline statistics.
    
    Returns comprehensive statistics about the RAG pipeline
    performance and usage.
    """
    try:
        # Check if user has admin privileges
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin privileges required"
            )
        
        # Get statistics from all components
        stats = {
            "rag_pipeline": rag_pipeline.get_stats(),
            "retrieval_service": retrieval_service.get_stats(),
            "vector_store": vector_store.get_stats(),
        }
        
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get RAG stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get statistics: {str(e)}"
        )


@router.get("/health")
async def rag_health_check() -> Dict[str, Any]:
    """
    Perform RAG pipeline health check.
    
    This endpoint checks the health of all RAG components
    and returns their status.
    """
    try:
        # Perform health checks
        health_results = {
            "rag_pipeline": await rag_pipeline.health_check(),
            "retrieval_service": await retrieval_service.health_check(),
            "vector_store": await vector_store.health_check(),
        }
        
        # Determine overall health
        all_healthy = all(
            result.get("status") == "healthy"
            for result in health_results.values()
        )
        
        return {
            "status": "healthy" if all_healthy else "degraded",
            "components": health_results,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"RAG health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }
