openapi: 3.1.0
info:
  title: ImpactCV API
  description: |
    Enterprise-grade AI-powered CV generation system with RAG pipeline.
    
    ## Features
    - AI-powered CV generation using GPT-4o
    - RAG (Retrieval-Augmented Generation) pipeline
    - Document parsing (PDF, DOCX)
    - GDPR-compliant data processing
    - Zero Trust security architecture
    
    ## Authentication
    All endpoints require JWT authentication via Bear<PERSON> token.
    
    ## Rate Limiting
    - 100 requests per minute per user
    - 10 CV generations per hour per user
    
    ## Compliance
    - OWASP Top 10 security standards
    - GDPR data protection compliance
    - DAMA-DMBOK v2 data governance
  version: 1.0.0
  contact:
    name: ImpactCV API Support
    email: <EMAIL>
    url: https://docs.impactcv.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.impactcv.com/v1
    description: Production server
  - url: https://staging-api.impactcv.com/v1
    description: Staging server
  - url: http://localhost:8000/v1
    description: Development server

security:
  - BearerAuth: []

paths:
  /health:
    get:
      summary: Health check endpoint
      description: Returns the health status of the API
      tags:
        - Health
      security: []
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'

  /auth/login:
    post:
      summary: User authentication
      description: Authenticate user and return JWT token
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/refresh:
    post:
      summary: Refresh JWT token
      description: Refresh an expired JWT token
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshRequest'
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'

  /cv/generate:
    post:
      summary: Generate CV
      description: |
        Generate a CV using AI-powered RAG pipeline.
        Supports PDF and DOCX input documents.
      tags:
        - CV Generation
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CVGenerationRequest'
      responses:
        '202':
          description: CV generation started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CVGenerationResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          description: Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cv/{cv_id}/status:
    get:
      summary: Get CV generation status
      description: Check the status of a CV generation job
      tags:
        - CV Generation
      parameters:
        - name: cv_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: CV generation job ID
      responses:
        '200':
          description: CV status retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CVStatusResponse'
        '404':
          description: CV not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /cv/{cv_id}/download:
    get:
      summary: Download generated CV
      description: Download the generated CV in PDF format
      tags:
        - CV Generation
      parameters:
        - name: cv_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: CV generation job ID
      responses:
        '200':
          description: CV file
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        '404':
          description: CV not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /documents/upload:
    post:
      summary: Upload documents
      description: Upload documents for processing (PDF, DOCX)
      tags:
        - Document Processing
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentUploadRequest'
      responses:
        '201':
          description: Documents uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentUploadResponse'
        '400':
          description: Invalid file format or size
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /documents/{document_id}:
    get:
      summary: Get document details
      description: Retrieve processed document information
      tags:
        - Document Processing
      parameters:
        - name: document_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: Document ID
      responses:
        '200':
          description: Document details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentResponse'

  /templates:
    get:
      summary: List CV templates
      description: Get available CV templates
      tags:
        - Templates
      parameters:
        - name: category
          in: query
          schema:
            type: string
            enum: [professional, creative, academic, technical]
          description: Filter by template category
      responses:
        '200':
          description: List of templates
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateListResponse'

  /user/profile:
    get:
      summary: Get user profile
      description: Retrieve current user profile information
      tags:
        - User Management
      responses:
        '200':
          description: User profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    HealthResponse:
      type: object
      properties:
        status:
          type: string
          example: "healthy"
        timestamp:
          type: string
          format: date-time
        version:
          type: string
          example: "1.0.0"
        services:
          type: object
          properties:
            database:
              type: string
              example: "connected"
            redis:
              type: string
              example: "connected"
            openai:
              type: string
              example: "connected"

    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          format: password
          minLength: 8
          example: "securepassword123"

    AuthResponse:
      type: object
      properties:
        access_token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        refresh_token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        token_type:
          type: string
          example: "bearer"
        expires_in:
          type: integer
          example: 3600
        user:
          $ref: '#/components/schemas/User'

    RefreshRequest:
      type: object
      required:
        - refresh_token
      properties:
        refresh_token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

    CVGenerationRequest:
      type: object
      required:
        - template_id
        - documents
      properties:
        template_id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        documents:
          type: array
          items:
            type: string
            format: binary
          maxItems: 5
        preferences:
          type: object
          properties:
            style:
              type: string
              enum: [professional, creative, minimal]
            color_scheme:
              type: string
              enum: [blue, green, red, black]
            include_photo:
              type: boolean
              default: false

    CVGenerationResponse:
      type: object
      properties:
        cv_id:
          type: string
          format: uuid
          example: "123e4567-e89b-12d3-a456-************"
        status:
          type: string
          enum: [queued, processing, completed, failed]
          example: "queued"
        estimated_completion:
          type: string
          format: date-time
        message:
          type: string
          example: "CV generation started successfully"

    CVStatusResponse:
      type: object
      properties:
        cv_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [queued, processing, completed, failed]
        progress:
          type: integer
          minimum: 0
          maximum: 100
          example: 75
        created_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time
          nullable: true
        download_url:
          type: string
          format: uri
          nullable: true
        error_message:
          type: string
          nullable: true

    DocumentUploadRequest:
      type: object
      required:
        - files
      properties:
        files:
          type: array
          items:
            type: string
            format: binary
          maxItems: 10

    DocumentUploadResponse:
      type: object
      properties:
        documents:
          type: array
          items:
            $ref: '#/components/schemas/Document'

    DocumentResponse:
      type: object
      properties:
        document:
          $ref: '#/components/schemas/Document'

    Document:
      type: object
      properties:
        id:
          type: string
          format: uuid
        filename:
          type: string
          example: "resume.pdf"
        file_type:
          type: string
          enum: [pdf, docx]
        file_size:
          type: integer
          example: 1024000
        upload_date:
          type: string
          format: date-time
        processing_status:
          type: string
          enum: [pending, processing, completed, failed]
        extracted_data:
          type: object
          nullable: true
        quality_score:
          type: number
          minimum: 0
          maximum: 1
          example: 0.95

    TemplateListResponse:
      type: object
      properties:
        templates:
          type: array
          items:
            $ref: '#/components/schemas/Template'

    Template:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          example: "Professional Modern"
        category:
          type: string
          enum: [professional, creative, academic, technical]
        description:
          type: string
          example: "Clean and modern professional template"
        preview_url:
          type: string
          format: uri
        is_premium:
          type: boolean
          default: false

    UserProfileResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        first_name:
          type: string
          example: "John"
        last_name:
          type: string
          example: "Doe"
        created_at:
          type: string
          format: date-time
        subscription_tier:
          type: string
          enum: [free, premium, enterprise]
        cv_generation_count:
          type: integer
          example: 5
        cv_generation_limit:
          type: integer
          example: 10

    ErrorResponse:
      type: object
      properties:
        error:
          type: object
          properties:
            code:
              type: string
              example: "VALIDATION_ERROR"
            message:
              type: string
              example: "Invalid input data"
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                  message:
                    type: string
        request_id:
          type: string
          format: uuid
        timestamp:
          type: string
          format: date-time
