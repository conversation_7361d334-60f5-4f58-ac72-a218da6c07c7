"""
User Models
User management with security and profile features
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, Integer, String, Text, JSON
from sqlalchemy.orm import relationship

from app.models.base import Base


class User(Base):
    """
    User model with comprehensive security features.
    
    Implements:
    - OAuth 2.0 compatible user management
    - Account security (lockout, MFA)
    - GDPR compliance features
    - Audit trail integration
    """
    
    # Basic user information
    email = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="User email address (unique identifier)"
    )
    
    username = Column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique username for the user"
    )
    
    hashed_password = Column(
        String(255),
        nullable=False,
        doc="Bcrypt hashed password"
    )
    
    # User status and verification
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether the user account is active"
    )
    
    is_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether the user email is verified"
    )
    
    is_superuser = Column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        doc="Whether the user has admin privileges"
    )
    
    # Security features
    failed_login_attempts = Column(
        Integer,
        default=0,
        nullable=False,
        doc="Number of consecutive failed login attempts"
    )
    
    locked_until = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp until which the account is locked"
    )
    
    last_login = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Timestamp of last successful login"
    )
    
    last_password_change = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Timestamp of last password change"
    )
    
    # Multi-factor authentication
    mfa_enabled = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether MFA is enabled for this user"
    )
    
    mfa_secret = Column(
        String(255),
        nullable=True,
        doc="Encrypted MFA secret key"
    )
    
    mfa_backup_codes = Column(
        JSON,
        nullable=True,
        doc="Encrypted MFA backup codes (stored as JSON array)"
    )
    
    # OAuth 2.0 scopes and permissions
    scopes = Column(
        JSON,
        default=lambda: ["read", "user:read"],
        nullable=False,
        doc="OAuth 2.0 scopes granted to the user (stored as JSON array)"
    )
    
    # GDPR compliance
    gdpr_consent_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Date when user gave GDPR consent"
    )
    
    gdpr_consent_version = Column(
        String(10),
        nullable=True,
        doc="Version of privacy policy user consented to"
    )
    
    data_retention_date = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="Date when user data should be deleted"
    )
    
    # User preferences
    language = Column(
        String(10),
        default="en",
        nullable=False,
        doc="User's preferred language (ISO 639-1)"
    )
    
    timezone = Column(
        String(50),
        default="UTC",
        nullable=False,
        doc="User's timezone"
    )
    
    # Notification preferences
    email_notifications = Column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether user wants email notifications"
    )
    
    marketing_emails = Column(
        Boolean,
        default=False,
        nullable=False,
        doc="Whether user consents to marketing emails"
    )
    
    # Relationships
    profile = relationship(
        "UserProfile",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan"
    )
    
    sessions = relationship(
        "UserSession",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    cvs = relationship(
        "CV",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        """String representation of the user."""
        return f"<User(email={self.email}, username={self.username})>"
    
    @property
    def is_locked(self) -> bool:
        """Check if the account is currently locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    @property
    def full_name(self) -> Optional[str]:
        """Get user's full name from profile."""
        if self.profile:
            return f"{self.profile.first_name} {self.profile.last_name}".strip()
        return None
    
    def has_scope(self, scope: str) -> bool:
        """Check if user has a specific scope."""
        return scope in (self.scopes or [])
    
    def add_scope(self, scope: str) -> None:
        """Add a scope to the user."""
        if self.scopes is None:
            self.scopes = []
        if scope not in self.scopes:
            self.scopes = self.scopes + [scope]
    
    def remove_scope(self, scope: str) -> None:
        """Remove a scope from the user."""
        if self.scopes and scope in self.scopes:
            self.scopes = [s for s in self.scopes if s != scope]


class UserProfile(Base):
    """
    Extended user profile information.
    
    Stores additional user data that's not security-critical.
    """
    
    # Foreign key to user
    user_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        doc="Reference to the user"
    )
    
    # Personal information
    first_name = Column(
        String(100),
        nullable=True,
        doc="User's first name"
    )
    
    last_name = Column(
        String(100),
        nullable=True,
        doc="User's last name"
    )
    
    display_name = Column(
        String(150),
        nullable=True,
        doc="User's preferred display name"
    )
    
    bio = Column(
        Text,
        nullable=True,
        doc="User's biography or description"
    )
    
    # Contact information
    phone = Column(
        String(20),
        nullable=True,
        doc="User's phone number"
    )
    
    website = Column(
        String(255),
        nullable=True,
        doc="User's website URL"
    )
    
    linkedin_url = Column(
        String(255),
        nullable=True,
        doc="User's LinkedIn profile URL"
    )
    
    github_url = Column(
        String(255),
        nullable=True,
        doc="User's GitHub profile URL"
    )
    
    # Location information
    country = Column(
        String(100),
        nullable=True,
        doc="User's country"
    )
    
    city = Column(
        String(100),
        nullable=True,
        doc="User's city"
    )
    
    address = Column(
        Text,
        nullable=True,
        doc="User's full address"
    )
    
    # Professional information
    job_title = Column(
        String(150),
        nullable=True,
        doc="User's current job title"
    )
    
    company = Column(
        String(150),
        nullable=True,
        doc="User's current company"
    )
    
    industry = Column(
        String(100),
        nullable=True,
        doc="User's industry"
    )
    
    experience_years = Column(
        Integer,
        nullable=True,
        doc="Years of professional experience"
    )
    
    # Profile settings
    profile_visibility = Column(
        String(20),
        default="private",
        nullable=False,
        doc="Profile visibility: public, private, contacts"
    )
    
    avatar_url = Column(
        String(500),
        nullable=True,
        doc="URL to user's avatar image"
    )
    
    # Additional data as JSON
    custom_fields = Column(
        JSON,
        nullable=True,
        doc="Custom profile fields as JSON"
    )
    
    # Relationships
    user = relationship("User", back_populates="profile")
    
    def __repr__(self) -> str:
        """String representation of the profile."""
        return f"<UserProfile(user_id={self.user_id}, name={self.first_name} {self.last_name})>"


class UserSession(Base):
    """
    User session tracking for security and analytics.
    
    Tracks user sessions for:
    - Security monitoring
    - Session management
    - Analytics and insights
    """
    
    # Foreign key to user
    user_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("user.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="Reference to the user"
    )
    
    # Session information
    session_token = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique session token"
    )
    
    refresh_token = Column(
        String(255),
        unique=True,
        nullable=True,
        index=True,
        doc="Refresh token for session renewal"
    )
    
    # Session metadata
    ip_address = Column(
        String(45),  # IPv6 compatible
        nullable=True,
        doc="IP address of the session"
    )
    
    user_agent = Column(
        Text,
        nullable=True,
        doc="User agent string"
    )
    
    device_info = Column(
        JSON,
        nullable=True,
        doc="Device information as JSON"
    )
    
    # Session timing
    expires_at = Column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="When the session expires"
    )
    
    last_activity = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Last activity timestamp"
    )
    
    # Session status
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        doc="Whether the session is active"
    )
    
    revoked_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the session was revoked"
    )
    
    revoked_reason = Column(
        String(100),
        nullable=True,
        doc="Reason for session revocation"
    )
    
    # Relationships
    user = relationship("User", back_populates="sessions")
    
    def __repr__(self) -> str:
        """String representation of the session."""
        return f"<UserSession(user_id={self.user_id}, token={self.session_token[:8]}...)>"
    
    @property
    def is_expired(self) -> bool:
        """Check if the session is expired."""
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if the session is valid (active and not expired)."""
        return self.is_active and not self.is_expired and self.revoked_at is None
    
    def revoke(self, reason: str = "manual") -> None:
        """Revoke the session."""
        self.is_active = False
        self.revoked_at = datetime.utcnow()
        self.revoked_reason = reason
    
    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity = datetime.utcnow()
