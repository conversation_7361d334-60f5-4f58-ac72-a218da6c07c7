"""
Retrieval Service
High-level RAG retrieval with context management and ranking
"""

import logging
import time
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from app.services.vector_store import SearchRequest, SearchResult, vector_store

logger = logging.getLogger(__name__)


class RetrievalRequest(BaseModel):
    """Retrieval request model."""
    
    query: str = Field(..., min_length=1, description="Search query")
    top_k: int = Field(default=5, ge=1, le=20, description="Number of results")
    score_threshold: float = Field(default=0.3, ge=0.0, le=1.0, description="Minimum similarity score")
    context_window: int = Field(default=4000, ge=500, le=8000, description="Maximum context length")
    include_metadata: bool = Field(default=True, description="Include document metadata")
    rerank: bool = Field(default=True, description="Apply reranking algorithms")
    filter_metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata filters")


class RetrievalContext(BaseModel):
    """Retrieved context for RAG."""
    
    query: str = Field(..., description="Original query")
    context: str = Field(..., description="Combined context from documents")
    sources: List[Dict[str, Any]] = Field(..., description="Source documents")
    total_tokens: int = Field(..., description="Estimated token count")
    retrieval_time_ms: float = Field(..., description="Retrieval time")
    reranked: bool = Field(default=False, description="Whether results were reranked")


class RetrievalError(Exception):
    """Custom exception for retrieval service errors."""
    
    def __init__(self, message: str, error_code: str = "RETRIEVAL_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class RetrievalService:
    """
    Enterprise retrieval service for RAG applications.
    
    Features:
    - Intelligent context assembly
    - Result reranking and filtering
    - Token counting and management
    - Query expansion and refinement
    - Performance monitoring
    """
    
    def __init__(self):
        """Initialize retrieval service."""
        self.initialized = False
        self.retrieval_count = 0
        self.total_retrieval_time = 0.0
    
    async def initialize(self) -> None:
        """Initialize retrieval service."""
        if self.initialized:
            return
        
        try:
            # Initialize vector store
            await vector_store.initialize()
            
            self.initialized = True
            logger.info("Retrieval service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize retrieval service: {e}")
            raise RetrievalError(
                f"Retrieval service initialization failed: {str(e)}",
                error_code="INITIALIZATION_FAILED"
            )
    
    async def retrieve_context(self, request: RetrievalRequest) -> RetrievalContext:
        """
        Retrieve and assemble context for RAG.
        
        Args:
            request: Retrieval request
            
        Returns:
            RetrievalContext: Assembled context with metadata
        """
        if not self.initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            # Expand query if needed
            expanded_query = await self._expand_query(request.query)
            
            # Search vector store
            search_request = SearchRequest(
                query=expanded_query,
                top_k=request.top_k * 2,  # Get more results for reranking
                score_threshold=request.score_threshold,
                filter_metadata=request.filter_metadata
            )
            
            search_results = await vector_store.search(search_request)
            
            if not search_results:
                logger.warning(f"No results found for query: {request.query}")
                return RetrievalContext(
                    query=request.query,
                    context="",
                    sources=[],
                    total_tokens=0,
                    retrieval_time_ms=(time.time() - start_time) * 1000,
                    reranked=False
                )
            
            # Rerank results if requested
            if request.rerank:
                search_results = await self._rerank_results(request.query, search_results)
            
            # Limit to requested number
            search_results = search_results[:request.top_k]
            
            # Assemble context
            context, sources, total_tokens = await self._assemble_context(
                search_results,
                request.context_window,
                request.include_metadata
            )
            
            # Update statistics
            retrieval_time_ms = (time.time() - start_time) * 1000
            self.retrieval_count += 1
            self.total_retrieval_time += retrieval_time_ms
            
            logger.info(f"Retrieved context for '{request.query}' in {retrieval_time_ms:.2f}ms")
            
            return RetrievalContext(
                query=request.query,
                context=context,
                sources=sources,
                total_tokens=total_tokens,
                retrieval_time_ms=retrieval_time_ms,
                reranked=request.rerank
            )
            
        except Exception as e:
            logger.error(f"Context retrieval failed: {e}")
            raise RetrievalError(
                f"Context retrieval failed: {str(e)}",
                error_code="RETRIEVAL_FAILED"
            )
    
    async def _expand_query(self, query: str) -> str:
        """
        Expand query for better retrieval (placeholder for future enhancement).
        
        Args:
            query: Original query
            
        Returns:
            str: Expanded query
        """
        # For now, just return the original query
        # In the future, this could use techniques like:
        # - Query expansion with synonyms
        # - Adding domain-specific terms
        # - Query reformulation using LLM
        return query
    
    async def _rerank_results(self, query: str, results: List[SearchResult]) -> List[SearchResult]:
        """
        Rerank search results using additional signals.
        
        Args:
            query: Original query
            results: Search results to rerank
            
        Returns:
            List[SearchResult]: Reranked results
        """
        if len(results) <= 1:
            return results
        
        try:
            # Simple reranking based on multiple factors
            for result in results:
                # Start with similarity score
                rerank_score = result.score
                
                # Boost based on content length (prefer substantial content)
                content_length = len(result.document.content)
                if 100 <= content_length <= 2000:
                    rerank_score *= 1.1
                elif content_length > 2000:
                    rerank_score *= 1.05
                
                # Boost based on document type
                doc_type = result.document.metadata.get("document_type", "")
                if doc_type in ["cv", "resume", "template"]:
                    rerank_score *= 1.15
                elif doc_type == "example":
                    rerank_score *= 1.1
                
                # Boost recent documents
                created_at = result.document.metadata.get("created_at")
                if created_at:
                    # Simple recency boost (placeholder)
                    rerank_score *= 1.02
                
                # Update score
                result.score = rerank_score
            
            # Sort by new scores
            results.sort(key=lambda x: x.score, reverse=True)
            
            # Update ranks
            for i, result in enumerate(results):
                result.rank = i + 1
            
            return results
            
        except Exception as e:
            logger.warning(f"Reranking failed, using original order: {e}")
            return results
    
    async def _assemble_context(
        self,
        results: List[SearchResult],
        max_tokens: int,
        include_metadata: bool
    ) -> tuple[str, List[Dict[str, Any]], int]:
        """
        Assemble context from search results.
        
        Args:
            results: Search results
            max_tokens: Maximum token count
            include_metadata: Whether to include metadata
            
        Returns:
            tuple: (context_string, sources, token_count)
        """
        context_parts = []
        sources = []
        total_tokens = 0
        
        for result in results:
            # Prepare document content
            content = result.document.content.strip()
            
            # Add metadata if requested
            if include_metadata:
                metadata_parts = []
                
                # Add document type
                doc_type = result.document.metadata.get("document_type", "document")
                metadata_parts.append(f"Type: {doc_type}")
                
                # Add title if available
                title = result.document.metadata.get("title")
                if title:
                    metadata_parts.append(f"Title: {title}")
                
                # Add category if available
                category = result.document.metadata.get("category")
                if category:
                    metadata_parts.append(f"Category: {category}")
                
                if metadata_parts:
                    content = f"[{', '.join(metadata_parts)}]\n{content}"
            
            # Estimate tokens (rough approximation: 1 token ≈ 4 characters)
            content_tokens = len(content) // 4
            
            # Check if adding this content would exceed token limit
            if total_tokens + content_tokens > max_tokens:
                # Try to fit partial content
                remaining_tokens = max_tokens - total_tokens
                if remaining_tokens > 50:  # Only if we have meaningful space left
                    remaining_chars = remaining_tokens * 4
                    truncated_content = content[:remaining_chars] + "..."
                    context_parts.append(f"Document {result.rank}:\n{truncated_content}")
                    total_tokens += remaining_tokens
                break
            
            # Add full content
            context_parts.append(f"Document {result.rank}:\n{content}")
            total_tokens += content_tokens
            
            # Prepare source info
            source_info = {
                "document_id": result.document.id,
                "score": result.score,
                "rank": result.rank,
                "content_preview": content[:200] + "..." if len(content) > 200 else content,
            }
            
            if include_metadata:
                source_info["metadata"] = result.document.metadata
            
            sources.append(source_info)
        
        # Combine context parts
        context = "\n\n".join(context_parts)
        
        return context, sources, total_tokens
    
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate token count for text.
        
        Args:
            text: Text to estimate
            
        Returns:
            int: Estimated token count
        """
        # Rough approximation: 1 token ≈ 4 characters
        # This is a simplification; in production you might use tiktoken
        return len(text) // 4
    
    async def get_similar_documents(
        self,
        document_id: str,
        top_k: int = 5,
        score_threshold: float = 0.5
    ) -> List[SearchResult]:
        """
        Find documents similar to a given document.
        
        Args:
            document_id: ID of the reference document
            top_k: Number of similar documents to return
            score_threshold: Minimum similarity score
            
        Returns:
            List[SearchResult]: Similar documents
        """
        if not self.initialized:
            await self.initialize()
        
        try:
            # Get the reference document
            reference_doc = await vector_store.get_document(document_id)
            if not reference_doc:
                raise RetrievalError(
                    f"Document not found: {document_id}",
                    error_code="DOCUMENT_NOT_FOUND"
                )
            
            # Use document content as query
            search_request = SearchRequest(
                query=reference_doc.content,
                top_k=top_k + 1,  # +1 to account for the reference document itself
                score_threshold=score_threshold
            )
            
            results = await vector_store.search(search_request)
            
            # Filter out the reference document
            filtered_results = [
                result for result in results
                if result.document.id != document_id
            ]
            
            return filtered_results[:top_k]
            
        except Exception as e:
            logger.error(f"Similar document search failed: {e}")
            raise RetrievalError(
                f"Similar document search failed: {str(e)}",
                error_code="SIMILAR_SEARCH_FAILED"
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get retrieval service statistics."""
        avg_retrieval_time = (
            self.total_retrieval_time / self.retrieval_count
            if self.retrieval_count > 0 else 0
        )
        
        return {
            "initialized": self.initialized,
            "retrieval_count": self.retrieval_count,
            "total_retrieval_time_ms": self.total_retrieval_time,
            "avg_retrieval_time_ms": avg_retrieval_time,
            "vector_store_stats": vector_store.get_stats(),
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        try:
            if not self.initialized:
                await self.initialize()
            
            # Test retrieval
            request = RetrievalRequest(
                query="test query",
                top_k=1,
                context_window=1000
            )
            
            start_time = time.time()
            context = await self.retrieve_context(request)
            response_time = (time.time() - start_time) * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "context_length": len(context.context),
                "sources_found": len(context.sources),
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
            }


# Global service instance
retrieval_service = RetrievalService()
