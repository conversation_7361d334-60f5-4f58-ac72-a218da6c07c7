"""
Unit tests for API Endpoints
Tests the FastAPI endpoints for TQR generation and health checks
"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import status

from app.main import app
from app.api.v1.endpoints.ai_test import router


class TestAITestEndpoints:
    """Test suite for AI Test endpoints"""
    
    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_ai_service(self):
        """Mock AI service"""
        mock = AsyncMock()
        mock.generate_tqr_achievement.return_value = {
            "tarea": "Optimización del sistema de base de datos",
            "cuantificacion": "Reducción del 40% en tiempos de consulta",
            "resultado": "Mejora significativa en el rendimiento del sistema",
            "original_description": "Optimicé el sistema de base de datos"
        }
        mock.health_check.return_value = {
            "status": "healthy",
            "provider": "mistral",
            "model": "mistral:7b"
        }
        return mock
    
    @pytest.fixture
    def valid_tqr_request(self):
        """Valid TQR request data"""
        return {
            "name": "<PERSON>",
            "position": "Senior Developer",
            "experience_years": 5,
            "achievement_description": "Optimicé el sistema de base de datos reduciendo los tiempos de consulta en un 40%"
        }
    
    @pytest.fixture
    def invalid_tqr_requests(self):
        """Invalid TQR request data"""
        return [
            {},  # Empty request
            {"name": "Test"},  # Missing required fields
            {"name": "", "position": "Dev", "experience_years": 5, "achievement_description": "Test"},  # Empty name
            {"name": "Test", "position": "", "experience_years": 5, "achievement_description": "Test"},  # Empty position
            {"name": "Test", "position": "Dev", "experience_years": -1, "achievement_description": "Test"},  # Invalid years
            {"name": "Test", "position": "Dev", "experience_years": 5, "achievement_description": ""},  # Empty description
        ]

    def test_health_endpoint(self, client):
        """Test general health endpoint"""
        response = client.get("/")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"

    def test_ready_endpoint(self, client):
        """Test readiness endpoint"""
        response = client.get("/api/v1/health/ready")

        # Readiness check may return 503 if services are not available (which is correct behavior)
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_503_SERVICE_UNAVAILABLE]
        data = response.json()

        if response.status_code == status.HTTP_200_OK:
            # If ready, should have these fields
            assert "status" in data
            assert "checks" in data
            assert data["ready"] is True
        else:
            # If not ready (503), response is in detail field
            assert "detail" in data
            detail = data["detail"]
            assert "ready" in detail
            assert "services" in detail
            assert detail["ready"] is False

    @patch('app.api.v1.endpoints.ai_test.ai_service')
    def test_ai_health_endpoint_success(self, mock_ai_service, client):
        """Test AI health check endpoint success"""
        # Configure async mock for health_check
        mock_ai_service.health_check = AsyncMock(return_value={
            "status": "healthy",
            "provider": "mistral",
            "model": "mistral:7b"
        })
        
        response = client.get("/api/v1/ai-test/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert "provider" in data or "model" in data

    @patch('app.api.v1.endpoints.ai_test.ai_service')
    def test_ai_health_endpoint_failure(self, mock_ai_service, client):
        """Test AI health check endpoint failure"""
        # Configure async mock for health_check with exception
        mock_ai_service.health_check = AsyncMock(side_effect=Exception("AI service unavailable"))
        
        response = client.get("/api/v1/ai-test/health")

        # AI health endpoint returns 500 on error, not 503
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        data = response.json()
        assert "error" in data

    @patch('app.api.v1.endpoints.ai_test.ai_service')
    def test_generate_tqr_success(self, mock_ai_service, client, valid_tqr_request):
        """Test successful TQR generation"""
        expected_response = {
            "tarea": "Optimización del sistema de base de datos de la empresa",
            "cuantificacion": "Reducción del 40% en los tiempos de consulta",
            "resultado": "Mejora significativa en el rendimiento y eficiencia del sistema"
        }

        # Configure async mock for generate_tqr_achievement
        mock_ai_service.generate_tqr_achievement = AsyncMock(return_value=expected_response)
        
        response = client.post(
            "/api/v1/ai-test/generate-tqr",
            json=valid_tqr_request
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert "tarea" in data
        assert "cuantificacion" in data
        assert "resultado" in data
        assert "original_description" in data
        assert data["original_description"] == valid_tqr_request["achievement_description"]
        
        # Verify AI service was called with correct parameters
        mock_ai_service.generate_tqr_achievement.assert_called_once()

    def test_generate_tqr_invalid_requests(self, client, invalid_tqr_requests):
        """Test TQR generation with invalid requests"""
        for invalid_request in invalid_tqr_requests:
            response = client.post(
                "/api/v1/ai-test/generate-tqr",
                json=invalid_request
            )
            
            # Should return validation error
            assert response.status_code in [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_422_UNPROCESSABLE_ENTITY
            ]

    def test_generate_tqr_missing_content_type(self, client, valid_tqr_request):
        """Test TQR generation without proper content type"""
        response = client.post(
            "/api/v1/ai-test/generate-tqr",
            data=json.dumps(valid_tqr_request)  # Send as string instead of JSON
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @patch('app.api.v1.endpoints.ai_test.ai_service')
    def test_generate_tqr_ai_service_error(self, mock_ai_service, client, valid_tqr_request):
        """Test TQR generation when AI service fails"""
        # Configure async mock with exception
        mock_ai_service.generate_tqr_achievement = AsyncMock(side_effect=Exception("AI service error"))
        
        response = client.post(
            "/api/v1/ai-test/generate-tqr",
            json=valid_tqr_request
        )
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        data = response.json()
        assert "detail" in data
        assert "TQR generation failed" in data["detail"]

    @patch('app.api.v1.endpoints.ai_test.ai_service')
    def test_generate_tqr_timeout(self, mock_ai_service, client, valid_tqr_request):
        """Test TQR generation timeout"""
        import asyncio
        # Configure async mock with timeout
        mock_ai_service.generate_tqr_achievement = AsyncMock(side_effect=asyncio.TimeoutError("Request timed out"))
        
        response = client.post(
            "/api/v1/ai-test/generate-tqr",
            json=valid_tqr_request
        )
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        data = response.json()
        assert "detail" in data

    def test_generate_tqr_request_validation(self, client):
        """Test request validation for TQR generation"""
        # Test with invalid data types
        invalid_requests = [
            {"name": 123, "position": "Dev", "experience_years": 5, "achievement_description": "Test"},  # Invalid name type
            {"name": "Test", "position": 123, "experience_years": 5, "achievement_description": "Test"},  # Invalid position type
            {"name": "Test", "position": "Dev", "experience_years": "five", "achievement_description": "Test"},  # Invalid years type
            {"name": "Test", "position": "Dev", "experience_years": 5, "achievement_description": 123},  # Invalid description type
        ]
        
        for invalid_request in invalid_requests:
            response = client.post(
                "/api/v1/ai-test/generate-tqr",
                json=invalid_request
            )
            
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @patch('app.api.v1.endpoints.ai_test.ai_service')
    def test_generate_tqr_response_headers(self, mock_ai_service, client, valid_tqr_request):
        """Test response headers for TQR generation"""
        # Configure async mock
        mock_ai_service.generate_tqr_achievement = AsyncMock(return_value={
            "tarea": "Test task",
            "cuantificacion": "Test quantification",
            "resultado": "Test result"
        })
        
        response = client.post(
            "/api/v1/ai-test/generate-tqr",
            json=valid_tqr_request
        )
        
        assert response.status_code == status.HTTP_200_OK
        
        # Check security headers
        assert "x-content-type-options" in response.headers
        assert "x-frame-options" in response.headers
        assert "content-security-policy" in response.headers

    @patch('app.api.v1.endpoints.ai_test.ai_service')
    def test_generate_tqr_rate_limiting(self, mock_ai_service, client, valid_tqr_request):
        """Test rate limiting for TQR generation"""
        mock_ai_service.generate_tqr_achievement = AsyncMock(return_value={
            "tarea": "Test task",
            "cuantificacion": "Test quantification",
            "resultado": "Test result",
            "original_description": "Test description"
        })
        
        # Make multiple requests rapidly
        responses = []
        for _ in range(5):
            response = client.post(
                "/api/v1/ai-test/generate-tqr",
                json=valid_tqr_request
            )
            responses.append(response)
        
        # Check if rate limiting headers are present
        for response in responses:
            if "x-ratelimit-limit" in response.headers:
                assert "x-ratelimit-remaining" in response.headers
                assert "x-ratelimit-reset" in response.headers

    def test_cors_headers(self, client):
        """Test CORS headers"""
        response = client.options("/api/v1/ai-test/health")
        
        # Should have CORS headers configured
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_405_METHOD_NOT_ALLOWED]

    @patch('app.api.v1.endpoints.ai_test.ai_service')
    def test_generate_tqr_large_input(self, mock_ai_service, client):
        """Test TQR generation with large input"""
        large_description = "A" * 10000  # Very large description
        
        large_request = {
            "name": "Test User",
            "position": "Developer",
            "experience_years": 5,
            "achievement_description": large_description
        }
        
        mock_ai_service.generate_tqr_achievement = AsyncMock(return_value={
            "tarea": "Test task",
            "cuantificacion": "Test quantification",
            "resultado": "Test result",
            "original_description": large_description
        })
        
        response = client.post(
            "/api/v1/ai-test/generate-tqr",
            json=large_request
        )
        
        # Should handle large inputs appropriately
        assert response.status_code in [
            status.HTTP_200_OK,
            status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            status.HTTP_422_UNPROCESSABLE_ENTITY
        ]

    def test_api_documentation(self, client):
        """Test API documentation endpoints"""
        # Test OpenAPI schema
        response = client.get("/openapi.json")
        assert response.status_code == status.HTTP_200_OK
        
        schema = response.json()
        assert "paths" in schema
        assert "/api/v1/ai-test/generate-tqr" in schema["paths"]
        assert "/api/v1/ai-test/health" in schema["paths"]

    def test_docs_endpoint(self, client):
        """Test Swagger docs endpoint"""
        response = client.get("/docs")
        assert response.status_code == status.HTTP_200_OK
        assert "text/html" in response.headers["content-type"]

    @patch('app.api.v1.endpoints.ai_test.ai_service')
    def test_generate_tqr_special_characters(self, mock_ai_service, client):
        """Test TQR generation with special characters"""
        special_request = {
            "name": "José María Ñoño",
            "position": "Développeur Senior",
            "experience_years": 5,
            "achievement_description": "Optimicé el sistema con caracteres especiales: áéíóú, ñ, ç, ü, etc."
        }
        
        mock_ai_service.generate_tqr_achievement = AsyncMock(return_value={
            "tarea": "Tarea con caracteres especiales",
            "cuantificacion": "Cuantificación con acentos",
            "resultado": "Resultado con ñ y ü",
            "original_description": special_request["achievement_description"]
        })
        
        response = client.post(
            "/api/v1/ai-test/generate-tqr",
            json=special_request
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Should handle Unicode characters correctly
        assert "tarea" in data
        assert "cuantificacion" in data
        assert "resultado" in data
