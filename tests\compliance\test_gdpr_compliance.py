"""
GDPR Compliance validation tests
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta

from app.core.pii_detector import pii_detector
from app.services.data_catalog import data_catalog
from app.services.data_quality import data_quality_service
from app.core.validators import data_validator


class TestGDPRCompliance:
    """Test GDPR compliance requirements."""
    
    def test_lawful_basis_for_processing(self):
        """Test Article 6 - Lawful basis for processing."""
        # Test that processing has lawful basis
        cv_data = {
            'personal': {
                'full_name': '<PERSON>',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            'consent': {
                'data_processing': True,
                'purpose': 'CV generation and improvement',
                'timestamp': datetime.utcnow().isoformat(),
                'lawful_basis': 'consent'  # Article 6(1)(a)
            }
        }
        
        # Validation should check for lawful basis
        validation_result = data_validator.validate_cv_data(cv_data)
        assert validation_result is not None
        
        # Should have consent tracking
        assert 'consent' in cv_data
        assert cv_data['consent']['data_processing'] == True
    
    def test_data_minimization_principle(self):
        """Test Article 5(1)(c) - Data minimization."""
        # Test that only necessary data is collected
        minimal_cv_data = {
            'personal': {
                'full_name': '<PERSON> Doe',
                'email': '<EMAIL>'  # Only necessary for contact
            },
            'experience': [
                {
                    'job_title': 'Software Engineer',
                    'company': 'Tech Corp'
                    # No unnecessary personal details
                }
            ]
        }
        
        excessive_cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'ssn': '***********',  # Not necessary for CV
                'credit_card': '4532-1234-5678-9012',  # Not necessary
                'mother_maiden_name': 'Smith',  # Not necessary
                'blood_type': 'O+',  # Not necessary
            }
        }
        
        # PII detector should flag excessive data collection
        minimal_text = str(minimal_cv_data)
        excessive_text = str(excessive_cv_data)
        
        minimal_pii = pii_detector.detect_pii(minimal_text)
        excessive_pii = pii_detector.detect_pii(excessive_text)
        
        # Excessive data should have higher risk level
        assert excessive_pii.risk_level in ['medium', 'high']
        assert len(excessive_pii.pii_matches) > len(minimal_pii.pii_matches)
    
    def test_purpose_limitation(self):
        """Test Article 5(1)(b) - Purpose limitation."""
        # Test that data is used only for specified purposes
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>'
            },
            'consent': {
                'purposes': ['cv_generation', 'profile_improvement'],
                'not_for_marketing': True,
                'not_for_third_party_sharing': True
            }
        }
        
        # Should validate purpose limitation
        validation_result = data_validator.validate_cv_data(cv_data)
        assert validation_result is not None
        
        # Should have clear purpose specification
        assert 'consent' in cv_data
        assert 'purposes' in cv_data['consent']
    
    def test_accuracy_principle(self):
        """Test Article 5(1)(d) - Accuracy."""
        # Test data accuracy requirements
        accurate_cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '(*************'
            }
        }
        
        inaccurate_cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': 'invalid-email',  # Invalid format
                'phone': '123'  # Invalid format
            }
        }
        
        # Validation should detect accuracy issues
        accurate_result = data_validator.validate_cv_data(accurate_cv_data)
        inaccurate_result = data_validator.validate_cv_data(inaccurate_cv_data)
        
        # Accurate data should have higher quality score
        assert accurate_result.score > inaccurate_result.score
        
        # Inaccurate data should have validation errors
        assert len(inaccurate_result.errors) > len(accurate_result.errors)
    
    def test_storage_limitation(self):
        """Test Article 5(1)(e) - Storage limitation."""
        # Test data retention limits
        current_date = datetime.utcnow()
        
        # Test recent data (should be retained)
        recent_cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>'
            },
            'metadata': {
                'created_at': current_date.isoformat(),
                'last_accessed': current_date.isoformat()
            }
        }
        
        # Test old data (should be flagged for deletion)
        old_date = current_date - timedelta(days=1100)  # ~3 years old
        old_cv_data = {
            'personal': {
                'full_name': 'Jane Smith',
                'email': '<EMAIL>'
            },
            'metadata': {
                'created_at': old_date.isoformat(),
                'last_accessed': old_date.isoformat()
            }
        }
        
        # Quality assessment should consider data age
        recent_quality = data_quality_service.assess_cv_quality(recent_cv_data)
        old_quality = data_quality_service.assess_cv_quality(old_cv_data)
        
        # Recent data should have better timeliness score
        recent_timeliness = next(m for m in recent_quality.metrics if m.name == 'timeliness')
        old_timeliness = next(m for m in old_quality.metrics if m.name == 'timeliness')
        
        assert recent_timeliness.value >= old_timeliness.value
    
    def test_right_to_access(self):
        """Test Article 15 - Right of access."""
        # Test data subject's right to access their data
        cv_id = "test_cv_access"
        user_id = "test_user_access"
        
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>'
            },
            'experience': [
                {
                    'job_title': 'Software Engineer',
                    'company': 'Tech Corp'
                }
            ]
        }
        
        # Register data in catalog
        assets = data_catalog.register_cv_assets(cv_id, user_id, cv_data)
        
        # Should be able to search and retrieve user's data
        search_result = data_catalog.search_assets(
            filters={'owner': user_id}
        )
        
        assert search_result.total_count > 0
        assert any(asset.owner == user_id for asset in search_result.assets)
        
        # Should provide complete data export capability
        user_assets = [asset for asset in search_result.assets if asset.owner == user_id]
        assert len(user_assets) > 0
    
    def test_right_to_rectification(self):
        """Test Article 16 - Right to rectification."""
        # Test data subject's right to correct inaccurate data
        cv_id = "test_cv_rectification"
        user_id = "test_user_rectification"
        
        # Original data with error
        original_cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>'  # Incorrect email
            }
        }
        
        # Register original data
        assets = data_catalog.register_cv_assets(cv_id, user_id, original_cv_data)
        original_asset = assets[0]
        
        # Correct the data
        corrected_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>'  # Corrected email
            }
        }
        
        # Update asset with corrected data
        updated_asset = data_catalog.update_asset(
            original_asset.id,
            {
                'metadata': {
                    **original_asset.metadata,
                    'corrected_data': corrected_data,
                    'rectification_date': datetime.utcnow().isoformat(),
                    'rectification_reason': 'User requested email correction'
                }
            }
        )
        
        # Should track rectification
        assert 'rectification_date' in updated_asset.metadata
        assert 'rectification_reason' in updated_asset.metadata
    
    def test_right_to_erasure(self):
        """Test Article 17 - Right to erasure (right to be forgotten)."""
        # Test data subject's right to have data deleted
        sensitive_text = "John Doe, SSN: ***********, Email: <EMAIL>"
        
        # Test complete erasure
        pii_result = pii_detector.detect_pii(sensitive_text, sensitivity_level='high')
        
        # Use remove method for complete erasure
        erased_text, method = pii_detector._sanitize_text(
            sensitive_text,
            pii_result.pii_matches,
            method='remove'
        )
        
        # Should completely remove PII
        assert '<EMAIL>' not in erased_text
        assert '***********' not in erased_text
        assert method == 'remove'
        
        # Should support cascading deletion
        cv_id = "test_cv_erasure"
        user_id = "test_user_erasure"
        
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>'
            }
        }
        
        # Register data
        assets = data_catalog.register_cv_assets(cv_id, user_id, cv_data)
        
        # Simulate erasure request
        for asset in assets:
            # Mark for deletion
            data_catalog.update_asset(
                asset.id,
                {
                    'metadata': {
                        **asset.metadata,
                        'deletion_requested': True,
                        'deletion_request_date': datetime.utcnow().isoformat(),
                        'deletion_reason': 'Right to be forgotten request'
                    }
                }
            )
        
        # Verify deletion marking
        updated_assets = [data_catalog.get_asset(asset.id) for asset in assets]
        for asset in updated_assets:
            assert asset.metadata.get('deletion_requested') == True
    
    def test_right_to_data_portability(self):
        """Test Article 20 - Right to data portability."""
        # Test data subject's right to receive data in portable format
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '(*************'
            },
            'experience': [
                {
                    'job_title': 'Software Engineer',
                    'company': 'Tech Corp',
                    'start_date': '2020',
                    'end_date': '2023'
                }
            ],
            'skills': [
                {'name': 'Python'},
                {'name': 'JavaScript'}
            ]
        }
        
        # Test JSON export (structured format)
        import json
        try:
            json_export = json.dumps(cv_data, indent=2)
            assert json_export is not None
            assert len(json_export) > 0
            
            # Should be valid JSON
            parsed_data = json.loads(json_export)
            assert parsed_data == cv_data
        except Exception:
            pytest.fail("Should be able to export data in JSON format")
        
        # Test CSV export capability (for tabular data)
        import csv
        import io
        
        try:
            # Export experience data as CSV
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=['job_title', 'company', 'start_date', 'end_date'])
            writer.writeheader()
            writer.writerows(cv_data['experience'])
            
            csv_content = output.getvalue()
            assert len(csv_content) > 0
            assert 'Software Engineer' in csv_content
        except Exception:
            pytest.fail("Should be able to export data in CSV format")
    
    def test_consent_management(self):
        """Test consent management requirements."""
        # Test granular consent
        consent_data = {
            'data_processing': True,
            'cv_generation': True,
            'profile_improvement': True,
            'analytics': False,  # User declined analytics
            'marketing': False,  # User declined marketing
            'third_party_sharing': False,  # User declined sharing
            'consent_date': datetime.utcnow().isoformat(),
            'consent_method': 'explicit_checkbox',
            'consent_version': '1.0'
        }
        
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>'
            },
            'consent': consent_data
        }
        
        # Validation should check consent
        validation_result = data_validator.validate_cv_data(cv_data)
        assert validation_result is not None
        
        # Should respect consent limitations
        assert cv_data['consent']['data_processing'] == True
        assert cv_data['consent']['marketing'] == False
        assert cv_data['consent']['third_party_sharing'] == False
    
    def test_data_protection_by_design(self):
        """Test Article 25 - Data protection by design and by default."""
        # Test that privacy is built into the system
        
        # 1. Default to minimal data collection
        minimal_cv_data = {
            'personal': {
                'full_name': 'John Doe'
                # Only essential data by default
            }
        }
        
        validation_result = data_validator.validate_cv_data(minimal_cv_data)
        # Should work with minimal data
        assert validation_result is not None
        
        # 2. Automatic PII detection and protection
        text_with_pii = "My name is John Doe and my SSN is ***********"
        pii_result = pii_detector.detect_pii(text_with_pii)
        
        # Should automatically detect and protect PII
        assert len(pii_result.pii_matches) > 0
        assert pii_result.sanitized_text != pii_result.original_text
        
        # 3. Quality assessment includes privacy considerations
        quality_report = data_quality_service.assess_cv_quality(minimal_cv_data)
        
        # Should assess data quality including privacy aspects
        assert quality_report.overall_score >= 0
        assert len(quality_report.metrics) > 0
    
    def test_records_of_processing(self):
        """Test Article 30 - Records of processing activities."""
        # Test that processing activities are documented
        cv_id = "test_cv_records"
        user_id = "test_user_records"
        
        cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>'
            }
        }
        
        # Register with processing records
        assets = data_catalog.register_cv_assets(cv_id, user_id, cv_data)
        
        # Should have processing metadata
        cv_asset = assets[0]
        assert cv_asset.metadata is not None
        assert 'cv_id' in cv_asset.metadata
        assert 'user_id' in cv_asset.metadata
        
        # Should track data categories
        assert cv_asset.classification == 'confidential'
        assert cv_asset.domain == 'hr_documents'
        
        # Should have data owner information
        assert cv_asset.owner == user_id
    
    def test_data_breach_detection(self):
        """Test data breach detection capabilities."""
        # Test detection of potential data breaches
        
        # 1. Unauthorized access attempt
        unauthorized_cv_data = {
            'personal': {
                'full_name': 'John Doe',
                'email': '<EMAIL>'
            },
            'access_log': {
                'unauthorized_access_attempt': True,
                'suspicious_ip': '*************',
                'failed_authentication': True
            }
        }
        
        # Should detect suspicious activity
        validation_result = data_validator.validate_cv_data(unauthorized_cv_data)
        assert validation_result is not None
        
        # 2. Data integrity issues
        corrupted_data = {
            'personal': {
                'full_name': None,  # Corrupted data
                'email': 'corrupted@#$%'  # Invalid format
            }
        }
        
        # Should detect data corruption
        validation_result = data_validator.validate_cv_data(corrupted_data)
        assert len(validation_result.errors) > 0
        
        # Quality assessment should flag integrity issues
        quality_report = data_quality_service.assess_cv_quality(corrupted_data)
        integrity_metric = next(m for m in quality_report.metrics if m.name == 'integrity')
        assert integrity_metric.value < 1.0  # Should detect integrity issues
