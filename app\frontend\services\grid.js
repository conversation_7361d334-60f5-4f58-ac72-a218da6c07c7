// Grid service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class GridService {
    constructor() {
        this.grids = new Map();
        this.subscribers = new Set();
        this.draggedItem = null;
        this.placeholder = null;
        this.initialize();
    }

    /**
     * Initialize the grid service
     */
    initialize() {
        document.addEventListener('dragstart', this.handleDragStart.bind(this));
        document.addEventListener('dragend', this.handleDragEnd.bind(this));
        document.addEventListener('dragover', this.handleDragOver.bind(this));
        document.addEventListener('drop', this.handleDrop.bind(this));
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    /**
     * Subscribe to grid events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of grid events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in grid subscriber:', error);
            }
        });
    }

    /**
     * Register a grid container
     * @param {string} id - The container ID
     * @param {Object} options - The grid options
     */
    registerGrid(id, options = {}) {
        const container = document.getElementById(id);
        if (!container) {
            console.error(`Container ${id} not found`);
            return;
        }

        // Set grid styles
        container.style.display = 'grid';
        container.style.gridTemplateColumns = `repeat(${options.columns || 3}, 1fr)`;
        container.style.gap = options.gap || '1rem';

        // Make items draggable
        const items = container.querySelectorAll(options.itemSelector || '.grid-item');
        items.forEach(item => {
            item.draggable = true;
            item.classList.add('grid-item');
        });

        this.grids.set(id, {
            ...options,
            container,
            items: Array.from(items),
        });

        // Initial layout
        this.layoutGrid(id);
    }

    /**
     * Create a placeholder element
     * @param {HTMLElement} item - The dragged item
     * @returns {HTMLElement} The placeholder element
     */
    createPlaceholder(item) {
        const placeholder = document.createElement('div');
        placeholder.className = 'grid-placeholder';
        placeholder.style.height = `${item.offsetHeight}px`;
        placeholder.style.width = `${item.offsetWidth}px`;
        return placeholder;
    }

    /**
     * Layout a grid
     * @param {string} id - The grid ID
     */
    layoutGrid(id) {
        return measurePerformance('grid_layout', () => {
            const grid = this.grids.get(id);
            if (!grid) {
                return;
            }

            const { container, items } = grid;
            const columns = grid.columns || 3;
            const gap = grid.gap || '1rem';

            // Update grid styles
            container.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
            container.style.gap = gap;

            // Update item styles
            items.forEach(item => {
                item.style.gridColumn = 'auto';
                item.style.gridRow = 'auto';
            });

            if (grid.onLayout) {
                grid.onLayout({ container, items });
            }

            this.notifySubscribers('layout', { id });
        });
    }

    /**
     * Handle drag start events
     * @param {DragEvent} event - The drag event
     */
    handleDragStart(event) {
        return measurePerformance('grid_dragstart', () => {
            const item = event.target.closest('.grid-item');
            if (!item) {
                return;
            }

            const container = item.parentElement;
            const id = container.id;
            const grid = this.grids.get(id);
            if (!grid) {
                return;
            }

            this.draggedItem = item;
            this.placeholder = this.createPlaceholder(item);

            // Set drag data
            event.dataTransfer.effectAllowed = 'move';
            event.dataTransfer.setData('text/plain', id);

            // Add placeholder
            container.insertBefore(this.placeholder, item);

            if (grid.onDragStart) {
                grid.onDragStart(event, {
                    item,
                    container,
                    placeholder: this.placeholder,
                });
            }

            this.notifySubscribers('dragstart', { id, event });
        });
    }

    /**
     * Handle drag end events
     * @param {DragEvent} event - The drag event
     */
    handleDragEnd(event) {
        return measurePerformance('grid_dragend', () => {
            if (!this.draggedItem) {
                return;
            }

            const container = this.draggedItem.parentElement;
            const id = container.id;
            const grid = this.grids.get(id);
            if (!grid) {
                return;
            }

            // Remove placeholder
            if (this.placeholder && this.placeholder.parentElement) {
                this.placeholder.parentElement.removeChild(this.placeholder);
            }

            if (grid.onDragEnd) {
                grid.onDragEnd(event, {
                    item: this.draggedItem,
                    container,
                });
            }

            this.draggedItem = null;
            this.placeholder = null;

            this.notifySubscribers('dragend', { id, event });
        });
    }

    /**
     * Handle drag over events
     * @param {DragEvent} event - The drag event
     */
    handleDragOver(event) {
        return measurePerformance('grid_dragover', () => {
            event.preventDefault();

            if (!this.draggedItem || !this.placeholder) {
                return;
            }

            const container = this.draggedItem.parentElement;
            const id = container.id;
            const grid = this.grids.get(id);
            if (!grid) {
                return;
            }

            const target = event.target.closest('.grid-item');
            if (!target || target === this.draggedItem) {
                return;
            }

            // Calculate position
            const rect = target.getBoundingClientRect();
            const midpoint = rect.top + rect.height / 2;
            const position = event.clientY < midpoint ? 'before' : 'after';

            // Move placeholder
            if (position === 'before') {
                container.insertBefore(this.placeholder, target);
            } else {
                container.insertBefore(this.placeholder, target.nextSibling);
            }

            if (grid.onDragOver) {
                grid.onDragOver(event, {
                    item: this.draggedItem,
                    target,
                    container,
                    position,
                });
            }

            this.notifySubscribers('dragover', { id, event });
        });
    }

    /**
     * Handle drop events
     * @param {DragEvent} event - The drop event
     */
    handleDrop(event) {
        return measurePerformance('grid_drop', () => {
            event.preventDefault();

            if (!this.draggedItem || !this.placeholder) {
                return;
            }

            const container = this.draggedItem.parentElement;
            const id = container.id;
            const grid = this.grids.get(id);
            if (!grid) {
                return;
            }

            // Move item to placeholder position
            container.insertBefore(this.draggedItem, this.placeholder);

            // Update items array
            const items = Array.from(container.querySelectorAll('.grid-item'));
            grid.items = items;

            // Re-layout grid
            this.layoutGrid(id);

            if (grid.onDrop) {
                grid.onDrop(event, {
                    item: this.draggedItem,
                    container,
                    items,
                });
            }

            this.notifySubscribers('drop', { id, event });
        });
    }

    /**
     * Handle resize events
     * @param {Event} event - The resize event
     */
    handleResize(event) {
        return measurePerformance('grid_resize', () => {
            this.grids.forEach((grid, id) => {
                this.layoutGrid(id);
            });
        });
    }

    /**
     * Get grid data
     * @param {string} id - The grid ID
     * @returns {Object} The grid data
     */
    getGridData(id) {
        return this.grids.get(id);
    }

    /**
     * Update grid data
     * @param {string} id - The grid ID
     * @param {Object} data - The new grid data
     */
    updateGridData(id, data) {
        const grid = this.grids.get(id);
        if (grid) {
            Object.assign(grid, data);
            this.layoutGrid(id);
        }
    }
}

// Create and export a singleton instance
const gridService = new GridService();
export default gridService; 