# ImpactCV Code Owners
# This file defines who is responsible for reviewing changes to specific parts of the codebase
# See: https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

# ============================================================================
# GLOBAL OWNERSHIP
# ============================================================================
# Global fallback - all files not covered by specific rules
* @dasotillop

# ============================================================================
# SECURITY & COMPLIANCE
# ============================================================================
# Security-related workflows and configurations
.github/workflows/security-*.yml @dasotillop
.github/workflows/dependency-*.yml @dasotillop
.github/workflows/ci-cd-*.yml @dasotillop

# Security scanning configurations
.bandit @dasotillop
.semgrep.yml @dasotillop
.semgrepignore @dasotillop
.pre-commit-config.yaml @dasotillop

# Security scripts and tools
scripts/security/ @dasotillop

# Compliance and governance documentation
docs/security/ @dasotillop
docs/compliance/ @dasotillop
docs/data_governance/ @dasotillop
prompts/security_*.md @dasotillop
prompts/gdpr_*.md @dasotillop
prompts/owasp_*.md @dasotillop

# ============================================================================
# INFRASTRUCTURE & DEPLOYMENT
# ============================================================================
# Infrastructure as Code
terraform/ @dasotillop
*.tf @dasotillop
*.tfvars @dasotillop
*.tfvars.example @dasotillop

# Container configurations
Dockerfile* @dasotillop
docker-compose*.yml @dasotillop
.dockerignore @dasotillop

# Kubernetes configurations
k8s/ @dasotillop
*.yaml @dasotillop
*.yml @dasotillop

# CI/CD and deployment
.github/workflows/ @dasotillop
.github/actions/ @dasotillop

# ============================================================================
# CORE APPLICATION
# ============================================================================
# Main application code
app/ @dasotillop
app/main.py @dasotillop
app/core/ @dasotillop
app/api/ @dasotillop

# Authentication and security modules
app/auth/ @dasotillop
app/security/ @dasotillop
app/middleware/ @dasotillop

# AI/ML and RAG pipeline
app/ai/ @dasotillop
app/rag/ @dasotillop
app/services/ai/ @dasotillop
app/services/rag/ @dasotillop

# Database models and migrations
app/models/ @dasotillop
app/database/ @dasotillop
alembic/ @dasotillop
alembic.ini @dasotillop

# ============================================================================
# DEPENDENCIES & CONFIGURATION
# ============================================================================
# Python dependencies
requirements*.txt @dasotillop
pyproject.toml @dasotillop
setup.py @dasotillop
setup.cfg @dasotillop

# Package management
Pipfile @dasotillop
Pipfile.lock @dasotillop
poetry.lock @dasotillop

# Environment and configuration
.env.template @dasotillop
.env.example @dasotillop
config/ @dasotillop

# Dependency management
.github/dependabot.yml @dasotillop

# ============================================================================
# TESTING & QUALITY ASSURANCE
# ============================================================================
# Test configurations and scripts
tests/ @dasotillop
pytest.ini @dasotillop
.coveragerc @dasotillop
tox.ini @dasotillop

# Code quality configurations
.flake8 @dasotillop
.mypy.ini @dasotillop
.isort.cfg @dasotillop
pyproject.toml @dasotillop

# ============================================================================
# DOCUMENTATION
# ============================================================================
# Core documentation
README.md @dasotillop
CONTRIBUTING.md @dasotillop
CHANGELOG.md @dasotillop
LICENSE @dasotillop

# Development documentation
docs/ @dasotillop
docs/development/ @dasotillop
docs/architecture/ @dasotillop
docs/api/ @dasotillop

# Project planning and prompts
prompts/ @dasotillop
prompts/enterprise_*.md @dasotillop

# ============================================================================
# GITHUB TEMPLATES & CONFIGURATION
# ============================================================================
# Issue and PR templates
.github/ISSUE_TEMPLATE/ @dasotillop
.github/PULL_REQUEST_TEMPLATE.md @dasotillop

# GitHub configuration
.github/CODEOWNERS @dasotillop
.github/SECURITY.md @dasotillop
.github/FUNDING.yml @dasotillop

# ============================================================================
# MONITORING & OBSERVABILITY
# ============================================================================
# Monitoring configurations
config/prometheus/ @dasotillop
config/grafana/ @dasotillop
config/elasticsearch/ @dasotillop
config/kibana/ @dasotillop

# Logging configurations
config/logging/ @dasotillop
config/logstash/ @dasotillop

# ============================================================================
# SCRIPTS & AUTOMATION
# ============================================================================
# Utility scripts
scripts/ @dasotillop
scripts/setup/ @dasotillop
scripts/deployment/ @dasotillop
scripts/monitoring/ @dasotillop

# Database scripts
scripts/db/ @dasotillop

# ============================================================================
# SPECIAL FILES
# ============================================================================
# Git configuration
.gitignore @dasotillop
.gitattributes @dasotillop

# Editor configurations
.vscode/ @dasotillop
.idea/ @dasotillop
.editorconfig @dasotillop

# Package metadata
MANIFEST.in @dasotillop
*.egg-info/ @dasotillop

# ============================================================================
# SENSITIVE CONFIGURATIONS (REQUIRE EXTRA REVIEW)
# ============================================================================
# These files require additional security review due to their sensitive nature

# Authentication configurations
app/core/auth.py @dasotillop
app/core/security.py @dasotillop

# Database connection configurations
app/core/database.py @dasotillop
app/core/config.py @dasotillop

# API key and secrets management
app/core/secrets.py @dasotillop
app/services/openai/ @dasotillop

# Production configurations
config/production/ @dasotillop
terraform/environments/prod/ @dasotillop

# ============================================================================
# NOTES FOR CODE OWNERS
# ============================================================================
# 
# Code owners are automatically requested for review when:
# 1. A pull request is opened that modifies files they own
# 2. Changes are made to files matching their patterns
# 
# Responsibilities:
# - Review code for security, quality, and architectural consistency
# - Ensure changes follow established patterns and conventions
# - Validate that security and compliance requirements are met
# - Approve or request changes before merging
# 
# Best Practices:
# - Review within 24-48 hours of assignment
# - Provide constructive feedback and suggestions
# - Test changes locally when possible
# - Ensure documentation is updated when needed
# - Verify that tests are adequate and passing
# 
# Security Review Requirements:
# - All security-related changes require thorough review
# - Validate input sanitization and output encoding
# - Check for proper authentication and authorization
# - Ensure secrets are not hardcoded
# - Verify compliance with OWASP guidelines
# 
# Contact: <EMAIL>
