// API service for handling backend interactions
import config from '../config.js';
import { handleApiError, measurePerformance } from '../utils.js';

class ApiService {
    constructor() {
        this.baseUrl = config.api.baseUrl;
        this.timeout = config.api.timeout;
        this.retryAttempts = config.api.retryAttempts;
        this.retryDelay = config.api.retryDelay;
    }

    /**
     * Make an API request with retry logic
     * @param {string} endpoint - The API endpoint
     * @param {Object} options - Request options
     * @returns {Promise<any>} The API response
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        let lastError;

        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const response = await measurePerformance('api_request', async () => {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

                    const response = await fetch(url, {
                        ...options,
                        signal: controller.signal,
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers,
                        },
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response;
                });

                const data = await response.json();
                return data;
            } catch (error) {
                lastError = error;
                
                if (error.name === 'AbortError') {
                    throw new Error('Request timeout');
                }

                if (attempt < this.retryAttempts) {
                    await new Promise(resolve => setTimeout(resolve, this.retryDelay));
                }
            }
        }

        throw lastError;
    }

    /**
     * Check the health of the API
     * @returns {Promise<Object>} Health check response
     */
    async checkHealth() {
        return this.request('/health');
    }

    /**
     * Generate TQR achievement
     * @param {Object} data - The achievement data
     * @returns {Promise<Object>} Generated achievement
     */
    async generateTqrAchievement(data) {
        return this.request('/ai-test/generate-tqr', {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    /**
     * Test AI completion
     * @param {string} prompt - The prompt to complete
     * @returns {Promise<Object>} Completion response
     */
    async testCompletion(prompt) {
        return this.request('/ai-test/completion', {
            method: 'POST',
            body: JSON.stringify({ prompt }),
        });
    }

    /**
     * Test JSON completion
     * @param {string} prompt - The prompt to complete
     * @returns {Promise<Object>} JSON completion response
     */
    async testJsonCompletion(prompt) {
        return this.request('/ai-test/json-completion', {
            method: 'POST',
            body: JSON.stringify({ prompt }),
        });
    }
}

// Create and export a singleton instance
const apiService = new ApiService();
export default apiService; 