"""
Audit and Security Models
Comprehensive audit logging and security event tracking
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON>olean, Column, DateTime, ForeignKey, Integer, String, Text, JSON
from sqlalchemy.orm import relationship

from app.models.base import Base


class AuditLog(Base):
    """
    Comprehensive audit log for compliance and security.
    
    Tracks all significant system events for:
    - GDPR compliance
    - Security monitoring
    - Business analytics
    - Regulatory compliance
    """
    
    # Event identification
    event_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Type of event (e.g., 'user_login', 'data_access', 'cv_generated')"
    )
    
    event_category = Column(
        String(30),
        nullable=False,
        index=True,
        doc="Event category: authentication, authorization, data, system, business"
    )
    
    event_action = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Specific action performed (CREATE, READ, UPDATE, DELETE, LOGIN, etc.)"
    )
    
    # Actor information
    user_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("user.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User who performed the action (null for system events)"
    )
    
    session_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Session identifier"
    )
    
    # Target information
    resource_type = Column(
        String(50),
        nullable=True,
        index=True,
        doc="Type of resource affected (user, cv, document, etc.)"
    )
    
    resource_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        nullable=True,
        index=True,
        doc="ID of the affected resource"
    )
    
    resource_name = Column(
        String(200),
        nullable=True,
        doc="Name or identifier of the affected resource"
    )
    
    # Event details
    event_description = Column(
        Text,
        nullable=False,
        doc="Human-readable description of the event"
    )
    
    event_data = Column(
        JSON,
        nullable=True,
        doc="Additional event data as JSON (PII-sanitized)"
    )

    # Request context
    ip_address = Column(
        String(45),  # IPv4/IPv6 address as string for SQLite compatibility
        nullable=True,
        index=True,
        doc="IP address of the request"
    )
    
    user_agent = Column(
        Text,
        nullable=True,
        doc="User agent string"
    )
    
    request_id = Column(
        String(100),
        nullable=True,
        index=True,
        doc="Request correlation ID"
    )
    
    # Event outcome
    success = Column(
        Boolean,
        nullable=False,
        index=True,
        doc="Whether the event was successful"
    )
    
    error_code = Column(
        String(50),
        nullable=True,
        doc="Error code if event failed"
    )
    
    error_message = Column(
        Text,
        nullable=True,
        doc="Error message if event failed"
    )
    
    # Timing information
    event_timestamp = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="When the event occurred"
    )
    
    duration_ms = Column(
        Integer,
        nullable=True,
        doc="Event duration in milliseconds"
    )
    
    # Compliance and retention
    retention_date = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="When this audit record should be deleted"
    )
    
    compliance_tags = Column(
        JSON,
        nullable=True,
        doc="Compliance-related tags (GDPR, SOX, etc.)"
    )
    
    # Risk assessment
    risk_level = Column(
        String(20),
        default="low",
        nullable=False,
        index=True,
        doc="Risk level: low, medium, high, critical"
    )
    
    sensitivity_level = Column(
        String(20),
        default="public",
        nullable=False,
        index=True,
        doc="Data sensitivity: public, internal, confidential, restricted"
    )
    
    # Relationships
    user = relationship("User", backref="audit_logs")
    
    def __repr__(self) -> str:
        """String representation of the audit log."""
        return f"<AuditLog(type={self.event_type}, action={self.event_action}, user_id={self.user_id})>"
    
    @classmethod
    def log_event(
        cls,
        event_type: str,
        event_action: str,
        event_description: str,
        user_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        success: bool = True,
        event_data: Optional[dict] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_id: Optional[str] = None,
        risk_level: str = "low",
        sensitivity_level: str = "public"
    ) -> "AuditLog":
        """
        Create a new audit log entry.
        
        Args:
            event_type: Type of event
            event_action: Action performed
            event_description: Description of the event
            user_id: User who performed the action
            resource_type: Type of resource affected
            resource_id: ID of affected resource
            success: Whether the event was successful
            event_data: Additional event data
            ip_address: Client IP address
            user_agent: Client user agent
            request_id: Request correlation ID
            risk_level: Risk level of the event
            sensitivity_level: Data sensitivity level
            
        Returns:
            AuditLog: Created audit log entry
        """
        # Determine event category
        category_mapping = {
            "login": "authentication",
            "logout": "authentication",
            "password_change": "authentication",
            "access_denied": "authorization",
            "permission_check": "authorization",
            "data_access": "data",
            "data_create": "data",
            "data_update": "data",
            "data_delete": "data",
            "cv_generate": "business",
            "cv_download": "business",
            "system_start": "system",
            "system_error": "system",
        }
        
        event_category = category_mapping.get(event_type, "system")
        
        return cls(
            event_type=event_type,
            event_category=event_category,
            event_action=event_action,
            event_description=event_description,
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            success=success,
            event_data=event_data,
            ip_address=ip_address,
            user_agent=user_agent,
            request_id=request_id,
            risk_level=risk_level,
            sensitivity_level=sensitivity_level,
            event_timestamp=datetime.utcnow()
        )


class SecurityEvent(Base):
    """
    Security-specific event tracking for threat detection.
    
    Tracks security-related events for:
    - Threat detection and response
    - Security monitoring
    - Incident investigation
    - Compliance reporting
    """
    
    # Event identification
    event_type = Column(
        String(50),
        nullable=False,
        index=True,
        doc="Security event type"
    )
    
    severity = Column(
        String(20),
        nullable=False,
        index=True,
        doc="Event severity: info, low, medium, high, critical"
    )
    
    # Threat information
    threat_category = Column(
        String(50),
        nullable=True,
        index=True,
        doc="MITRE ATT&CK category or custom threat category"
    )
    
    attack_vector = Column(
        String(50),
        nullable=True,
        doc="Attack vector used"
    )
    
    # Actor information
    source_ip = Column(
        String(45),  # IPv4/IPv6 address as string for SQLite compatibility
        nullable=True,
        index=True,
        doc="Source IP address"
    )

    user_id = Column(
        String(36),  # UUID as string for SQLite compatibility
        ForeignKey("user.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
        doc="User associated with the event"
    )
    
    session_id = Column(
        String(255),
        nullable=True,
        index=True,
        doc="Session identifier"
    )
    
    # Event details
    event_description = Column(
        Text,
        nullable=False,
        doc="Detailed description of the security event"
    )
    
    indicators = Column(
        JSON,
        nullable=True,
        doc="Indicators of compromise (IoCs)"
    )
    
    # Detection information
    detection_method = Column(
        String(50),
        nullable=True,
        doc="How the event was detected (rule, anomaly, manual, etc.)"
    )
    
    detection_rule = Column(
        String(100),
        nullable=True,
        doc="Detection rule or signature that triggered"
    )
    
    confidence_score = Column(
        Integer,
        nullable=True,
        doc="Confidence score (0-100) for the detection"
    )
    
    # Response information
    response_status = Column(
        String(20),
        default="new",
        nullable=False,
        index=True,
        doc="Response status: new, investigating, contained, resolved, false_positive"
    )
    
    assigned_to = Column(
        String(36),  # UUID as string for SQLite compatibility
        nullable=True,
        doc="Security analyst assigned to investigate"
    )

    response_actions = Column(
        JSON,
        nullable=True,
        doc="Actions taken in response to the event"
    )
    
    # Timeline
    event_timestamp = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="When the security event occurred"
    )
    
    detected_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="When the event was detected"
    )
    
    resolved_at = Column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the event was resolved"
    )
    
    # Additional context
    request_data = Column(
        JSON,
        nullable=True,
        doc="Request data that triggered the event (sanitized)"
    )

    system_context = Column(
        JSON,
        nullable=True,
        doc="System context at the time of the event"
    )
    
    # Relationships
    user = relationship("User", backref="security_events")
    
    def __repr__(self) -> str:
        """String representation of the security event."""
        return f"<SecurityEvent(type={self.event_type}, severity={self.severity})>"
    
    @classmethod
    def create_security_event(
        cls,
        event_type: str,
        severity: str,
        description: str,
        source_ip: Optional[str] = None,
        user_id: Optional[str] = None,
        threat_category: Optional[str] = None,
        detection_method: Optional[str] = None,
        indicators: Optional[dict] = None,
        confidence_score: Optional[int] = None
    ) -> "SecurityEvent":
        """
        Create a new security event.
        
        Args:
            event_type: Type of security event
            severity: Event severity level
            description: Event description
            source_ip: Source IP address
            user_id: Associated user ID
            threat_category: Threat category
            detection_method: How the event was detected
            indicators: Indicators of compromise
            confidence_score: Detection confidence score
            
        Returns:
            SecurityEvent: Created security event
        """
        return cls(
            event_type=event_type,
            severity=severity,
            event_description=description,
            source_ip=source_ip,
            user_id=user_id,
            threat_category=threat_category,
            detection_method=detection_method,
            indicators=indicators,
            confidence_score=confidence_score,
            event_timestamp=datetime.utcnow(),
            detected_at=datetime.utcnow()
        )
    
    def resolve(self, resolution_notes: str = None) -> None:
        """Mark the security event as resolved."""
        self.response_status = "resolved"
        self.resolved_at = datetime.utcnow()
        if resolution_notes:
            if not self.response_actions:
                self.response_actions = {}
            self.response_actions["resolution_notes"] = resolution_notes
    
    def mark_false_positive(self, notes: str = None) -> None:
        """Mark the security event as a false positive."""
        self.response_status = "false_positive"
        self.resolved_at = datetime.utcnow()
        if notes:
            if not self.response_actions:
                self.response_actions = {}
            self.response_actions["false_positive_notes"] = notes
