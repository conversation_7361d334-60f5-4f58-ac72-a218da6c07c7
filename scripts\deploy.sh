#!/bin/bash

# ImpactCV Production Deployment Script
# Enterprise-grade deployment with health checks and rollback capabilities

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${ENVIRONMENT:-production}"
VERSION="${VERSION:-latest}"
REGISTRY="${REGISTRY:-ghcr.io}"
IMAGE_NAME="${IMAGE_NAME:-dasotillop/impactcv}"
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.yml}"
BACKUP_DIR="${BACKUP_DIR:-/opt/impactcv/backups}"
LOG_FILE="${LOG_FILE:-/var/log/impactcv/deploy.log}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[INFO]${NC} $message" ;;
        WARN)  echo -e "${YELLOW}[WARN]${NC} $message" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} $message" ;;
        DEBUG) echo -e "${BLUE}[DEBUG]${NC} $message" ;;
    esac
    
    # Also log to file
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Error handling
error_exit() {
    log ERROR "$1"
    exit 1
}

# Cleanup function
cleanup() {
    log INFO "Cleaning up temporary files..."
    # Add cleanup commands here
}

# Set trap for cleanup
trap cleanup EXIT

# Check prerequisites
check_prerequisites() {
    log INFO "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error_exit "Docker is not installed"
    fi
    
    if ! docker info &> /dev/null; then
        error_exit "Docker daemon is not running"
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error_exit "Docker Compose is not installed"
    fi
    
    # Check if required environment variables are set
    if [[ -z "${POSTGRES_PASSWORD:-}" ]]; then
        error_exit "POSTGRES_PASSWORD environment variable is not set"
    fi
    
    if [[ -z "${REDIS_PASSWORD:-}" ]]; then
        error_exit "REDIS_PASSWORD environment variable is not set"
    fi
    
    if [[ -z "${OPENAI_API_KEY:-}" ]]; then
        error_exit "OPENAI_API_KEY environment variable is not set"
    fi
    
    log INFO "Prerequisites check passed"
}

# Create backup
create_backup() {
    log INFO "Creating backup..."
    
    local backup_timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_path="$BACKUP_DIR/backup_$backup_timestamp"
    
    mkdir -p "$backup_path"
    
    # Backup database
    log INFO "Backing up database..."
    docker-compose exec -T postgresql pg_dump -U impactcv_user impactcv > "$backup_path/database.sql" || {
        log WARN "Database backup failed, continuing..."
    }
    
    # Backup volumes
    log INFO "Backing up volumes..."
    docker run --rm -v impactcv_postgres_data:/data -v "$backup_path":/backup alpine tar czf /backup/postgres_data.tar.gz -C /data . || {
        log WARN "Postgres volume backup failed, continuing..."
    }
    
    docker run --rm -v impactcv_redis_data:/data -v "$backup_path":/backup alpine tar czf /backup/redis_data.tar.gz -C /data . || {
        log WARN "Redis volume backup failed, continuing..."
    }
    
    log INFO "Backup created at $backup_path"
    echo "$backup_path" > "$BACKUP_DIR/latest_backup.txt"
}

# Health check function
health_check() {
    local service=$1
    local url=$2
    local max_attempts=${3:-30}
    local wait_time=${4:-10}
    
    log INFO "Performing health check for $service..."
    
    for ((i=1; i<=max_attempts; i++)); do
        if curl -f -s "$url" > /dev/null; then
            log INFO "$service health check passed"
            return 0
        fi
        
        log DEBUG "Health check attempt $i/$max_attempts failed, waiting ${wait_time}s..."
        sleep "$wait_time"
    done
    
    log ERROR "$service health check failed after $max_attempts attempts"
    return 1
}

# Rollback function
rollback() {
    log WARN "Initiating rollback..."
    
    # Get latest backup
    if [[ -f "$BACKUP_DIR/latest_backup.txt" ]]; then
        local backup_path=$(cat "$BACKUP_DIR/latest_backup.txt")
        log INFO "Rolling back to backup: $backup_path"
        
        # Stop current services
        docker-compose down
        
        # Restore database
        if [[ -f "$backup_path/database.sql" ]]; then
            log INFO "Restoring database..."
            docker-compose up -d postgresql
            sleep 30
            docker-compose exec -T postgresql psql -U impactcv_user -d impactcv < "$backup_path/database.sql"
        fi
        
        # Restore volumes
        if [[ -f "$backup_path/postgres_data.tar.gz" ]]; then
            log INFO "Restoring postgres volume..."
            docker run --rm -v impactcv_postgres_data:/data -v "$backup_path":/backup alpine tar xzf /backup/postgres_data.tar.gz -C /data
        fi
        
        if [[ -f "$backup_path/redis_data.tar.gz" ]]; then
            log INFO "Restoring redis volume..."
            docker run --rm -v impactcv_redis_data:/data -v "$backup_path":/backup alpine tar xzf /backup/redis_data.tar.gz -C /data
        fi
        
        log INFO "Rollback completed"
    else
        log ERROR "No backup found for rollback"
        return 1
    fi
}

# Deploy function
deploy() {
    log INFO "Starting deployment of ImpactCV $VERSION to $ENVIRONMENT..."
    
    cd "$PROJECT_ROOT"
    
    # Pull latest images
    log INFO "Pulling latest images..."
    docker-compose pull
    
    # Start services with rolling update
    log INFO "Starting services..."
    
    # Start infrastructure services first
    docker-compose up -d postgresql redis elasticsearch
    
    # Wait for infrastructure to be ready
    sleep 30
    
    # Start application services
    docker-compose up -d api-gateway auth-service cv-generation-service rag-service data-processing-service
    
    # Wait for application services
    sleep 60
    
    # Start monitoring and proxy services
    docker-compose up -d prometheus grafana nginx
    
    log INFO "All services started"
}

# Run tests
run_tests() {
    log INFO "Running post-deployment tests..."
    
    # Wait for services to be fully ready
    sleep 30
    
    # Run health checks
    health_check "API Gateway" "http://localhost:8000/health" 30 10 || return 1
    health_check "Auth Service" "http://localhost:8001/health" 30 10 || return 1
    health_check "CV Generation Service" "http://localhost:8002/health" 30 10 || return 1
    health_check "RAG Service" "http://localhost:8003/health" 30 10 || return 1
    health_check "Data Processing Service" "http://localhost:8004/health" 30 10 || return 1
    
    # Run smoke tests
    log INFO "Running smoke tests..."
    if command -v python3 &> /dev/null; then
        python3 "$SCRIPT_DIR/run_tests.py" --suite smoke --ci || {
            log ERROR "Smoke tests failed"
            return 1
        }
    else
        log WARN "Python3 not available, skipping smoke tests"
    fi
    
    log INFO "All tests passed"
}

# Main deployment function
main() {
    log INFO "ImpactCV Deployment Script v1.0"
    log INFO "Environment: $ENVIRONMENT"
    log INFO "Version: $VERSION"
    
    # Create log directory
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Check prerequisites
    check_prerequisites
    
    # Create backup
    create_backup
    
    # Deploy
    if deploy; then
        log INFO "Deployment completed successfully"
        
        # Run tests
        if run_tests; then
            log INFO "Post-deployment validation passed"
            log INFO "🎉 Deployment successful!"
        else
            log ERROR "Post-deployment validation failed"
            if [[ "${AUTO_ROLLBACK:-true}" == "true" ]]; then
                rollback
                error_exit "Deployment failed and rolled back"
            else
                error_exit "Deployment validation failed"
            fi
        fi
    else
        log ERROR "Deployment failed"
        if [[ "${AUTO_ROLLBACK:-true}" == "true" ]]; then
            rollback
            error_exit "Deployment failed and rolled back"
        else
            error_exit "Deployment failed"
        fi
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --environment|-e)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --version|-v)
            VERSION="$2"
            shift 2
            ;;
        --no-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --no-rollback)
            AUTO_ROLLBACK=false
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -e, --environment ENV    Deployment environment (default: production)"
            echo "  -v, --version VERSION    Version to deploy (default: latest)"
            echo "  --no-backup             Skip backup creation"
            echo "  --no-rollback           Disable automatic rollback on failure"
            echo "  -h, --help              Show this help message"
            exit 0
            ;;
        *)
            error_exit "Unknown option: $1"
            ;;
    esac
done

# Run main function
main "$@"
