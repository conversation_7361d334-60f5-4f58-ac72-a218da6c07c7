"""
Rate Limiting Middleware
Implements rate limiting to prevent abuse and DoS attacks
"""

import time
from typing import Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from app.core.logging import security_audit_logger
from app.core.security import rate_limit_tracker


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware to prevent abuse.
    
    Features:
    - Per-IP rate limiting
    - Configurable limits and windows
    - Security event logging
    - Graceful error responses
    """
    
    def __init__(
        self,
        app,
        requests_per_minute: int = 60,
        burst_limit: int = 10,
        burst_window: int = 10,
        exclude_paths: Optional[list] = None
    ):
        """
        Initialize rate limiting middleware.
        
        Args:
            app: FastAPI application
            requests_per_minute: Requests allowed per minute
            burst_limit: Burst requests allowed
            burst_window: Burst window in seconds
            exclude_paths: Paths to exclude from rate limiting
        """
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.burst_limit = burst_limit
        self.burst_window = burst_window
        self.exclude_paths = exclude_paths or ["/health", "/metrics", "/docs", "/openapi.json"]
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """Apply rate limiting to requests."""
        
        # Skip rate limiting for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Get client identifier
        client_ip = self._get_client_ip(request)
        
        # Check burst limit (short window)
        burst_allowed = rate_limit_tracker.is_allowed(
            f"burst_{client_ip}",
            self.burst_limit,
            self.burst_window
        )
        
        if not burst_allowed:
            security_audit_logger.log_security_event(
                "rate_limit_exceeded",
                severity="WARNING",
                description="Burst rate limit exceeded",
                client_ip=client_ip,
                path=request.url.path,
                limit_type="burst"
            )
            
            return self._create_rate_limit_response(
                "Burst rate limit exceeded. Please slow down.",
                retry_after=self.burst_window
            )
        
        # Check per-minute limit (longer window)
        minute_allowed = rate_limit_tracker.is_allowed(
            f"minute_{client_ip}",
            self.requests_per_minute,
            60
        )
        
        if not minute_allowed:
            security_audit_logger.log_security_event(
                "rate_limit_exceeded",
                severity="WARNING",
                description="Per-minute rate limit exceeded",
                client_ip=client_ip,
                path=request.url.path,
                limit_type="per_minute"
            )
            
            return self._create_rate_limit_response(
                "Rate limit exceeded. Please try again later.",
                retry_after=60
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(
            max(0, self.requests_per_minute - len(
                rate_limit_tracker.requests.get(f"minute_{client_ip}", [])
            ))
        )
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + 60)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Get client IP address from request.
        
        Args:
            request: FastAPI request object
            
        Returns:
            str: Client IP address
        """
        # Check for forwarded headers (behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        return request.client.host if request.client else "unknown"
    
    def _create_rate_limit_response(self, message: str, retry_after: int) -> JSONResponse:
        """
        Create rate limit exceeded response.
        
        Args:
            message: Error message
            retry_after: Retry after seconds
            
        Returns:
            JSONResponse: Rate limit error response
        """
        return JSONResponse(
            status_code=429,
            content={
                "error": "Rate Limit Exceeded",
                "message": message,
                "retry_after": retry_after,
                "timestamp": time.time()
            },
            headers={
                "Retry-After": str(retry_after),
                "X-RateLimit-Limit": str(self.requests_per_minute),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(int(time.time()) + retry_after)
            }
        )
