# ImpactCV Semgrep Security Configuration
# Advanced Static Application Security Testing (SAST)

# ============================================================================
# SEMGREP CONFIGURATION
# ============================================================================
rules:
  # Include standard security rulesets
  - p/security-audit
  - p/secrets
  - p/owasp-top-ten
  - p/python
  - p/django
  - p/flask
  - p/docker
  - p/kubernetes
  - p/terraform
  - p/ci
  - p/supply-chain

# ============================================================================
# CUSTOM RULES FOR IMPACTCV
# ============================================================================
custom_rules:
  # ========================================================================
  # API SECURITY RULES
  # ========================================================================
  - id: impactcv-hardcoded-api-key
    pattern-either:
      - pattern: |
          $API_KEY = "sk-..."
      - pattern: |
          openai_api_key = "sk-..."
      - pattern: |
          OPENAI_API_KEY = "sk-..."
    message: "Hardcoded OpenAI API key detected"
    languages: [python]
    severity: ERROR
    metadata:
      category: security
      subcategory: [secrets]
      cwe: "CWE-798: Use of Hard-coded Credentials"
      owasp: "A07:2021 - Identification and Authentication Failures"
      impact: HIGH
      likelihood: HIGH
      confidence: HIGH

  - id: impactcv-jwt-secret-hardcoded
    pattern-either:
      - pattern: |
          jwt.encode($PAYLOAD, "...", ...)
      - pattern: |
          JWT_SECRET = "..."
      - pattern: |
          jwt_secret_key = "..."
    message: "Hardcoded JWT secret detected"
    languages: [python]
    severity: ERROR
    metadata:
      category: security
      subcategory: [secrets]
      cwe: "CWE-798: Use of Hard-coded Credentials"

  # ========================================================================
  # DATABASE SECURITY RULES
  # ========================================================================
  - id: impactcv-sql-injection-risk
    pattern-either:
      - pattern: |
          $CONN.execute("... " + $VAR + " ...")
      - pattern: |
          $CONN.execute(f"... {$VAR} ...")
      - pattern: |
          cursor.execute("... " + $VAR + " ...")
    message: "Potential SQL injection vulnerability"
    languages: [python]
    severity: ERROR
    metadata:
      category: security
      subcategory: [injection]
      cwe: "CWE-89: SQL Injection"
      owasp: "A03:2021 - Injection"

  - id: impactcv-database-connection-string
    pattern-either:
      - pattern: |
          DATABASE_URL = "postgresql://..."
      - pattern: |
          db_url = "postgresql://..."
      - pattern: |
          connection_string = "postgresql://..."
    message: "Hardcoded database connection string"
    languages: [python]
    severity: WARNING
    metadata:
      category: security
      subcategory: [secrets]

  # ========================================================================
  # AI/ML SECURITY RULES
  # ========================================================================
  - id: impactcv-prompt-injection-risk
    pattern-either:
      - pattern: |
          openai.Completion.create(prompt=$USER_INPUT, ...)
      - pattern: |
          llm.invoke($USER_INPUT)
      - pattern: |
          chain.run($USER_INPUT)
    message: "Potential prompt injection vulnerability - validate user input"
    languages: [python]
    severity: WARNING
    metadata:
      category: security
      subcategory: [injection]
      cwe: "CWE-94: Code Injection"
      owasp: "A03:2021 - Injection"

  - id: impactcv-unsafe-pickle-usage
    pattern-either:
      - pattern: |
          pickle.loads($VAR)
      - pattern: |
          pickle.load($FILE)
      - pattern: |
          joblib.load($FILE)
    message: "Unsafe deserialization with pickle - use safe alternatives"
    languages: [python]
    severity: ERROR
    metadata:
      category: security
      subcategory: [deserialization]
      cwe: "CWE-502: Deserialization of Untrusted Data"

  # ========================================================================
  # FILE UPLOAD SECURITY RULES
  # ========================================================================
  - id: impactcv-unsafe-file-upload
    pattern-either:
      - pattern: |
          $FILE.save($PATH)
      - pattern: |
          open($USER_FILENAME, ...)
      - pattern: |
          with open($USER_FILENAME, ...) as $F: ...
    message: "Unsafe file upload - validate filename and content"
    languages: [python]
    severity: WARNING
    metadata:
      category: security
      subcategory: [file-upload]
      cwe: "CWE-434: Unrestricted Upload of File with Dangerous Type"

  - id: impactcv-path-traversal-risk
    pattern-either:
      - pattern: |
          os.path.join($BASE, $USER_INPUT)
      - pattern: |
          pathlib.Path($BASE) / $USER_INPUT
      - pattern: |
          $BASE + "/" + $USER_INPUT
    message: "Potential path traversal vulnerability"
    languages: [python]
    severity: WARNING
    metadata:
      category: security
      subcategory: [path-traversal]
      cwe: "CWE-22: Path Traversal"

  # ========================================================================
  # AUTHENTICATION & AUTHORIZATION RULES
  # ========================================================================
  - id: impactcv-missing-auth-check
    pattern: |
      @app.route(...)
      def $FUNC(...):
        ...
    pattern-not: |
      @app.route(...)
      @$AUTH_DECORATOR
      def $FUNC(...):
        ...
    message: "Route missing authentication decorator"
    languages: [python]
    severity: WARNING
    metadata:
      category: security
      subcategory: [authentication]
      cwe: "CWE-306: Missing Authentication for Critical Function"

  - id: impactcv-weak-session-config
    pattern-either:
      - pattern: |
          app.config['SESSION_COOKIE_SECURE'] = False
      - pattern: |
          app.config['SESSION_COOKIE_HTTPONLY'] = False
      - pattern: |
          app.config['SESSION_COOKIE_SAMESITE'] = None
    message: "Weak session cookie configuration"
    languages: [python]
    severity: WARNING
    metadata:
      category: security
      subcategory: [session-management]

  # ========================================================================
  # LOGGING & MONITORING RULES
  # ========================================================================
  - id: impactcv-sensitive-data-logging
    pattern-either:
      - pattern: |
          logger.info(f"... {$PASSWORD} ...")
      - pattern: |
          logger.debug(f"... {$API_KEY} ...")
      - pattern: |
          print(f"... {$SECRET} ...")
    message: "Potential sensitive data in logs"
    languages: [python]
    severity: WARNING
    metadata:
      category: security
      subcategory: [logging]
      cwe: "CWE-532: Information Exposure Through Log Files"

  # ========================================================================
  # DOCKER SECURITY RULES
  # ========================================================================
  - id: impactcv-docker-root-user
    pattern: |
      USER root
    message: "Running as root user in Docker container"
    languages: [dockerfile]
    severity: WARNING
    metadata:
      category: security
      subcategory: [container]
      cwe: "CWE-250: Execution with Unnecessary Privileges"

  - id: impactcv-docker-secrets-in-env
    pattern-either:
      - pattern: |
          ENV $VAR=sk-...
      - pattern: |
          ENV API_KEY=...
      - pattern: |
          ENV SECRET=...
    message: "Secrets in Docker environment variables"
    languages: [dockerfile]
    severity: ERROR
    metadata:
      category: security
      subcategory: [secrets]

  # ========================================================================
  # KUBERNETES SECURITY RULES
  # ========================================================================
  - id: impactcv-k8s-privileged-container
    pattern: |
      privileged: true
    message: "Privileged container detected"
    languages: [yaml]
    severity: ERROR
    metadata:
      category: security
      subcategory: [container]

  - id: impactcv-k8s-root-user
    pattern: |
      runAsUser: 0
    message: "Container running as root user"
    languages: [yaml]
    severity: WARNING
    metadata:
      category: security
      subcategory: [container]

# ============================================================================
# RULE CONFIGURATION
# ============================================================================
exclude:
  # Exclude test files from certain rules
  - "tests/"
  - "test_*.py"
  - "*_test.py"
  - "conftest.py"
  
  # Exclude migration files
  - "migrations/"
  - "alembic/versions/"

# ============================================================================
# SEVERITY CONFIGURATION
# ============================================================================
severity:
  # Fail CI/CD on ERROR level findings
  fail_on: ERROR
  
  # Report all findings but only fail on ERROR
  report_all: true

# ============================================================================
# OUTPUT CONFIGURATION
# ============================================================================
output:
  # Output formats
  formats:
    - json
    - sarif
    - text
  
  # Include metrics in output
  include_metrics: true
  
  # Include rule metadata
  include_rule_metadata: true

# ============================================================================
# PERFORMANCE CONFIGURATION
# ============================================================================
performance:
  # Maximum number of files to scan
  max_files: 10000
  
  # Timeout for individual rules (seconds)
  timeout: 30
  
  # Maximum memory usage (MB)
  max_memory: 2048

# ============================================================================
# INTEGRATION CONFIGURATION
# ============================================================================
integrations:
  # GitHub integration
  github:
    # Create GitHub Security Advisories for findings
    create_advisories: true
    
    # Comment on PRs with findings
    comment_on_pr: true
  
  # SARIF output for GitHub Code Scanning
  sarif:
    enabled: true
    output_file: "semgrep-results.sarif"

# ============================================================================
# BASELINE CONFIGURATION
# ============================================================================
baseline:
  # Path to baseline file
  file: ".semgrep_baseline.json"
  
  # Only report new findings
  new_only: false
  
  # Update baseline automatically
  auto_update: false
