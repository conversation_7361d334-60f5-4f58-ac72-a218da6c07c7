"""
API Dependencies
FastAPI dependency injection for authentication, database, and common utilities
"""

import logging
from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db
from app.models.user import User

logger = logging.getLogger(__name__)

# Security scheme for Bearer token authentication
security = HTTPBearer()


def get_current_user(
    db: Session = Depends(get_db),
    token: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    Get current authenticated user from JWT token.
    
    Args:
        db: Database session
        token: JWT token from Authorization header
        
    Returns:
        User: Current authenticated user
        
    Raises:
        HTTPException: If token is invalid or user not found
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Decode JWT token
        payload = jwt.decode(
            token.credentials,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # Extract user ID from token
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    # Get user from database
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise credentials_exception
    
    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user"
        )
    
    # Check if user account is locked
    if user.is_locked:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Account is locked"
        )
    
    return user


def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user (additional validation).
    
    Args:
        current_user: Current user from get_current_user
        
    Returns:
        User: Current active user
        
    Raises:
        HTTPException: If user is not active
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


def get_current_superuser(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current superuser (admin privileges required).
    
    Args:
        current_user: Current user from get_current_user
        
    Returns:
        User: Current superuser
        
    Raises:
        HTTPException: If user is not a superuser
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


def get_optional_current_user(
    db: Session = Depends(get_db),
    token: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[User]:
    """
    Get current user if token is provided (optional authentication).
    
    Args:
        db: Database session
        token: Optional JWT token
        
    Returns:
        Optional[User]: Current user if authenticated, None otherwise
    """
    if not token:
        return None
    
    try:
        # Decode JWT token
        payload = jwt.decode(
            token.credentials,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        
        # Extract user ID from token
        user_id: str = payload.get("sub")
        if user_id is None:
            return None
            
    except JWTError:
        return None
    
    # Get user from database
    user = db.query(User).filter(User.id == user_id).first()
    if user is None or not user.is_active:
        return None
    
    return user


def check_user_scope(required_scope: str):
    """
    Dependency factory for checking user scopes.
    
    Args:
        required_scope: Required OAuth scope
        
    Returns:
        Dependency function that checks user scope
    """
    def scope_checker(current_user: User = Depends(get_current_user)) -> User:
        if not current_user.has_scope(required_scope):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient scope. Required: {required_scope}"
            )
        return current_user
    
    return scope_checker


def get_user_with_cv_access(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get user with CV generation access.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User: User with CV access
        
    Raises:
        HTTPException: If user doesn't have CV access
    """
    required_scopes = ["cv:read", "cv:write"]
    
    if not any(current_user.has_scope(scope) for scope in required_scopes):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="CV access required"
        )
    
    return current_user


def get_user_with_admin_access(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get user with admin access.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User: User with admin access
        
    Raises:
        HTTPException: If user doesn't have admin access
    """
    if not current_user.is_superuser and not current_user.has_scope("admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    return current_user


# Rate limiting dependency (placeholder for future implementation)
def rate_limit(requests_per_minute: int = 60):
    """
    Rate limiting dependency factory.
    
    Args:
        requests_per_minute: Maximum requests per minute
        
    Returns:
        Dependency function for rate limiting
    """
    def rate_limiter(
        current_user: Optional[User] = Depends(get_optional_current_user)
    ):
        # TODO: Implement actual rate limiting logic
        # For now, this is a placeholder
        pass
    
    return rate_limiter


# Pagination dependency
class PaginationParams:
    """Pagination parameters."""
    
    def __init__(
        self,
        skip: int = 0,
        limit: int = 100,
        max_limit: int = 1000
    ):
        self.skip = max(0, skip)
        self.limit = min(max(1, limit), max_limit)


def get_pagination_params(
    skip: int = 0,
    limit: int = 100
) -> PaginationParams:
    """
    Get pagination parameters with validation.
    
    Args:
        skip: Number of items to skip
        limit: Maximum number of items to return
        
    Returns:
        PaginationParams: Validated pagination parameters
    """
    return PaginationParams(skip=skip, limit=limit)


# Request ID dependency for tracing
def get_request_id() -> str:
    """
    Generate or extract request ID for tracing.
    
    Returns:
        str: Request correlation ID
    """
    import uuid
    return str(uuid.uuid4())


# Common query parameters
class CommonQueryParams:
    """Common query parameters for filtering and sorting."""
    
    def __init__(
        self,
        q: Optional[str] = None,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
        include_deleted: bool = False
    ):
        self.q = q
        self.sort_by = sort_by
        self.sort_order = sort_order.lower() if sort_order else "asc"
        self.include_deleted = include_deleted


def get_common_query_params(
    q: Optional[str] = None,
    sort_by: Optional[str] = None,
    sort_order: str = "asc",
    include_deleted: bool = False
) -> CommonQueryParams:
    """
    Get common query parameters.
    
    Args:
        q: Search query
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        include_deleted: Include soft-deleted items
        
    Returns:
        CommonQueryParams: Common query parameters
    """
    return CommonQueryParams(
        q=q,
        sort_by=sort_by,
        sort_order=sort_order,
        include_deleted=include_deleted
    )
