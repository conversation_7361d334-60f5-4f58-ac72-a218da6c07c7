"""
ImpactCV Configuration Management
Secure configuration handling with environment variables and validation
"""

import os
import secrets
from functools import lru_cache
from typing import Any, Dict, List, Optional, Union

from pydantic import Field, validator
from pydantic import AnyHttpUrl, PostgresDsn, RedisDsn
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings with environment variable support and validation.
    
    Security Features:
    - Environment variable validation
    - Secure defaults for development
    - Production-ready configuration
    - Secrets management integration
    """
    
    # ========================================================================
    # APPLICATION SETTINGS
    # ========================================================================
    APP_NAME: str = Field(default="ImpactCV", description="Application name")
    APP_VERSION: str = Field(default="1.0.0", description="Application version")
    APP_DESCRIPTION: str = Field(
        default="AI-Powered CV Generation System with RAG Pipeline",
        description="Application description"
    )
    
    # Environment configuration
    ENVIRONMENT: str = Field(
        default="development",
        description="Application environment",
        pattern="^(development|staging|production)$"
    )
    
    # Debug mode (disabled in production)
    DEBUG: bool = Field(default=True, description="Debug mode")
    
    @validator("DEBUG", pre=True)
    def validate_debug_mode(cls, v: Any, values: Dict[str, Any]) -> bool:
        """Ensure debug is disabled in production."""
        if values.get("ENVIRONMENT") == "production":
            return False
        return bool(v)
    
    # ========================================================================
    # SERVER CONFIGURATION
    # ========================================================================
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=8000, description="Server port", ge=1, le=65535)
    
    # CORS settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = Field(
        default=[],
        description="Allowed CORS origins"
    )
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Parse CORS origins from environment variable."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # ========================================================================
    # SECURITY CONFIGURATION
    # ========================================================================
    SECRET_KEY: str = Field(
        default_factory=lambda: secrets.token_urlsafe(32),
        description="Application secret key"
    )
    
    # JWT Configuration
    JWT_SECRET_KEY: str = Field(
        default_factory=lambda: secrets.token_urlsafe(32),
        description="JWT secret key"
    )
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30,
        description="JWT access token expiration in minutes"
    )
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(
        default=7,
        description="JWT refresh token expiration in days"
    )
    
    # Password hashing
    PASSWORD_HASH_ALGORITHM: str = Field(
        default="bcrypt",
        description="Password hashing algorithm"
    )
    PASSWORD_HASH_ROUNDS: int = Field(
        default=12,
        description="Password hashing rounds",
        ge=10,
        le=15
    )
    
    # Rate limiting
    RATE_LIMIT_ENABLED: bool = Field(
        default=True,
        description="Enable rate limiting"
    )
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = Field(
        default=60,
        description="Rate limit requests per minute",
        ge=1
    )
    
    # ========================================================================
    # DATABASE CONFIGURATION
    # ========================================================================
    # PostgreSQL
    POSTGRES_SERVER: str = Field(default="localhost", description="PostgreSQL server")
    POSTGRES_PORT: int = Field(default=5432, description="PostgreSQL port", ge=1, le=65535)
    POSTGRES_USER: str = Field(default="impactcv", description="PostgreSQL user")
    POSTGRES_PASSWORD: str = Field(
        default="dev_password_123",
        description="PostgreSQL password"
    )
    POSTGRES_DB: str = Field(default="impactcv", description="PostgreSQL database")
    
    # Database connection settings
    DATABASE_POOL_SIZE: int = Field(
        default=10,
        description="Database connection pool size",
        ge=1,
        le=50
    )
    DATABASE_MAX_OVERFLOW: int = Field(
        default=20,
        description="Database max overflow connections",
        ge=0,
        le=100
    )
    DATABASE_POOL_TIMEOUT: int = Field(
        default=30,
        description="Database pool timeout in seconds",
        ge=1
    )
    
    # Database URL (can be SQLite or PostgreSQL)
    DATABASE_URL: str = Field(
        default="",
        description="Complete database URL (SQLite or PostgreSQL)"
    )

    # Construct Database DSN (fallback)
    SQLALCHEMY_DATABASE_URI: Optional[str] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        """Construct database connection string."""
        # If DATABASE_URL is provided, use it directly
        if values.get("DATABASE_URL"):
            return values["DATABASE_URL"]

        if isinstance(v, str):
            return v

        # Build PostgreSQL URL manually for Pydantic v2 compatibility
        user = values.get("POSTGRES_USER", "impactcv_user")
        password = values.get("POSTGRES_PASSWORD", "dev_postgres_123")
        host = values.get("POSTGRES_SERVER", "localhost")
        port = values.get("POSTGRES_PORT", 5432)
        db = values.get("POSTGRES_DB", "impactcv")

        return f"postgresql://{user}:{password}@{host}:{port}/{db}"
    
    # ========================================================================
    # REDIS CONFIGURATION
    # ========================================================================
    REDIS_HOST: str = Field(default="localhost", description="Redis host")
    REDIS_PORT: int = Field(default=6379, description="Redis port", ge=1, le=65535)
    REDIS_PASSWORD: Optional[str] = Field(
        default="dev_redis_123",
        description="Redis password"
    )
    REDIS_DB: int = Field(default=0, description="Redis database", ge=0, le=15)
    
    # Redis connection settings
    REDIS_POOL_SIZE: int = Field(
        default=10,
        description="Redis connection pool size",
        ge=1,
        le=50
    )
    REDIS_TIMEOUT: int = Field(
        default=5,
        description="Redis timeout in seconds",
        ge=1
    )
    
    # Construct Redis DSN
    REDIS_URL: Optional[RedisDsn] = None
    
    @validator("REDIS_URL", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        """Construct Redis connection string."""
        if isinstance(v, str):
            return v
        
        password = values.get("REDIS_PASSWORD")
        auth_part = f":{password}@" if password else ""
        
        return f"redis://{auth_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT')}/{values.get('REDIS_DB')}"
    
    # ========================================================================
    # AI/ML CONFIGURATION
    # ========================================================================
    # AI Configuration
    AI_PROVIDER: str = Field(
        default="mistral",
        description="AI provider to use: 'mistral' or 'openai'"
    )

    # OpenAI Configuration
    OPENAI_API_KEY: str = Field(
        default="",
        description="OpenAI API key"
    )
    OPENAI_MODEL: str = Field(
        default="gpt-3.5-turbo",
        description="OpenAI model to use"
    )
    OPENAI_MAX_TOKENS: int = Field(
        default=500,
        description="OpenAI max tokens per request",
        ge=1,
        le=4000
    )

    # Mistral Local Configuration
    MISTRAL_BASE_URL: str = Field(
        default="http://localhost:11434",
        description="Mistral/Ollama base URL"
    )
    MISTRAL_MODEL_NAME: str = Field(
        default="mistral:7b",
        description="Mistral model name in Ollama"
    )
    MISTRAL_TIMEOUT: int = Field(
        default=300,
        description="Mistral request timeout in seconds"
    )
    OPENAI_TEMPERATURE: float = Field(
        default=0.7,
        description="OpenAI temperature",
        ge=0.0,
        le=2.0
    )
    OPENAI_TIMEOUT: int = Field(
        default=30,
        description="OpenAI API timeout in seconds",
        ge=1
    )
    
    # RAG Configuration
    RAG_ENABLED: bool = Field(default=True, description="Enable RAG pipeline")
    RAG_VECTOR_STORE_PATH: str = Field(
        default="data/vector_store",
        description="Vector store path"
    )
    RAG_CHUNK_SIZE: int = Field(
        default=1000,
        description="RAG chunk size",
        ge=100,
        le=2000
    )
    RAG_CHUNK_OVERLAP: int = Field(
        default=200,
        description="RAG chunk overlap",
        ge=0,
        le=500
    )
    RAG_TOP_K: int = Field(
        default=5,
        description="RAG top K results",
        ge=1,
        le=20
    )
    
    # ========================================================================
    # LOGGING CONFIGURATION
    # ========================================================================
    LOG_LEVEL: str = Field(
        default="INFO",
        description="Logging level",
        pattern="^(DEBUG|INFO|WARNING|ERROR|CRITICAL)$"
    )
    LOG_FORMAT: str = Field(
        default="json",
        description="Log format",
        pattern="^(json|text)$"
    )
    LOG_FILE_PATH: Optional[str] = Field(
        default="logs/impactcv.log",
        description="Log file path"
    )
    LOG_ROTATION_SIZE: str = Field(
        default="10MB",
        description="Log rotation size"
    )
    LOG_RETENTION_DAYS: int = Field(
        default=30,
        description="Log retention in days",
        ge=1
    )
    
    # ========================================================================
    # MONITORING CONFIGURATION
    # ========================================================================
    METRICS_ENABLED: bool = Field(default=True, description="Enable metrics")
    METRICS_PORT: int = Field(
        default=9090,
        description="Metrics port",
        ge=1,
        le=65535
    )
    
    HEALTH_CHECK_ENABLED: bool = Field(
        default=True,
        description="Enable health checks"
    )
    
    # ========================================================================
    # FILE UPLOAD CONFIGURATION
    # ========================================================================
    UPLOAD_MAX_SIZE: int = Field(
        default=10 * 1024 * 1024,  # 10MB
        description="Maximum upload size in bytes",
        ge=1024
    )
    UPLOAD_ALLOWED_EXTENSIONS: List[str] = Field(
        default=[".pdf", ".docx", ".doc", ".txt"],
        description="Allowed file extensions"
    )
    UPLOAD_PATH: str = Field(
        default="uploads",
        description="Upload directory path"
    )

    # Data directory for storage
    DATA_DIR: str = Field(
        default="data",
        description="Data directory for storage"
    )
    
    # ========================================================================
    # SECURITY HEADERS
    # ========================================================================
    SECURITY_HEADERS_ENABLED: bool = Field(
        default=True,
        description="Enable security headers"
    )
    
    # ========================================================================
    # VALIDATION METHODS
    # ========================================================================
    @validator("OPENAI_API_KEY", pre=True)
    def validate_openai_key(cls, v: str) -> str:
        """Validate OpenAI API key format."""
        # If empty, try to get from environment variable
        if not v:
            v = os.getenv("OPENAI_API_KEY", "")

        if not v or v == "sk-your-openai-api-key-here":
            # This is the template value or empty, warn but allow for development
            import warnings
            warnings.warn(
                "Using template or empty OpenAI API key. AI features will use mock responses. Set OPENAI_API_KEY environment variable for full functionality.",
                UserWarning
            )
            return v
        elif not v.startswith("sk-"):
            raise ValueError("OpenAI API key must start with 'sk-'")
        return v
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v: str) -> str:
        """Validate environment setting."""
        valid_environments = {"development", "staging", "production"}
        if v not in valid_environments:
            raise ValueError(f"Environment must be one of: {valid_environments}")
        return v
    
    # ========================================================================
    # CONFIGURATION METHODS
    # ========================================================================
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.ENVIRONMENT == "development"
    
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.ENVIRONMENT == "production"
    
    def get_database_url(self) -> str:
        """Get database URL string."""
        return str(self.SQLALCHEMY_DATABASE_URI)
    
    def get_redis_url(self) -> str:
        """Get Redis URL string."""
        return str(self.REDIS_URL)
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        validate_assignment = True
        extra = "allow"  # Allow extra fields from .env file


@lru_cache()
def get_settings() -> Settings:
    """
    Get cached application settings.
    
    Returns:
        Settings: Application configuration instance
    """
    return Settings()


# Global settings instance
settings = get_settings()
