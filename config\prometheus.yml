# Prometheus Configuration for ImpactCV
# Enterprise monitoring with comprehensive metrics collection

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'impactcv-production'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Application metrics
  - job_name: 'impactcv-app'
    static_configs:
      - targets: ['app:8000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # API Gateway metrics
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['api-gateway:8000']
    scrape_interval: 15s
    metrics_path: /metrics

  # Authentication service metrics
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service:8000']
    scrape_interval: 15s
    metrics_path: /metrics

  # CV Generation service metrics
  - job_name: 'cv-generation-service'
    static_configs:
      - targets: ['cv-generation-service:8000']
    scrape_interval: 15s
    metrics_path: /metrics

  # RAG service metrics
  - job_name: 'rag-service'
    static_configs:
      - targets: ['rag-service:8000']
    scrape_interval: 15s
    metrics_path: /metrics

  # Data Processing service metrics
  - job_name: 'data-processing-service'
    static_configs:
      - targets: ['data-processing-service:8000']
    scrape_interval: 15s
    metrics_path: /metrics

  # Database metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Elasticsearch metrics
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch-exporter:9114']
    scrape_interval: 30s
    metrics_path: /metrics

  # Custom business metrics
  - job_name: 'business-metrics'
    static_configs:
      - targets: ['app:8000']
    scrape_interval: 60s
    metrics_path: /business-metrics
    honor_labels: true

  # Health check endpoints
  - job_name: 'health-checks'
    static_configs:
      - targets: 
        - 'app:8000'
        - 'api-gateway:8000'
        - 'auth-service:8000'
        - 'cv-generation-service:8000'
        - 'rag-service:8000'
        - 'data-processing-service:8000'
    scrape_interval: 30s
    metrics_path: /health
    honor_labels: true

# Remote write configuration for long-term storage
remote_write:
  - url: "http://cortex:9009/api/prom/push"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Remote read configuration
remote_read:
  - url: "http://cortex:9009/api/prom/read"
    read_recent: true
