// Infinite scroll service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class InfiniteScrollService {
    constructor() {
        this.containers = new Map();
        this.subscribers = new Set();
        this.observers = new Map();
        this.initialize();
    }

    /**
     * Initialize the infinite scroll service
     */
    initialize() {
        // Create intersection observer
        this.observer = new IntersectionObserver(
            this.handleIntersection.bind(this),
            {
                root: null,
                rootMargin: '0px',
                threshold: 0.1,
            }
        );
    }

    /**
     * Subscribe to infinite scroll events
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of infinite scroll events
     * @param {string} event - The event type
     * @param {Object} data - The event data
     */
    notifySubscribers(event, data) {
        this.subscribers.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in infinite scroll subscriber:', error);
            }
        });
    }

    /**
     * Register an infinite scroll container
     * @param {string} id - The container ID
     * @param {Object} options - The infinite scroll options
     */
    registerContainer(id, options = {}) {
        const container = document.getElementById(id);
        if (!container) {
            console.error(`Container ${id} not found`);
            return;
        }

        // Create sentinel element
        const sentinel = document.createElement('div');
        sentinel.className = 'infinite-scroll-sentinel';
        container.appendChild(sentinel);

        // Create loading element
        const loading = document.createElement('div');
        loading.className = 'infinite-scroll-loading';
        loading.style.display = 'none';
        container.appendChild(loading);

        this.containers.set(id, {
            ...options,
            container,
            sentinel,
            loading,
            page: 1,
            loading: false,
            hasMore: true,
        });

        // Observe sentinel
        this.observer.observe(sentinel);
    }

    /**
     * Handle intersection events
     * @param {IntersectionObserverEntry[]} entries - The intersection entries
     */
    handleIntersection(entries) {
        return measurePerformance('infinite_intersection', () => {
            entries.forEach(entry => {
                if (!entry.isIntersecting) {
                    return;
                }

                const sentinel = entry.target;
                const container = sentinel.parentElement;
                const id = container.id;
                const infinite = this.containers.get(id);
                if (!infinite || infinite.loading || !infinite.hasMore) {
                    return;
                }

                this.loadMore(id);
            });
        });
    }

    /**
     * Load more items
     * @param {string} id - The container ID
     */
    async loadMore(id) {
        return measurePerformance('infinite_loadmore', async () => {
            const infinite = this.containers.get(id);
            if (!infinite || infinite.loading || !infinite.hasMore) {
                return;
            }

            try {
                // Show loading
                infinite.loading = true;
                infinite.loadingElement.style.display = 'block';

                // Load items
                const items = await infinite.loadItems(infinite.page);
                if (!items || items.length === 0) {
                    infinite.hasMore = false;
                    return;
                }

                // Append items
                items.forEach(item => {
                    infinite.container.insertBefore(item, infinite.sentinel);
                });

                // Update page
                infinite.page += 1;

                if (infinite.onLoad) {
                    infinite.onLoad({
                        container: infinite.container,
                        items,
                        page: infinite.page,
                    });
                }

                this.notifySubscribers('load', { id, items });
            } catch (error) {
                console.error('Error loading more items:', error);

                if (infinite.onError) {
                    infinite.onError(error);
                }

                this.notifySubscribers('error', { id, error });
            } finally {
                // Hide loading
                infinite.loading = false;
                infinite.loadingElement.style.display = 'none';
            }
        });
    }

    /**
     * Reset a container
     * @param {string} id - The container ID
     */
    resetContainer(id) {
        return measurePerformance('infinite_reset', () => {
            const infinite = this.containers.get(id);
            if (!infinite) {
                return;
            }

            // Remove items
            const items = infinite.container.querySelectorAll('.infinite-scroll-item');
            items.forEach(item => {
                if (item !== infinite.sentinel && item !== infinite.loading) {
                    infinite.container.removeChild(item);
                }
            });

            // Reset state
            infinite.page = 1;
            infinite.loading = false;
            infinite.hasMore = true;

            if (infinite.onReset) {
                infinite.onReset({
                    container: infinite.container,
                });
            }

            this.notifySubscribers('reset', { id });
        });
    }

    /**
     * Get container data
     * @param {string} id - The container ID
     * @returns {Object} The container data
     */
    getContainerData(id) {
        return this.containers.get(id);
    }

    /**
     * Update container data
     * @param {string} id - The container ID
     * @param {Object} data - The new container data
     */
    updateContainerData(id, data) {
        const infinite = this.containers.get(id);
        if (infinite) {
            Object.assign(infinite, data);
        }
    }
}

// Create and export a singleton instance
const infiniteScrollService = new InfiniteScrollService();
export default infiniteScrollService; 