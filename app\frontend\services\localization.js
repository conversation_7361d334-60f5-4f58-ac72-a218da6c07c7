// Localization service
import config from '../config.js';
import { measurePerformance } from '../utils.js';

class LocalizationService {
    constructor() {
        this.defaultLocale = config.localization?.defaultLocale || 'en';
        this.fallbackLocale = config.localization?.fallbackLocale || 'en';
        this.currentLocale = this.defaultLocale;
        this.translations = new Map();
        this.subscribers = new Set();
        this.dateFormats = {
            short: { year: 'numeric', month: 'short', day: 'numeric' },
            medium: { year: 'numeric', month: 'long', day: 'numeric' },
            long: { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' },
        };
        this.numberFormats = {
            decimal: { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 },
            currency: { style: 'currency', currency: 'USD' },
            percent: { style: 'percent', minimumFractionDigits: 2, maximumFractionDigits: 2 },
        };
    }

    /**
     * Subscribe to locale changes
     * @param {Function} callback - The callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(callback) {
        this.subscribers.add(callback);
        return () => this.subscribers.delete(callback);
    }

    /**
     * Notify subscribers of locale changes
     */
    notifySubscribers() {
        this.subscribers.forEach(callback => {
            try {
                callback(this.currentLocale);
            } catch (error) {
                console.error('Error in localization subscriber:', error);
            }
        });
    }

    /**
     * Load translations for a locale
     * @param {string} locale - The locale to load
     * @returns {Promise<void>}
     */
    async loadTranslations(locale) {
        return measurePerformance('load_translations', async () => {
            try {
                const response = await fetch(`/locales/${locale}.json`);
                if (!response.ok) {
                    throw new Error(`Failed to load translations for ${locale}`);
                }
                const translations = await response.json();
                this.translations.set(locale, translations);
            } catch (error) {
                console.error(`Error loading translations for ${locale}:`, error);
                if (locale !== this.fallbackLocale) {
                    await this.loadTranslations(this.fallbackLocale);
                }
            }
        });
    }

    /**
     * Set the current locale
     * @param {string} locale - The locale to set
     * @returns {Promise<void>}
     */
    async setLocale(locale) {
        if (locale === this.currentLocale) return;

        await this.loadTranslations(locale);
        this.currentLocale = locale;
        document.documentElement.lang = locale;
        this.notifySubscribers();
    }

    /**
     * Get the current locale
     * @returns {string} The current locale
     */
    getLocale() {
        return this.currentLocale;
    }

    /**
     * Translate a key
     * @param {string} key - The translation key
     * @param {Object} [params] - The translation parameters
     * @returns {string} The translated string
     */
    translate(key, params = {}) {
        const translations = this.translations.get(this.currentLocale) || {};
        let translation = this.getNestedTranslation(translations, key);

        if (!translation && this.currentLocale !== this.fallbackLocale) {
            const fallbackTranslations = this.translations.get(this.fallbackLocale) || {};
            translation = this.getNestedTranslation(fallbackTranslations, key);
        }

        if (!translation) {
            console.warn(`Missing translation for key: ${key}`);
            return key;
        }

        return this.interpolate(translation, params);
    }

    /**
     * Get a nested translation
     * @param {Object} translations - The translations object
     * @param {string} key - The translation key
     * @returns {string|undefined} The translation
     */
    getNestedTranslation(translations, key) {
        return key.split('.').reduce((obj, k) => obj?.[k], translations);
    }

    /**
     * Interpolate parameters in a translation
     * @param {string} translation - The translation string
     * @param {Object} params - The parameters
     * @returns {string} The interpolated string
     */
    interpolate(translation, params) {
        return translation.replace(/\{(\w+)\}/g, (match, key) => {
            return params[key] !== undefined ? params[key] : match;
        });
    }

    /**
     * Format a date
     * @param {Date|string|number} date - The date to format
     * @param {string} [format='medium'] - The date format
     * @returns {string} The formatted date
     */
    formatDate(date, format = 'medium') {
        const dateObj = new Date(date);
        const options = this.dateFormats[format] || this.dateFormats.medium;
        return new Intl.DateTimeFormat(this.currentLocale, options).format(dateObj);
    }

    /**
     * Format a number
     * @param {number} number - The number to format
     * @param {string} [format='decimal'] - The number format
     * @returns {string} The formatted number
     */
    formatNumber(number, format = 'decimal') {
        const options = this.numberFormats[format] || this.numberFormats.decimal;
        return new Intl.NumberFormat(this.currentLocale, options).format(number);
    }

    /**
     * Format a currency amount
     * @param {number} amount - The amount to format
     * @param {string} [currency='USD'] - The currency code
     * @returns {string} The formatted currency
     */
    formatCurrency(amount, currency = 'USD') {
        return this.formatNumber(amount, {
            ...this.numberFormats.currency,
            currency,
        });
    }

    /**
     * Format a percentage
     * @param {number} value - The value to format
     * @returns {string} The formatted percentage
     */
    formatPercent(value) {
        return this.formatNumber(value, 'percent');
    }

    /**
     * Get available locales
     * @returns {string[]} The available locales
     */
    getAvailableLocales() {
        return Array.from(this.translations.keys());
    }

    /**
     * Check if a locale is available
     * @param {string} locale - The locale to check
     * @returns {boolean} Whether the locale is available
     */
    isLocaleAvailable(locale) {
        return this.translations.has(locale);
    }

    /**
     * Get the browser's preferred locale
     * @returns {string} The preferred locale
     */
    getBrowserLocale() {
        return navigator.language || navigator.userLanguage || this.defaultLocale;
    }

    /**
     * Initialize the localization service
     * @returns {Promise<void>}
     */
    async initialize() {
        const browserLocale = this.getBrowserLocale();
        await this.setLocale(browserLocale);
    }
}

// Create and export a singleton instance
const localizationService = new LocalizationService();
export default localizationService; 