import asyncio
import time
import aiohttp
import json
from typing import List, Dict, Any
import statistics
from datetime import datetime

class APIPerformanceTest:
    def __init__(self, base_url: str = "http://localhost:8000/api/v1"):
        self.base_url = base_url
        self.results: List[Dict[str, Any]] = []
        
    async def test_endpoint(self, endpoint: str, method: str = "GET", data: Dict = None) -> Dict[str, Any]:
        """Test a single endpoint and measure its performance"""
        start_time = time.time()
        async with aiohttp.ClientSession() as session:
            try:
                if method == "GET":
                    async with session.get(f"{self.base_url}/{endpoint}") as response:
                        response_time = time.time() - start_time
                        return {
                            "endpoint": endpoint,
                            "method": method,
                            "status": response.status,
                            "response_time": response_time,
                            "success": response.status == 200
                        }
                elif method == "POST":
                    async with session.post(
                        f"{self.base_url}/{endpoint}",
                        json=data
                    ) as response:
                        response_time = time.time() - start_time
                        return {
                            "endpoint": endpoint,
                            "method": method,
                            "status": response.status,
                            "response_time": response_time,
                            "success": response.status == 200
                        }
            except Exception as e:
                return {
                    "endpoint": endpoint,
                    "method": method,
                    "status": 500,
                    "response_time": time.time() - start_time,
                    "success": False,
                    "error": str(e)
                }
    
    async def run_concurrent_tests(self, endpoint: str, num_requests: int, method: str = "GET", data: Dict = None) -> Dict[str, Any]:
        """Run multiple concurrent tests on an endpoint"""
        tasks = [self.test_endpoint(endpoint, method, data) for _ in range(num_requests)]
        results = await asyncio.gather(*tasks)
        
        # Calculate statistics
        response_times = [r["response_time"] for r in results]
        success_rate = sum(1 for r in results if r["success"]) / len(results)
        
        return {
            "endpoint": endpoint,
            "method": method,
            "num_requests": num_requests,
            "success_rate": success_rate,
            "avg_response_time": statistics.mean(response_times),
            "min_response_time": min(response_times),
            "max_response_time": max(response_times),
            "std_dev": statistics.stdev(response_times) if len(response_times) > 1 else 0,
            "results": results
        }
    
    def save_results(self, filename: str = None):
        """Save test results to a file"""
        if filename is None:
            filename = f"performance_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "base_url": self.base_url,
                "results": self.results
            }, f, indent=2)
        
        print(f"Results saved to {filename}")

async def main():
    # Initialize test
    test = APIPerformanceTest()
    
    # Test health endpoint
    print("Testing health endpoint...")
    health_results = await test.run_concurrent_tests("ai-test/health", 10)
    test.results.append(health_results)
    
    # Test TQR generation
    print("Testing TQR generation...")
    tqr_data = {
        "name": "John Doe",
        "position": "Software Engineer",
        "experience_years": 5,
        "achievement_description": "Led a team of 5 developers to implement a new feature that increased user engagement by 25%"
    }
    tqr_results = await test.run_concurrent_tests(
        "ai-test/generate-tqr",
        5,
        method="POST",
        data=tqr_data
    )
    test.results.append(tqr_results)
    
    # Print summary
    print("\nTest Summary:")
    for result in test.results:
        print(f"\nEndpoint: {result['endpoint']}")
        print(f"Method: {result['method']}")
        print(f"Success Rate: {result['success_rate']*100:.2f}%")
        print(f"Average Response Time: {result['avg_response_time']*1000:.2f}ms")
        print(f"Min Response Time: {result['min_response_time']*1000:.2f}ms")
        print(f"Max Response Time: {result['max_response_time']*1000:.2f}ms")
        print(f"Standard Deviation: {result['std_dev']*1000:.2f}ms")
    
    # Save results
    test.save_results()

if __name__ == "__main__":
    asyncio.run(main()) 