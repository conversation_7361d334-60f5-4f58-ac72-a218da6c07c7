---
name: 🐛 Bug Report
about: Create a report to help us improve ImpactCV
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ['dasotillop']
---

# 🐛 Bug Report

## 📋 **Bug Information**

### **Bug Summary**
<!-- A clear and concise description of what the bug is -->

### **Expected Behavior**
<!-- A clear and concise description of what you expected to happen -->

### **Actual Behavior**
<!-- A clear and concise description of what actually happened -->

---

## 🔄 **Reproduction Steps**

### **Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

### **Minimal Reproducible Example**
```python
# Provide a minimal code example that reproduces the issue
```

### **Frequency**
- [ ] Always
- [ ] Sometimes
- [ ] Rarely
- [ ] Only once

---

## 🖥️ **Environment**

### **System Information**
- **OS:** [e.g., Windows 11, Ubuntu 22.04, macOS 13]
- **Python Version:** [e.g., 3.11.5]
- **ImpactCV Version:** [e.g., 1.0.0]
- **Docker Version:** [e.g., 24.0.6] (if applicable)

### **Dependencies**
```bash
# Paste output of: pip list
```

### **Configuration**
```yaml
# Relevant configuration (remove sensitive data)
```

---

## 📊 **Additional Context**

### **Screenshots/Logs**
<!-- Add screenshots, error logs, or other visual aids -->

### **Error Messages**
```
Paste any error messages here
```

### **Stack Trace**
```
Paste full stack trace here
```

---

## 🔒 **Security Considerations**

### **Security Impact**
- [ ] No security impact
- [ ] Potential security vulnerability
- [ ] Confirmed security issue

### **Data Exposure**
- [ ] No sensitive data involved
- [ ] Potential data exposure
- [ ] Confirmed data leak

**Note:** If this is a security vulnerability, please report it <NAME_EMAIL>

---

## 🎯 **Impact Assessment**

### **Severity**
- [ ] 🔴 **Critical** - System unusable, data loss
- [ ] 🟠 **High** - Major functionality broken
- [ ] 🟡 **Medium** - Minor functionality affected
- [ ] 🟢 **Low** - Cosmetic issue, workaround available

### **Components Affected**
- [ ] 🤖 AI/ML Pipeline
- [ ] 🔐 Authentication
- [ ] 📊 API Endpoints
- [ ] 🗄️ Database
- [ ] 🐳 Infrastructure
- [ ] 📈 Monitoring
- [ ] 📝 Documentation

### **User Impact**
- **Users Affected:** [e.g., All users, Admin users only, Specific workflow]
- **Business Impact:** [e.g., Cannot generate CVs, Performance degraded]

---

## 🔧 **Possible Solution**

### **Suggested Fix**
<!-- If you have ideas on how to fix the issue -->

### **Workaround**
<!-- If you found a temporary workaround -->

---

## 📋 **Checklist**

- [ ] I have searched existing issues to ensure this is not a duplicate
- [ ] I have provided all requested information
- [ ] I have removed any sensitive data from logs/screenshots
- [ ] I have tested this on the latest version
- [ ] I have included steps to reproduce the issue

---

## 🏷️ **Labels**

<!-- The following labels will be automatically applied -->
- `bug` - Bug report
- `needs-triage` - Needs initial review

<!-- Additional labels may be added by maintainers -->
- `priority-high` - High priority issue
- `security` - Security-related issue
- `performance` - Performance issue
- `ai-ml` - AI/ML related
- `infrastructure` - Infrastructure issue
