# Data Retention Policies Configuration
# GDPR and Enterprise Compliance

# Global Settings
global:
  default_retention_days: 2555  # 7 years default
  grace_period_days: 30
  audit_retention_days: 3650    # 10 years for audit logs
  backup_retention_days: 365    # 1 year for backups
  
# Data Classification Policies
classification_policies:
  public:
    retention_days: 3650        # 10 years
    auto_delete: false
    requires_approval: false
    
  internal:
    retention_days: 2555        # 7 years
    auto_delete: true
    requires_approval: false
    
  confidential:
    retention_days: 1825        # 5 years
    auto_delete: true
    requires_approval: true
    
  restricted:
    retention_days: 1095        # 3 years
    auto_delete: true
    requires_approval: true
    
  personal_data:
    retention_days: 1095        # 3 years (GDPR compliance)
    auto_delete: true
    requires_approval: true
    right_to_be_forgotten: true

# Data Type Specific Policies
data_type_policies:
  cv_documents:
    retention_days: 1825        # 5 years
    classification: confidential
    auto_delete: true
    requires_approval: true
    gdpr_applicable: true
    backup_required: true
    
  user_profiles:
    retention_days: 1095        # 3 years
    classification: personal_data
    auto_delete: true
    requires_approval: true
    gdpr_applicable: true
    right_to_be_forgotten: true
    
  generated_content:
    retention_days: 1095        # 3 years
    classification: confidential
    auto_delete: true
    requires_approval: false
    gdpr_applicable: true
    
  audit_logs:
    retention_days: 3650        # 10 years
    classification: internal
    auto_delete: false
    requires_approval: true
    backup_required: true
    
  security_logs:
    retention_days: 2555        # 7 years
    classification: confidential
    auto_delete: false
    requires_approval: true
    backup_required: true
    
  performance_metrics:
    retention_days: 730         # 2 years
    classification: internal
    auto_delete: true
    requires_approval: false
    
  temporary_files:
    retention_days: 30          # 30 days
    classification: internal
    auto_delete: true
    requires_approval: false
    
  cache_data:
    retention_days: 7           # 7 days
    classification: internal
    auto_delete: true
    requires_approval: false

# GDPR Specific Policies
gdpr_policies:
  personal_data_retention:
    max_retention_days: 1095    # 3 years maximum
    consent_required: true
    purpose_limitation: true
    data_minimization: true
    
  right_to_be_forgotten:
    enabled: true
    grace_period_days: 30
    verification_required: true
    cascade_delete: true
    
  data_portability:
    enabled: true
    export_formats: ["json", "csv", "pdf"]
    max_export_size_mb: 100
    
  consent_management:
    track_consent: true
    consent_expiry_days: 730    # 2 years
    renewal_notification_days: 30

# Compliance Framework Policies
compliance_frameworks:
  dama_dmbok:
    data_governance: true
    quality_monitoring: true
    lineage_tracking: true
    metadata_management: true
    
  iso_27001:
    security_controls: true
    access_logging: true
    incident_response: true
    
  soc2:
    availability_monitoring: true
    processing_integrity: true
    confidentiality_controls: true

# Automated Cleanup Rules
cleanup_rules:
  daily_cleanup:
    enabled: true
    schedule: "0 2 * * *"       # 2 AM daily
    targets:
      - temporary_files
      - cache_data
      - expired_sessions
      
  weekly_cleanup:
    enabled: true
    schedule: "0 3 * * 0"       # 3 AM Sunday
    targets:
      - old_logs
      - expired_tokens
      - unused_uploads
      
  monthly_cleanup:
    enabled: true
    schedule: "0 4 1 * *"       # 4 AM first day of month
    targets:
      - archived_data
      - old_backups
      - expired_consents
      
  annual_review:
    enabled: true
    schedule: "0 5 1 1 *"       # 5 AM January 1st
    targets:
      - all_data_types
      - policy_review
      - compliance_audit

# Notification Settings
notifications:
  retention_warnings:
    enabled: true
    warning_days: [90, 30, 7]   # Days before deletion
    recipients:
      - data_owner
      - data_steward
      - compliance_team
      
  deletion_confirmations:
    enabled: true
    recipients:
      - data_owner
      - audit_team
      
  policy_updates:
    enabled: true
    recipients:
      - all_users
      - compliance_team
      - legal_team

# Exception Handling
exceptions:
  legal_hold:
    enabled: true
    override_retention: true
    requires_approval: true
    approval_roles:
      - legal_counsel
      - compliance_officer
      
  regulatory_requirements:
    enabled: true
    override_policies: true
    documentation_required: true
    
  business_critical:
    enabled: true
    extended_retention: true
    approval_required: true

# Monitoring and Reporting
monitoring:
  retention_compliance:
    enabled: true
    check_frequency: daily
    alert_threshold: 95         # % compliance
    
  deletion_tracking:
    enabled: true
    log_all_deletions: true
    verify_deletions: true
    
  policy_violations:
    enabled: true
    immediate_alerts: true
    escalation_enabled: true

# Data Subject Rights (GDPR)
data_subject_rights:
  access_request:
    enabled: true
    response_time_days: 30
    verification_required: true
    
  rectification:
    enabled: true
    response_time_days: 30
    audit_trail: true
    
  erasure:
    enabled: true
    response_time_days: 30
    verification_required: true
    cascade_delete: true
    
  portability:
    enabled: true
    response_time_days: 30
    supported_formats: ["json", "csv"]
    
  objection:
    enabled: true
    response_time_days: 30
    processing_halt: true

# Backup and Archive Policies
backup_policies:
  incremental_backup:
    frequency: daily
    retention_days: 30
    
  full_backup:
    frequency: weekly
    retention_days: 365
    
  archive_backup:
    frequency: monthly
    retention_days: 2555        # 7 years
    
  disaster_recovery:
    rpo_hours: 24              # Recovery Point Objective
    rto_hours: 72              # Recovery Time Objective
    offsite_backup: true

# Encryption Requirements
encryption:
  data_at_rest:
    required: true
    algorithm: "AES-256"
    key_rotation_days: 90
    
  data_in_transit:
    required: true
    protocol: "TLS 1.3"
    certificate_validation: true
    
  backup_encryption:
    required: true
    separate_keys: true
    key_escrow: true
