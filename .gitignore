# =============================================================================
# IMPACTCV GITIGNORE
# =============================================================================

# Environment variables (contains sensitive information)
# .env  # Temporarily commented out to allow creation
.env.local
.env.production
.env.staging

# Python virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# celery beat schedule file
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# DOCKER & CONTAINERS
# =============================================================================

# Docker volumes and data
docker-compose.override.yml
.dockerignore

# Container data volumes (persistent data)
postgres_data/
redis_data/
elasticsearch_data/
grafana_data/
prometheus_data/
faiss_data/
cv_storage/
document_storage/

# =============================================================================
# LOGS & TEMPORARY FILES
# =============================================================================

# Application logs
logs/
*.log
*.log.*

# Temporary files
tmp/
temp/
.tmp/
.temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# =============================================================================
# IDE & EDITOR FILES
# =============================================================================

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*~
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# APPLICATION SPECIFIC
# =============================================================================

# Uploaded files and user data
uploads/
storage/
media/
static/

# Generated CV files
generated_cvs/
output/

# FAISS indices and embeddings
faiss_indices/
embeddings/
*.index
*.faiss

# SSL certificates (local development)
*.pem
*.key
*.crt
*.p12
*.pfx

# Backup files
*.bak
*.backup
*.sql
backups/

# =============================================================================
# SECURITY & SECRETS
# =============================================================================

# API keys and secrets
secrets/
.secrets/
api_keys.txt
*.key
*.secret

# SSL/TLS certificates
ssl/
certs/
certificates/

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Prometheus data
prometheus/data/

# Grafana data
grafana/data/

# Elasticsearch data
elasticsearch/data/

# Log files
kibana/logs/
logstash/logs/

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Test databases
test.db
test.sqlite
test_*.db

# Test outputs
test_outputs/
test_results/

# Performance testing
locust_reports/
load_test_results/

# =============================================================================
# DOCUMENTATION
# =============================================================================

# Generated documentation
docs/_build/
docs/build/
site/

# =============================================================================
# PACKAGE MANAGERS
# =============================================================================

# npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# =============================================================================
# CLOUD & DEPLOYMENT
# =============================================================================

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Ansible
*.retry

# Kubernetes
*.kubeconfig

# =============================================================================
# MACOS
# =============================================================================

# General
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# =============================================================================
# WINDOWS
# =============================================================================

# Windows thumbnail cache files
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db

# Dump file
*.stackdump

# Folder config file
[Dd]esktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msix
*.msm
*.msp

# Windows shortcuts
*.lnk

# =============================================================================
# LINUX
# =============================================================================

*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Database
*.db
*.sqlite3

# Logs
*.log

# Local development
.env
.env.local
.env.*.local

# Test coverage
.coverage
coverage.json
htmlcov/

# Project specific
impactcv.db
checklist_progress.json
execution_report.json
forgot_password_test.json
login_test.json
phase_1_completion_report.json
phase_2_validation_report.json
rag_test.json
