"""
Data Quality Service
Automated quality scoring and reporting with DAMA-DMBOK compliance
"""

import logging
import statistics
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class QualityMetric(BaseModel):
    """Quality metric model."""
    
    name: str = Field(..., description="Metric name")
    value: float = Field(..., description="Metric value (0-1)")
    weight: float = Field(..., description="Metric weight in overall score")
    description: str = Field(..., description="Metric description")
    category: str = Field(..., description="Quality category")
    threshold: float = Field(..., description="Acceptable threshold")
    status: str = Field(..., description="Status: pass, warning, fail")


class QualityReport(BaseModel):
    """Data quality report model."""
    
    overall_score: float = Field(..., description="Overall quality score (0-1)")
    grade: str = Field(..., description="Quality grade (A-F)")
    metrics: List[QualityMetric] = Field(..., description="Individual quality metrics")
    recommendations: List[str] = Field(default_factory=list, description="Improvement recommendations")
    compliance_status: Dict[str, str] = Field(..., description="Compliance framework status")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Report generation time")


class DataQualityError(Exception):
    """Custom exception for data quality errors."""
    
    def __init__(self, message: str, error_code: str = "QUALITY_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DataQualityService:
    """
    Enterprise data quality service with DAMA-DMBOK compliance.
    
    Features:
    - Comprehensive quality metrics
    - DAMA-DMBOK framework compliance
    - Automated scoring and grading
    - Quality trend analysis
    - Improvement recommendations
    """
    
    def __init__(self):
        """Initialize data quality service."""
        
        # Quality dimensions based on DAMA-DMBOK
        self.quality_dimensions = {
            'completeness': {
                'weight': 0.25,
                'threshold': 0.8,
                'description': 'Percentage of required fields populated'
            },
            'accuracy': {
                'weight': 0.20,
                'threshold': 0.9,
                'description': 'Correctness of data values'
            },
            'consistency': {
                'weight': 0.15,
                'threshold': 0.85,
                'description': 'Data consistency across fields'
            },
            'validity': {
                'weight': 0.15,
                'threshold': 0.9,
                'description': 'Data conforms to defined formats'
            },
            'uniqueness': {
                'weight': 0.10,
                'threshold': 0.95,
                'description': 'Absence of duplicate records'
            },
            'timeliness': {
                'weight': 0.10,
                'threshold': 0.8,
                'description': 'Data is current and up-to-date'
            },
            'integrity': {
                'weight': 0.05,
                'threshold': 0.95,
                'description': 'Referential integrity maintained'
            }
        }
        
        # Grading scale
        self.grading_scale = {
            0.95: 'A+',
            0.90: 'A',
            0.85: 'A-',
            0.80: 'B+',
            0.75: 'B',
            0.70: 'B-',
            0.65: 'C+',
            0.60: 'C',
            0.55: 'C-',
            0.50: 'D',
            0.00: 'F'
        }
    
    def assess_cv_quality(self, cv_data: Dict[str, Any]) -> QualityReport:
        """
        Assess CV data quality using DAMA-DMBOK framework.
        
        Args:
            cv_data: CV data dictionary
            
        Returns:
            QualityReport: Comprehensive quality assessment
        """
        try:
            metrics = []
            recommendations = []
            
            # Assess each quality dimension
            completeness_metric = self._assess_completeness(cv_data)
            metrics.append(completeness_metric)
            
            accuracy_metric = self._assess_accuracy(cv_data)
            metrics.append(accuracy_metric)
            
            consistency_metric = self._assess_consistency(cv_data)
            metrics.append(consistency_metric)
            
            validity_metric = self._assess_validity(cv_data)
            metrics.append(validity_metric)
            
            uniqueness_metric = self._assess_uniqueness(cv_data)
            metrics.append(uniqueness_metric)
            
            timeliness_metric = self._assess_timeliness(cv_data)
            metrics.append(timeliness_metric)
            
            integrity_metric = self._assess_integrity(cv_data)
            metrics.append(integrity_metric)
            
            # Calculate overall score
            overall_score = sum(
                metric.value * metric.weight 
                for metric in metrics
            )
            
            # Determine grade
            grade = self._calculate_grade(overall_score)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(metrics)
            
            # Check compliance status
            compliance_status = self._check_compliance(metrics)
            
            return QualityReport(
                overall_score=overall_score,
                grade=grade,
                metrics=metrics,
                recommendations=recommendations,
                compliance_status=compliance_status
            )
            
        except Exception as e:
            logger.error(f"Data quality assessment failed: {e}")
            raise DataQualityError(
                f"Data quality assessment failed: {str(e)}",
                error_code="ASSESSMENT_FAILED"
            )
    
    def _assess_completeness(self, cv_data: Dict[str, Any]) -> QualityMetric:
        """Assess data completeness."""
        required_fields = {
            'personal': ['full_name'],
            'experience': ['job_title', 'company'],
            'education': ['degree', 'institution'],
            'skills': ['name']
        }
        
        total_required = 0
        total_present = 0
        
        for section, fields in required_fields.items():
            if section in cv_data:
                section_data = cv_data[section]
                
                if section == 'personal':
                    # Single object
                    for field in fields:
                        total_required += 1
                        if section_data.get(field):
                            total_present += 1
                else:
                    # List of objects
                    if isinstance(section_data, list):
                        for item in section_data:
                            for field in fields:
                                total_required += 1
                                if item.get(field):
                                    total_present += 1
        
        # Add optional but recommended fields
        optional_fields = ['email', 'phone', 'summary']
        for field in optional_fields:
            total_required += 1
            if cv_data.get('personal', {}).get(field) or cv_data.get(field):
                total_present += 1
        
        completeness_score = total_present / total_required if total_required > 0 else 0
        
        config = self.quality_dimensions['completeness']
        status = 'pass' if completeness_score >= config['threshold'] else 'warning' if completeness_score >= 0.6 else 'fail'
        
        return QualityMetric(
            name='completeness',
            value=completeness_score,
            weight=config['weight'],
            description=config['description'],
            category='Data Completeness',
            threshold=config['threshold'],
            status=status
        )
    
    def _assess_accuracy(self, cv_data: Dict[str, Any]) -> QualityMetric:
        """Assess data accuracy."""
        accuracy_score = 1.0
        total_checks = 0
        failed_checks = 0
        
        # Check email format
        email = cv_data.get('personal', {}).get('email')
        if email:
            total_checks += 1
            import re
            email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
            if not email_pattern.match(email):
                failed_checks += 1
        
        # Check phone format
        phone = cv_data.get('personal', {}).get('phone')
        if phone:
            total_checks += 1
            phone_pattern = re.compile(r'^[\+]?[1-9]?[\d\s\-\(\)\.]{7,15}$')
            if not phone_pattern.match(phone):
                failed_checks += 1
        
        # Check date formats in experience
        if 'experience' in cv_data:
            for exp in cv_data['experience']:
                for date_field in ['start_date', 'end_date']:
                    if exp.get(date_field) and exp[date_field] != 'present':
                        total_checks += 1
                        if not self._is_valid_date_format(exp[date_field]):
                            failed_checks += 1
        
        # Check date formats in education
        if 'education' in cv_data:
            for edu in cv_data['education']:
                for date_field in ['start_date', 'end_date']:
                    if edu.get(date_field) and edu[date_field] != 'present':
                        total_checks += 1
                        if not self._is_valid_date_format(edu[date_field]):
                            failed_checks += 1
        
        if total_checks > 0:
            accuracy_score = (total_checks - failed_checks) / total_checks
        
        config = self.quality_dimensions['accuracy']
        status = 'pass' if accuracy_score >= config['threshold'] else 'warning' if accuracy_score >= 0.7 else 'fail'
        
        return QualityMetric(
            name='accuracy',
            value=accuracy_score,
            weight=config['weight'],
            description=config['description'],
            category='Data Accuracy',
            threshold=config['threshold'],
            status=status
        )
    
    def _assess_consistency(self, cv_data: Dict[str, Any]) -> QualityMetric:
        """Assess data consistency."""
        consistency_score = 1.0
        total_checks = 0
        failed_checks = 0
        
        # Check name consistency
        personal_name = cv_data.get('personal', {}).get('full_name', '')
        if personal_name:
            # Check if name appears consistently in other sections
            total_checks += 1
            # This is a simplified check - in practice, you'd use more sophisticated matching
        
        # Check date consistency (start < end)
        if 'experience' in cv_data:
            for exp in cv_data['experience']:
                start_date = exp.get('start_date')
                end_date = exp.get('end_date')
                if start_date and end_date and end_date != 'present':
                    total_checks += 1
                    if not self._is_date_order_valid(start_date, end_date):
                        failed_checks += 1
        
        # Check education date consistency
        if 'education' in cv_data:
            for edu in cv_data['education']:
                start_date = edu.get('start_date')
                end_date = edu.get('end_date')
                if start_date and end_date and end_date != 'present':
                    total_checks += 1
                    if not self._is_date_order_valid(start_date, end_date):
                        failed_checks += 1
        
        # Check skill name consistency (no duplicates)
        if 'skills' in cv_data:
            skill_names = [skill.get('name', '').lower() for skill in cv_data['skills'] if skill.get('name')]
            total_checks += len(skill_names)
            unique_skills = set(skill_names)
            failed_checks += len(skill_names) - len(unique_skills)
        
        if total_checks > 0:
            consistency_score = (total_checks - failed_checks) / total_checks
        
        config = self.quality_dimensions['consistency']
        status = 'pass' if consistency_score >= config['threshold'] else 'warning' if consistency_score >= 0.7 else 'fail'
        
        return QualityMetric(
            name='consistency',
            value=consistency_score,
            weight=config['weight'],
            description=config['description'],
            category='Data Consistency',
            threshold=config['threshold'],
            status=status
        )
    
    def _assess_validity(self, cv_data: Dict[str, Any]) -> QualityMetric:
        """Assess data validity."""
        validity_score = 1.0
        total_checks = 0
        failed_checks = 0
        
        # Check URL validity
        personal = cv_data.get('personal', {})
        url_fields = ['linkedin', 'github', 'website', 'portfolio']
        for field in url_fields:
            url = personal.get(field)
            if url:
                total_checks += 1
                import re
                url_pattern = re.compile(
                    r'^https?://'
                    r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'
                    r'localhost|'
                    r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
                    r'(?::\d+)?'
                    r'(?:/?|[/?]\S+)$', re.IGNORECASE
                )
                if not url_pattern.match(url):
                    failed_checks += 1
        
        # Check GPA validity
        if 'education' in cv_data:
            for edu in cv_data['education']:
                gpa = edu.get('gpa')
                if gpa:
                    total_checks += 1
                    try:
                        gpa_float = float(gpa)
                        if not (0.0 <= gpa_float <= 4.0):
                            failed_checks += 1
                    except ValueError:
                        failed_checks += 1
        
        if total_checks > 0:
            validity_score = (total_checks - failed_checks) / total_checks
        
        config = self.quality_dimensions['validity']
        status = 'pass' if validity_score >= config['threshold'] else 'warning' if validity_score >= 0.7 else 'fail'
        
        return QualityMetric(
            name='validity',
            value=validity_score,
            weight=config['weight'],
            description=config['description'],
            category='Data Validity',
            threshold=config['threshold'],
            status=status
        )
    
    def _assess_uniqueness(self, cv_data: Dict[str, Any]) -> QualityMetric:
        """Assess data uniqueness."""
        uniqueness_score = 1.0
        
        # Check for duplicate skills
        if 'skills' in cv_data:
            skill_names = [skill.get('name', '').lower() for skill in cv_data['skills'] if skill.get('name')]
            if skill_names:
                unique_ratio = len(set(skill_names)) / len(skill_names)
                uniqueness_score = min(uniqueness_score, unique_ratio)
        
        # Check for duplicate experiences (same company + overlapping dates)
        if 'experience' in cv_data:
            experiences = cv_data['experience']
            if len(experiences) > 1:
                # Simplified duplicate check
                companies = [exp.get('company', '').lower() for exp in experiences]
                if companies:
                    unique_companies = len(set(companies))
                    total_companies = len(companies)
                    # Allow some overlap for different roles at same company
                    if unique_companies < total_companies * 0.8:
                        uniqueness_score *= 0.9
        
        config = self.quality_dimensions['uniqueness']
        status = 'pass' if uniqueness_score >= config['threshold'] else 'warning' if uniqueness_score >= 0.8 else 'fail'
        
        return QualityMetric(
            name='uniqueness',
            value=uniqueness_score,
            weight=config['weight'],
            description=config['description'],
            category='Data Uniqueness',
            threshold=config['threshold'],
            status=status
        )
    
    def _assess_timeliness(self, cv_data: Dict[str, Any]) -> QualityMetric:
        """Assess data timeliness."""
        timeliness_score = 1.0
        current_year = datetime.now().year
        
        # Check if experience dates are recent
        if 'experience' in cv_data:
            recent_experience = False
            for exp in cv_data['experience']:
                end_date = exp.get('end_date', '')
                if end_date == 'present':
                    recent_experience = True
                    break
                elif end_date:
                    try:
                        if end_date.isdigit() and int(end_date) >= current_year - 2:
                            recent_experience = True
                            break
                    except ValueError:
                        pass
            
            if not recent_experience:
                timeliness_score *= 0.8
        
        # Check if education is reasonably recent for entry-level candidates
        if 'education' in cv_data:
            recent_education = False
            for edu in cv_data['education']:
                end_date = edu.get('end_date', '')
                if end_date:
                    try:
                        if end_date.isdigit() and int(end_date) >= current_year - 10:
                            recent_education = True
                            break
                    except ValueError:
                        pass
            
            if not recent_education:
                timeliness_score *= 0.9
        
        config = self.quality_dimensions['timeliness']
        status = 'pass' if timeliness_score >= config['threshold'] else 'warning' if timeliness_score >= 0.6 else 'fail'
        
        return QualityMetric(
            name='timeliness',
            value=timeliness_score,
            weight=config['weight'],
            description=config['description'],
            category='Data Timeliness',
            threshold=config['threshold'],
            status=status
        )
    
    def _assess_integrity(self, cv_data: Dict[str, Any]) -> QualityMetric:
        """Assess data integrity."""
        integrity_score = 1.0
        
        # Check referential integrity (all required sections present)
        required_sections = ['personal']
        for section in required_sections:
            if section not in cv_data:
                integrity_score *= 0.5
        
        # Check data structure integrity
        if 'experience' in cv_data and not isinstance(cv_data['experience'], list):
            integrity_score *= 0.8
        
        if 'education' in cv_data and not isinstance(cv_data['education'], list):
            integrity_score *= 0.8
        
        if 'skills' in cv_data and not isinstance(cv_data['skills'], list):
            integrity_score *= 0.8
        
        config = self.quality_dimensions['integrity']
        status = 'pass' if integrity_score >= config['threshold'] else 'warning' if integrity_score >= 0.8 else 'fail'
        
        return QualityMetric(
            name='integrity',
            value=integrity_score,
            weight=config['weight'],
            description=config['description'],
            category='Data Integrity',
            threshold=config['threshold'],
            status=status
        )
    
    def _is_valid_date_format(self, date_str: str) -> bool:
        """Check if date string is in valid format."""
        import re
        date_patterns = [
            r'^\d{4}-\d{2}-\d{2}$',  # YYYY-MM-DD
            r'^\d{4}-\d{2}$',        # YYYY-MM
            r'^\d{4}$',              # YYYY
            r'^[A-Za-z]+ \d{4}$',    # Month YYYY
        ]
        return any(re.match(pattern, date_str) for pattern in date_patterns)
    
    def _is_date_order_valid(self, start_date: str, end_date: str) -> bool:
        """Check if start date is before end date."""
        try:
            import re
            # Simple year comparison
            start_year = None
            end_year = None
            
            if re.match(r'^\d{4}', start_date):
                start_year = int(start_date[:4])
            
            if re.match(r'^\d{4}', end_date):
                end_year = int(end_date[:4])
            
            if start_year and end_year:
                return start_year <= end_year
        except ValueError:
            pass
        
        return True  # Assume valid if can't parse
    
    def _calculate_grade(self, score: float) -> str:
        """Calculate letter grade from score."""
        for threshold, grade in sorted(self.grading_scale.items(), reverse=True):
            if score >= threshold:
                return grade
        return 'F'
    
    def _generate_recommendations(self, metrics: List[QualityMetric]) -> List[str]:
        """Generate improvement recommendations."""
        recommendations = []
        
        for metric in metrics:
            if metric.status == 'fail':
                if metric.name == 'completeness':
                    recommendations.append("Add missing required fields to improve completeness")
                elif metric.name == 'accuracy':
                    recommendations.append("Verify and correct data formats (email, phone, dates)")
                elif metric.name == 'consistency':
                    recommendations.append("Review and fix inconsistent data across sections")
                elif metric.name == 'validity':
                    recommendations.append("Validate URLs, GPAs, and other formatted fields")
                elif metric.name == 'uniqueness':
                    recommendations.append("Remove duplicate entries and consolidate similar items")
                elif metric.name == 'timeliness':
                    recommendations.append("Update with recent experience and current information")
                elif metric.name == 'integrity':
                    recommendations.append("Fix data structure and ensure all required sections are present")
            
            elif metric.status == 'warning':
                recommendations.append(f"Consider improving {metric.name} to meet quality standards")
        
        return recommendations
    
    def _check_compliance(self, metrics: List[QualityMetric]) -> Dict[str, str]:
        """Check compliance with various frameworks."""
        overall_score = sum(metric.value * metric.weight for metric in metrics)
        
        compliance_status = {
            'DAMA-DMBOK': 'compliant' if overall_score >= 0.8 else 'non-compliant',
            'ISO_8000': 'compliant' if overall_score >= 0.85 else 'non-compliant',
            'Internal_Standards': 'compliant' if overall_score >= 0.75 else 'non-compliant'
        }
        
        return compliance_status


    def get_quality_trends(self, historical_reports: List[QualityReport]) -> Dict[str, Any]:
        """Analyze quality trends over time."""
        if not historical_reports:
            return {}

        trends = {}
        for dimension in self.quality_dimensions.keys():
            scores = []
            for report in historical_reports:
                for metric in report.metrics:
                    if metric.name == dimension:
                        scores.append(metric.value)
                        break

            if scores:
                trends[dimension] = {
                    'current': scores[-1] if scores else 0,
                    'average': statistics.mean(scores),
                    'trend': 'improving' if len(scores) > 1 and scores[-1] > scores[0] else 'declining' if len(scores) > 1 and scores[-1] < scores[0] else 'stable',
                    'volatility': statistics.stdev(scores) if len(scores) > 1 else 0
                }

        return trends


# Global data quality service instance
data_quality_service = DataQualityService()
