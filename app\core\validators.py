"""
Data Validation Service
Comprehensive validation with business rules and quality checks
"""

import logging
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator

logger = logging.getLogger(__name__)


class ValidationError(BaseModel):
    """Validation error model."""
    
    field: str = Field(..., description="Field that failed validation")
    error_type: str = Field(..., description="Type of validation error")
    message: str = Field(..., description="Human-readable error message")
    severity: str = Field(..., description="Error severity: error, warning, info")
    suggested_fix: Optional[str] = Field(None, description="Suggested fix for the error")


class ValidationResult(BaseModel):
    """Validation result model."""
    
    is_valid: bool = Field(..., description="Whether data passed validation")
    errors: List[ValidationError] = Field(default_factory=list, description="Validation errors")
    warnings: List[ValidationError] = Field(default_factory=list, description="Validation warnings")
    score: float = Field(..., description="Overall quality score (0-1)")
    validated_data: Dict[str, Any] = Field(..., description="Validated and cleaned data")


class DataValidationError(Exception):
    """Custom exception for data validation errors."""
    
    def __init__(self, message: str, error_code: str = "VALIDATION_ERROR", details: Optional[Dict] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DataValidator:
    """
    Enterprise data validation service with business rules.
    
    Features:
    - CV data validation
    - Business rule enforcement
    - Quality scoring
    - Format validation
    - Completeness checks
    """
    
    def __init__(self):
        """Initialize data validator."""
        
        # Email validation pattern
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        
        # Phone validation pattern
        self.phone_pattern = re.compile(r'^[\+]?[1-9]?[\d\s\-\(\)\.]{7,15}$')
        
        # URL validation pattern
        self.url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE
        )
        
        # Required fields for different CV sections
        self.required_fields = {
            'personal': ['full_name'],
            'experience': ['job_title', 'company'],
            'education': ['degree', 'institution'],
            'skills': ['name'],
            'certifications': ['name'],
            'projects': ['name']
        }
        
        # Business rules
        self.business_rules = {
            'min_experience_duration_months': 1,
            'max_experience_duration_years': 50,
            'min_education_duration_months': 6,
            'max_education_duration_years': 10,
            'max_skills_count': 50,
            'min_summary_words': 10,
            'max_summary_words': 200,
            'max_description_words': 500,
        }
    
    def validate_cv_data(self, cv_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate complete CV data structure.
        
        Args:
            cv_data: CV data dictionary
            
        Returns:
            ValidationResult: Validation results with errors and score
        """
        try:
            errors = []
            warnings = []
            validated_data = cv_data.copy()
            
            # Validate personal information
            if 'personal' in cv_data:
                personal_errors, personal_warnings = self._validate_personal_info(cv_data['personal'])
                errors.extend(personal_errors)
                warnings.extend(personal_warnings)
            else:
                errors.append(ValidationError(
                    field='personal',
                    error_type='missing_section',
                    message='Personal information section is required',
                    severity='error',
                    suggested_fix='Add personal information section with at least full name'
                ))
            
            # Validate experience
            if 'experience' in cv_data:
                exp_errors, exp_warnings = self._validate_experience(cv_data['experience'])
                errors.extend(exp_errors)
                warnings.extend(exp_warnings)
            
            # Validate education
            if 'education' in cv_data:
                edu_errors, edu_warnings = self._validate_education(cv_data['education'])
                errors.extend(edu_errors)
                warnings.extend(edu_warnings)
            
            # Validate skills
            if 'skills' in cv_data:
                skill_errors, skill_warnings = self._validate_skills(cv_data['skills'])
                errors.extend(skill_errors)
                warnings.extend(skill_warnings)
            
            # Validate summary
            if 'summary' in cv_data:
                summary_errors, summary_warnings = self._validate_summary(cv_data['summary'])
                errors.extend(summary_errors)
                warnings.extend(summary_warnings)
            
            # Validate certifications
            if 'certifications' in cv_data:
                cert_errors, cert_warnings = self._validate_certifications(cv_data['certifications'])
                errors.extend(cert_errors)
                warnings.extend(cert_warnings)
            
            # Validate projects
            if 'projects' in cv_data:
                proj_errors, proj_warnings = self._validate_projects(cv_data['projects'])
                errors.extend(proj_errors)
                warnings.extend(proj_warnings)
            
            # Calculate quality score
            score = self._calculate_quality_score(cv_data, errors, warnings)
            
            # Determine if data is valid
            is_valid = len([e for e in errors if e.severity == 'error']) == 0
            
            return ValidationResult(
                is_valid=is_valid,
                errors=errors,
                warnings=warnings,
                score=score,
                validated_data=validated_data
            )
            
        except Exception as e:
            logger.error(f"CV data validation failed: {e}")
            raise DataValidationError(
                f"CV data validation failed: {str(e)}",
                error_code="VALIDATION_FAILED"
            )
    
    def _validate_personal_info(self, personal_data: Dict[str, Any]) -> tuple[List[ValidationError], List[ValidationError]]:
        """Validate personal information section."""
        errors = []
        warnings = []
        
        # Check required fields
        if not personal_data.get('full_name'):
            errors.append(ValidationError(
                field='personal.full_name',
                error_type='required_field',
                message='Full name is required',
                severity='error',
                suggested_fix='Provide a full name'
            ))
        
        # Validate email
        email = personal_data.get('email')
        if email:
            if not self.email_pattern.match(email):
                errors.append(ValidationError(
                    field='personal.email',
                    error_type='invalid_format',
                    message='Invalid email format',
                    severity='error',
                    suggested_fix='Provide a valid email address'
                ))
        else:
            warnings.append(ValidationError(
                field='personal.email',
                error_type='missing_recommended',
                message='Email address is recommended',
                severity='warning',
                suggested_fix='Add email address for better contact options'
            ))
        
        # Validate phone
        phone = personal_data.get('phone')
        if phone:
            if not self.phone_pattern.match(phone):
                warnings.append(ValidationError(
                    field='personal.phone',
                    error_type='invalid_format',
                    message='Phone number format may be invalid',
                    severity='warning',
                    suggested_fix='Use standard phone number format'
                ))
        
        # Validate URLs
        for url_field in ['linkedin', 'github', 'website', 'portfolio']:
            url = personal_data.get(url_field)
            if url and not self.url_pattern.match(url):
                warnings.append(ValidationError(
                    field=f'personal.{url_field}',
                    error_type='invalid_format',
                    message=f'Invalid {url_field} URL format',
                    severity='warning',
                    suggested_fix='Provide a valid URL starting with http:// or https://'
                ))
        
        return errors, warnings
    
    def _validate_experience(self, experience_data: List[Dict[str, Any]]) -> tuple[List[ValidationError], List[ValidationError]]:
        """Validate work experience section."""
        errors = []
        warnings = []
        
        if not experience_data:
            warnings.append(ValidationError(
                field='experience',
                error_type='empty_section',
                message='No work experience provided',
                severity='warning',
                suggested_fix='Add at least one work experience entry'
            ))
            return errors, warnings
        
        for i, exp in enumerate(experience_data):
            # Check required fields
            if not exp.get('job_title'):
                errors.append(ValidationError(
                    field=f'experience[{i}].job_title',
                    error_type='required_field',
                    message='Job title is required',
                    severity='error',
                    suggested_fix='Provide a job title'
                ))
            
            if not exp.get('company'):
                errors.append(ValidationError(
                    field=f'experience[{i}].company',
                    error_type='required_field',
                    message='Company name is required',
                    severity='error',
                    suggested_fix='Provide a company name'
                ))
            
            # Validate dates
            start_date = exp.get('start_date')
            end_date = exp.get('end_date')
            
            if start_date:
                if not self._is_valid_date(start_date):
                    warnings.append(ValidationError(
                        field=f'experience[{i}].start_date',
                        error_type='invalid_date',
                        message='Invalid start date format',
                        severity='warning',
                        suggested_fix='Use YYYY-MM-DD format or "Month YYYY"'
                    ))
                elif self._is_future_date(start_date):
                    warnings.append(ValidationError(
                        field=f'experience[{i}].start_date',
                        error_type='future_date',
                        message='Start date is in the future',
                        severity='warning',
                        suggested_fix='Check the start date'
                    ))
            
            if end_date and end_date != 'present':
                if not self._is_valid_date(end_date):
                    warnings.append(ValidationError(
                        field=f'experience[{i}].end_date',
                        error_type='invalid_date',
                        message='Invalid end date format',
                        severity='warning',
                        suggested_fix='Use YYYY-MM-DD format, "Month YYYY", or "present"'
                    ))
            
            # Validate duration
            if start_date and end_date and end_date != 'present':
                duration_valid, duration_msg = self._validate_duration(
                    start_date, end_date, 
                    self.business_rules['min_experience_duration_months'],
                    self.business_rules['max_experience_duration_years'] * 12
                )
                if not duration_valid:
                    warnings.append(ValidationError(
                        field=f'experience[{i}].duration',
                        error_type='invalid_duration',
                        message=duration_msg,
                        severity='warning',
                        suggested_fix='Check the start and end dates'
                    ))
            
            # Check description length
            description = exp.get('description', '')
            if description:
                word_count = len(description.split())
                if word_count > self.business_rules['max_description_words']:
                    warnings.append(ValidationError(
                        field=f'experience[{i}].description',
                        error_type='too_long',
                        message=f'Description is too long ({word_count} words, max {self.business_rules["max_description_words"]})',
                        severity='warning',
                        suggested_fix='Shorten the description to focus on key achievements'
                    ))
        
        return errors, warnings
    
    def _validate_education(self, education_data: List[Dict[str, Any]]) -> tuple[List[ValidationError], List[ValidationError]]:
        """Validate education section."""
        errors = []
        warnings = []
        
        if not education_data:
            warnings.append(ValidationError(
                field='education',
                error_type='empty_section',
                message='No education provided',
                severity='warning',
                suggested_fix='Add at least one education entry'
            ))
            return errors, warnings
        
        for i, edu in enumerate(education_data):
            # Check required fields
            if not edu.get('degree'):
                errors.append(ValidationError(
                    field=f'education[{i}].degree',
                    error_type='required_field',
                    message='Degree is required',
                    severity='error',
                    suggested_fix='Provide a degree or qualification'
                ))
            
            if not edu.get('institution'):
                errors.append(ValidationError(
                    field=f'education[{i}].institution',
                    error_type='required_field',
                    message='Institution is required',
                    severity='error',
                    suggested_fix='Provide an institution name'
                ))
            
            # Validate dates
            start_date = edu.get('start_date')
            end_date = edu.get('end_date')
            
            if start_date and not self._is_valid_date(start_date):
                warnings.append(ValidationError(
                    field=f'education[{i}].start_date',
                    error_type='invalid_date',
                    message='Invalid start date format',
                    severity='warning',
                    suggested_fix='Use YYYY-MM-DD format or "Month YYYY"'
                ))
            
            if end_date and end_date != 'present' and not self._is_valid_date(end_date):
                warnings.append(ValidationError(
                    field=f'education[{i}].end_date',
                    error_type='invalid_date',
                    message='Invalid end date format',
                    severity='warning',
                    suggested_fix='Use YYYY-MM-DD format, "Month YYYY", or "present"'
                ))
            
            # Validate GPA
            gpa = edu.get('gpa')
            if gpa:
                try:
                    gpa_float = float(gpa)
                    if not (0.0 <= gpa_float <= 4.0):
                        warnings.append(ValidationError(
                            field=f'education[{i}].gpa',
                            error_type='invalid_range',
                            message='GPA should be between 0.0 and 4.0',
                            severity='warning',
                            suggested_fix='Provide GPA in 4.0 scale or remove if different scale'
                        ))
                except ValueError:
                    warnings.append(ValidationError(
                        field=f'education[{i}].gpa',
                        error_type='invalid_format',
                        message='GPA should be a number',
                        severity='warning',
                        suggested_fix='Provide GPA as a decimal number'
                    ))
        
        return errors, warnings
    
    def _validate_skills(self, skills_data: List[Dict[str, Any]]) -> tuple[List[ValidationError], List[ValidationError]]:
        """Validate skills section."""
        errors = []
        warnings = []
        
        if not skills_data:
            warnings.append(ValidationError(
                field='skills',
                error_type='empty_section',
                message='No skills provided',
                severity='warning',
                suggested_fix='Add relevant skills'
            ))
            return errors, warnings
        
        if len(skills_data) > self.business_rules['max_skills_count']:
            warnings.append(ValidationError(
                field='skills',
                error_type='too_many',
                message=f'Too many skills ({len(skills_data)}, max {self.business_rules["max_skills_count"]})',
                severity='warning',
                suggested_fix='Focus on most relevant skills'
            ))
        
        skill_names = []
        for i, skill in enumerate(skills_data):
            if not skill.get('name'):
                errors.append(ValidationError(
                    field=f'skills[{i}].name',
                    error_type='required_field',
                    message='Skill name is required',
                    severity='error',
                    suggested_fix='Provide a skill name'
                ))
            else:
                skill_name = skill['name'].lower()
                if skill_name in skill_names:
                    warnings.append(ValidationError(
                        field=f'skills[{i}].name',
                        error_type='duplicate',
                        message='Duplicate skill found',
                        severity='warning',
                        suggested_fix='Remove duplicate skills'
                    ))
                skill_names.append(skill_name)
        
        return errors, warnings
    
    def _validate_summary(self, summary: str) -> tuple[List[ValidationError], List[ValidationError]]:
        """Validate summary/objective section."""
        errors = []
        warnings = []
        
        if not summary or not summary.strip():
            warnings.append(ValidationError(
                field='summary',
                error_type='empty_field',
                message='Summary is empty',
                severity='warning',
                suggested_fix='Add a professional summary'
            ))
            return errors, warnings
        
        word_count = len(summary.split())
        
        if word_count < self.business_rules['min_summary_words']:
            warnings.append(ValidationError(
                field='summary',
                error_type='too_short',
                message=f'Summary is too short ({word_count} words, min {self.business_rules["min_summary_words"]})',
                severity='warning',
                suggested_fix='Expand the summary to better describe your background'
            ))
        
        if word_count > self.business_rules['max_summary_words']:
            warnings.append(ValidationError(
                field='summary',
                error_type='too_long',
                message=f'Summary is too long ({word_count} words, max {self.business_rules["max_summary_words"]})',
                severity='warning',
                suggested_fix='Shorten the summary to be more concise'
            ))
        
        return errors, warnings
    
    def _validate_certifications(self, cert_data: List[Dict[str, Any]]) -> tuple[List[ValidationError], List[ValidationError]]:
        """Validate certifications section."""
        errors = []
        warnings = []
        
        for i, cert in enumerate(cert_data):
            if not cert.get('name'):
                errors.append(ValidationError(
                    field=f'certifications[{i}].name',
                    error_type='required_field',
                    message='Certification name is required',
                    severity='error',
                    suggested_fix='Provide certification name'
                ))
        
        return errors, warnings
    
    def _validate_projects(self, project_data: List[Dict[str, Any]]) -> tuple[List[ValidationError], List[ValidationError]]:
        """Validate projects section."""
        errors = []
        warnings = []
        
        for i, project in enumerate(project_data):
            if not project.get('name'):
                errors.append(ValidationError(
                    field=f'projects[{i}].name',
                    error_type='required_field',
                    message='Project name is required',
                    severity='error',
                    suggested_fix='Provide project name'
                ))
            
            # Validate project URL
            url = project.get('url')
            if url and not self.url_pattern.match(url):
                warnings.append(ValidationError(
                    field=f'projects[{i}].url',
                    error_type='invalid_format',
                    message='Invalid project URL format',
                    severity='warning',
                    suggested_fix='Provide a valid URL'
                ))
        
        return errors, warnings
    
    def _is_valid_date(self, date_str: str) -> bool:
        """Check if date string is in valid format."""
        if not date_str:
            return False
        
        date_patterns = [
            r'^\d{4}-\d{2}-\d{2}$',  # YYYY-MM-DD
            r'^\d{4}-\d{2}$',        # YYYY-MM
            r'^\d{4}$',              # YYYY
            r'^[A-Za-z]+ \d{4}$',    # Month YYYY
        ]
        
        return any(re.match(pattern, date_str) for pattern in date_patterns)
    
    def _is_future_date(self, date_str: str) -> bool:
        """Check if date is in the future."""
        try:
            if re.match(r'^\d{4}$', date_str):
                year = int(date_str)
                return year > datetime.now().year
            elif re.match(r'^\d{4}-\d{2}$', date_str):
                year, month = map(int, date_str.split('-'))
                date_obj = datetime(year, month, 1)
                return date_obj > datetime.now()
            elif re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
                year, month, day = map(int, date_str.split('-'))
                date_obj = datetime(year, month, day)
                return date_obj > datetime.now()
        except ValueError:
            pass
        
        return False
    
    def _validate_duration(self, start_date: str, end_date: str, min_months: int, max_months: int) -> tuple[bool, str]:
        """Validate duration between dates."""
        try:
            # Simple validation - can be enhanced
            if re.match(r'^\d{4}$', start_date) and re.match(r'^\d{4}$', end_date):
                start_year = int(start_date)
                end_year = int(end_date)
                duration_months = (end_year - start_year) * 12
                
                if duration_months < min_months:
                    return False, f"Duration too short (minimum {min_months} months)"
                if duration_months > max_months:
                    return False, f"Duration too long (maximum {max_months} months)"
        except ValueError:
            pass
        
        return True, ""
    
    def _calculate_quality_score(
        self,
        cv_data: Dict[str, Any],
        errors: List[ValidationError],
        warnings: List[ValidationError]
    ) -> float:
        """Calculate overall CV quality score."""
        base_score = 1.0
        
        # Deduct for errors
        error_penalty = len([e for e in errors if e.severity == 'error']) * 0.1
        warning_penalty = len([e for e in warnings if e.severity == 'warning']) * 0.05
        
        # Bonus for completeness
        completeness_bonus = 0.0
        sections = ['personal', 'experience', 'education', 'skills', 'summary']
        present_sections = sum(1 for section in sections if section in cv_data and cv_data[section])
        completeness_bonus = (present_sections / len(sections)) * 0.2
        
        # Bonus for rich content
        content_bonus = 0.0
        if 'experience' in cv_data and len(cv_data['experience']) >= 2:
            content_bonus += 0.1
        if 'skills' in cv_data and len(cv_data['skills']) >= 5:
            content_bonus += 0.1
        if 'certifications' in cv_data and cv_data['certifications']:
            content_bonus += 0.05
        if 'projects' in cv_data and cv_data['projects']:
            content_bonus += 0.05
        
        final_score = base_score - error_penalty - warning_penalty + completeness_bonus + content_bonus
        return max(0.0, min(1.0, final_score))


# Global validator instance
data_validator = DataValidator()
