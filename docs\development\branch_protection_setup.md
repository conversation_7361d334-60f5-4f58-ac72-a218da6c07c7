# 🛡️ Branch Protection Rules Setup

This document provides step-by-step instructions for setting up branch protection rules in the ImpactCV GitHub repository to enforce code review requirements and maintain code quality.

## 📋 **Overview**

Branch protection rules ensure that:
- All changes go through proper code review
- Automated tests pass before merging
- Security scans are completed
- Code quality standards are maintained
- No direct pushes to protected branches

---

## 🔧 **Setup Instructions**

### **Step 1: Access Repository Settings**

1. Navigate to your GitHub repository: `https://github.com/dasotillop/ImpactCV`
2. Click on **Settings** tab
3. In the left sidebar, click **Branches**

### **Step 2: Add Branch Protection Rule for `main`**

1. Click **Add rule** button
2. Configure the following settings:

#### **Branch Name Pattern**
```
main
```

#### **Protect Matching Branches**
- ✅ **Require a pull request before merging**
  - ✅ **Require approvals**: `1` (minimum)
  - ✅ **Dismiss stale PR approvals when new commits are pushed**
  - ✅ **Require review from code owners** (if CODEOWNERS file exists)
  - ✅ **Restrict pushes that create files larger than 100MB**

- ✅ **Require status checks to pass before merging**
  - ✅ **Require branches to be up to date before merging**
  - **Required status checks:**
    - `🔍 Code Quality & Security Scan`
    - `🛡️ Dependency Security Scan`
    - `🧪 Testing Suite`
    - `🐳 Container Security Scan`
    - `📊 Compliance Report`

- ✅ **Require conversation resolution before merging**

- ✅ **Require signed commits**

- ✅ **Require linear history**

- ✅ **Require deployments to succeed before merging**
  - **Required deployment environments:**
    - `staging`

#### **Restrict Pushes**
- ✅ **Restrict pushes that create files larger than 100MB**

#### **Rules Applied to Everyone**
- ✅ **Include administrators**
- ✅ **Allow force pushes**: ❌ (disabled)
- ✅ **Allow deletions**: ❌ (disabled)

### **Step 3: Add Branch Protection Rule for `develop`**

1. Click **Add rule** button again
2. Configure the following settings:

#### **Branch Name Pattern**
```
develop
```

#### **Protect Matching Branches**
- ✅ **Require a pull request before merging**
  - ✅ **Require approvals**: `1` (minimum)
  - ✅ **Dismiss stale PR approvals when new commits are pushed**
  - ✅ **Require review from code owners**

- ✅ **Require status checks to pass before merging**
  - ✅ **Require branches to be up to date before merging**
  - **Required status checks:**
    - `🔍 Code Quality & Security Scan`
    - `🛡️ Dependency Security Scan`
    - `🧪 Testing Suite`
    - `🐳 Container Security Scan`

- ✅ **Require conversation resolution before merging**

- ✅ **Require signed commits**

#### **Rules Applied to Everyone**
- ✅ **Include administrators**
- ✅ **Allow force pushes**: ❌ (disabled)
- ✅ **Allow deletions**: ❌ (disabled)

---

## 👥 **Code Owners Setup**

### **Step 1: Create CODEOWNERS File**

Create `.github/CODEOWNERS` file with the following content:

```bash
# ImpactCV Code Owners
# Global ownership
* @dasotillop

# Security-related files
.github/workflows/security-*.yml @dasotillop
.github/workflows/dependency-*.yml @dasotillop
scripts/security/ @dasotillop
.bandit @dasotillop
.semgrep.yml @dasotillop

# Infrastructure and deployment
terraform/ @dasotillop
docker-compose*.yml @dasotillop
Dockerfile* @dasotillop
.github/workflows/ci-cd-*.yml @dasotillop

# Core application
app/ @dasotillop
requirements*.txt @dasotillop

# Documentation
docs/ @dasotillop
README.md @dasotillop
CONTRIBUTING.md @dasotillop
```

### **Step 2: Configure Team Permissions**

1. Go to **Settings** → **Manage access**
2. Configure the following permissions:

#### **Repository Permissions**
- **dasotillop**: `Admin` (owner)
- **Contributors**: `Write` (when added)
- **Reviewers**: `Triage` (when added)

---

## 🔒 **Security Settings**

### **Step 1: Enable Security Features**

1. Go to **Settings** → **Security & analysis**
2. Enable the following features:

#### **Dependency Graph**
- ✅ **Enable dependency graph**

#### **Dependabot**
- ✅ **Enable Dependabot alerts**
- ✅ **Enable Dependabot security updates**

#### **Code Scanning**
- ✅ **Enable CodeQL analysis**
- ✅ **Enable third-party code scanning tools**

#### **Secret Scanning**
- ✅ **Enable secret scanning**
- ✅ **Enable push protection**

### **Step 2: Configure Security Policies**

1. Create `.github/SECURITY.md` file (if not exists)
2. Configure vulnerability reporting process
3. Set up security advisory process

---

## 📊 **Required Status Checks**

The following GitHub Actions workflows must pass before merging:

### **Main Branch Requirements**
1. **🔍 Code Quality & Security Scan**
   - Bandit security scan
   - Semgrep SAST analysis
   - Code formatting (Black, isort)
   - Linting (flake8, mypy)

2. **🛡️ Dependency Security Scan**
   - Safety vulnerability check
   - Pip-audit analysis
   - License compliance check
   - SBOM generation

3. **🧪 Testing Suite**
   - Unit tests (>90% coverage)
   - Integration tests
   - Security tests
   - Performance tests

4. **🐳 Container Security Scan**
   - Trivy container scan
   - Docker Scout analysis
   - Dockerfile security check

5. **📊 Compliance Report**
   - OWASP compliance validation
   - GDPR compliance check
   - Security metrics generation

### **Develop Branch Requirements**
- Same as main branch except deployment requirements

---

## 🔄 **Workflow Integration**

### **GitHub Actions Configuration**

Ensure your workflows include the following job names that match the required status checks:

```yaml
# .github/workflows/ci-cd-pipeline.yml
jobs:
  code-quality:
    name: 🔍 Code Quality & Security Scan
    # ... job configuration

  dependency-security:
    name: 🛡️ Dependency Security Scan
    # ... job configuration

  testing:
    name: 🧪 Testing Suite
    # ... job configuration

  container-security:
    name: 🐳 Container Security Scan
    # ... job configuration

  compliance-report:
    name: 📊 Compliance Report
    # ... job configuration
```

---

## 🚀 **Deployment Protection**

### **Environment Protection Rules**

1. Go to **Settings** → **Environments**
2. Create the following environments:

#### **Staging Environment**
- **Protection rules:**
  - ✅ **Required reviewers**: `dasotillop`
  - ✅ **Wait timer**: `0` minutes
  - ✅ **Deployment branches**: `develop` only

#### **Production Environment**
- **Protection rules:**
  - ✅ **Required reviewers**: `dasotillop`
  - ✅ **Wait timer**: `5` minutes
  - ✅ **Deployment branches**: `main` only

---

## 📋 **Verification Checklist**

After setting up branch protection rules, verify:

- [ ] ✅ Cannot push directly to `main` branch
- [ ] ✅ Cannot push directly to `develop` branch
- [ ] ✅ Pull requests require approval
- [ ] ✅ Status checks must pass before merging
- [ ] ✅ Conversations must be resolved
- [ ] ✅ Branches must be up to date
- [ ] ✅ Force pushes are disabled
- [ ] ✅ Branch deletions are disabled
- [ ] ✅ Large files are blocked
- [ ] ✅ Secret scanning is active
- [ ] ✅ Dependabot is enabled

---

## 🔧 **Testing the Setup**

### **Test Pull Request Process**

1. Create a feature branch:
   ```bash
   git checkout develop
   git checkout -b feature/test-branch-protection
   ```

2. Make a small change and push:
   ```bash
   echo "# Test" >> test.md
   git add test.md
   git commit -m "test: verify branch protection rules"
   git push origin feature/test-branch-protection
   ```

3. Create a pull request to `develop`
4. Verify that:
   - Status checks are required
   - Approval is required
   - Cannot merge until checks pass

### **Test Direct Push Prevention**

1. Try to push directly to main:
   ```bash
   git checkout main
   echo "# Direct push test" >> test-direct.md
   git add test-direct.md
   git commit -m "test: direct push"
   git push origin main
   ```

2. Verify that the push is rejected

---

## 📞 **Support**

If you encounter issues setting up branch protection rules:

1. Check GitHub documentation: [About protected branches](https://docs.github.com/en/repositories/configuring-branches-and-merges-in-your-repository/defining-the-mergeability-of-pull-requests/about-protected-branches)
2. Verify workflow names match required status checks
3. Ensure you have admin permissions on the repository
4. Contact repository maintainer: <EMAIL>

---

## 🔄 **Maintenance**

### **Regular Reviews**
- **Monthly**: Review and update required status checks
- **Quarterly**: Audit branch protection effectiveness
- **Annually**: Review and update code owner assignments

### **Updates**
- Update required status checks when new workflows are added
- Adjust approval requirements based on team size
- Review and update deployment protection rules
